package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午5:19
 */
@Setter
@Getter
@Entity
@Table(name = "m_region",
        indexes = {
                @Index(name = "addressCode", columnList = "addressCode"),
                @Index(name = "name", columnList = "name"),
                @Index(name = "nameCode", columnList = "nameCode"),
                @Index(name = "level", columnList = "level")
        })
@TableName("m_region")
public class Region {
    @Id
    @TableId
    private String id;
    private String parentCode;
    private String cityCode;
    private String addressCode;
    private String name;
    private String nameEn;
    private String nameCode;
    private String center;
    private String level;
}
