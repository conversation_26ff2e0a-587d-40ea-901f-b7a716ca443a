package com.endovas.cps.service;

import com.endovas.cps.pojo.vo.AddressVO;
import com.endovas.cps.pojo.vo.RegionListVO;
import com.endovas.cps.pojo.vo.RegionVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午2:27
 */
public interface RegionService {

    List<RegionVO> level3();
      List<RegionVO> level2();
      String getNameByAddressCode(String addressCode);
      List<RegionListVO> findByLevel(String level, String parentCode);
      AddressVO parseAddress(String address, String idCardNo);
      void regeneration();





}
