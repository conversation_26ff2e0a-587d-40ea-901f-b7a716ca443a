package com.endovas.cps.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.endovas.cps.dao.RegionDAO;
import com.endovas.cps.entity.Region;
import com.endovas.cps.enums.RegionEnum;
import com.endovas.cps.pojo.vo.AddressVO;
import com.endovas.cps.pojo.vo.RegionListVO;
import com.endovas.cps.pojo.vo.RegionVO;
import com.endovas.cps.service.RegionService;
import io.daige.starter.common.cache.RedisCacheExpired;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RegionServiceImpl implements RegionService {
    private static List<String> ZHIXIASHI = Arrays.asList("重庆市", "北京市", "上海市", "天津市");
    private final RedisHelper redisHelper;
    private final RegionDAO regionDAO;
    private final String PROVINCE = "province";
    private final String CITY = "city";
    private final String DISTRICT = "district";
    private final String COUNTY = "county";
    private final String splitAddress =
            "(?<province>[^省]+省|[^自治区]+自治区|.+市)(?<city>[^自治州]+自治州|.+区划|[^市]+市|.+区)?(?<county>[^市]+市|[^县]+县|[^旗]+旗|.+区)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
    private final Pattern pattern = Pattern.compile(splitAddress);

    @Override
    public String getNameByAddressCode(String addressCode) {
        if (StrUtil.isNotEmpty(addressCode) && redisHelper.hshHasKey(RedisCacheKeys.getDistrictCodeName(), addressCode)) {
            return redisHelper.hshGet(RedisCacheKeys.getDistrictCodeName(), addressCode);
        }
        Region district = regionDAO.getByAddressCode(addressCode);
        if (Objects.nonNull(district)) {
            return district.getName();
        }
        return StringUtils.EMPTY;
    }

    @Override
    public List<RegionListVO> findByLevel(String level, String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return regionDAO.findByLevelOrderByAddressCodeAsc(level).stream().map(x -> new RegionListVO().convertFrom(x)).collect(Collectors.toList());
        } else {
            return regionDAO.findByLevelAndParentCodeOrderByAddressCodeAsc(level, parentCode).stream().map(x -> new RegionListVO().convertFrom(x)).collect(Collectors.toList());
        }
    }

    @Override
    public List<RegionVO> level3() {
        //读取缓存
        List<RegionVO> regionVOS = redisHelper.listGet(RedisCacheKeys.getProvinceCityDistrictCodeKey(), RegionVO.class);
        if (CollectionUtils.isEmpty(regionVOS)) {
            List<Region> regions = regionDAO.findByLevelIn(RegionEnum.level3());
            List<Region> streets = regionDAO.findByLevelOrderByAddressCodeAsc(RegionEnum.street.name());
            regionVOS = regions.stream().filter(x -> x.getLevel().equals(RegionEnum.province.name())).sorted(Comparator.comparing(Region::getAddressCode)).map(RegionVO.level3(regions)).collect(Collectors.toList());
            alternateStreet(regionVOS, streets);
            redisHelper.objectSet(RedisCacheKeys.getProvinceCityDistrictCodeKey(), regionVOS, 365, TimeUnit.DAYS);
            Map<String, String> codeNameMap = new HashMap<>();
            for (Region d : regions) {
                codeNameMap.put(d.getAddressCode(), d.getName());
            }
            if (CollectionUtils.isNotEmpty(streets)) {
                Set<String> cityCode = regions.stream().filter(x -> x.getLevel().equals(RegionEnum.city.name))
                        .map(Region::getAddressCode).collect(Collectors.toSet());
                streets.stream().filter(x -> cityCode.contains(x.getParentCode()))
                        .forEach(x -> codeNameMap.put(x.getAddressCode(), x.getName()));
            }

            redisHelper.hshPutAll(RedisCacheKeys.getDistrictCodeName(), codeNameMap);
        }
        return regionVOS;
    }

    private void alternateStreet(List<RegionVO> vos, List<Region> streets) {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        vos.forEach(vo -> {
            if (CollectionUtils.isEmpty(vo.getChildren())) {
                if (vo.getLevel().equals(RegionEnum.city.name())) {
                    List<Region> districts = streets.stream().filter(x -> x.getParentCode().equals(vo.getValue())).collect(Collectors.toList());
                    List<RegionVO> stvos = districts.stream().map(RegionVO.entityToVO).collect(Collectors.toList());
                    vo.setChildren(stvos);
                }
            } else {
                alternateStreet(vo.getChildren(), streets);
            }
        });
    }

    @Override
    public List<RegionVO> level2() {
        List<RegionVO> vos = redisHelper.listGet(RedisCacheKeys.getProvinceCityCodeKey(), RegionVO.class);
        if (CollectionUtils.isEmpty(vos)) {
            List<Region> regions = regionDAO.findByLevelIn(RegionEnum.level2());
            vos = regions.stream().filter(x -> x.getLevel().equals(RegionEnum.province.name())).sorted(Comparator.comparing(Region::getAddressCode)).map(RegionVO.level3(regions)).collect(Collectors.toList());
            redisHelper.objectSet(RedisCacheKeys.getProvinceCityCodeKey(), vos, RedisCacheExpired.oneYear, null);
        }
        return vos;
    }

    @Override
    public AddressVO parseAddress(String address, String idCardNo) {
        if (StrUtil.isEmpty(address) && StrUtil.isEmpty(idCardNo)) {
            return null;
        }

        if (address.contains("囗")) {
            address = address.replaceAll("囗", "口");
        }

        if (address.startsWith("广西")) {
            address = "广西壮族自治区".concat(address.substring(2));
        }
        if (address.startsWith("内蒙古")) {
            address = "内蒙古自治区".concat(address.substring(3));
        }
        if (address.startsWith("西藏")) {
            address = "西藏自治区".concat(address.substring(2));
        }
        if (address.startsWith("宁夏")) {
            address = "宁夏回族自治区".concat(address.substring(2));
        }
        if (address.startsWith("新疆")) {
            address = "新疆维吾尔自治区".concat(address.substring(2));
        }

        AddressVO addressVO = new AddressVO();
        Matcher m = pattern.matcher(address);
        if (m.find()) {
            String provinceCN = m.group(PROVINCE);
            String cityCN = m.group(CITY);
            String areaCN = m.group(COUNTY);
            Region province = regionDAO.getByNameAndLevel(provinceCN, PROVINCE);
            //没带省直接就是xx市
            if (Objects.isNull(province)) {
                //会被province捕获
                Region city = regionDAO.getByNameAndLevel(provinceCN, CITY);
                if (Objects.nonNull(city)) {
                    addressVO.setCityCode(city.getAddressCode());
                    addressVO.setProvinceCode(city.getParentCode());
                    List<Region> areas = regionDAO.findAllByNameAndLevel(cityCN, DISTRICT);
                    if (CollectionUtils.isNotEmpty(areas)) {
                        areas.forEach(qu -> {
                            if (qu.getParentCode().equals(city.getAddressCode())) {
                                addressVO.setAreaCode(qu.getAddressCode());
                            }
                        });
                    }
                }
            } else {
                addressVO.setProvinceCode(province.getAddressCode());
                if (ZHIXIASHI.contains(province.getName())) {
                    if (StrUtil.isNotBlank(cityCN)) {
                        //这里主要是数据库里市和区中间有个分类层
                        List<Region> districts = regionDAO.findAllByNameAndLevel(cityCN, DISTRICT);
                        Region area = null;
                        for (Region district : districts) {
                            //东莞和中山的addressCode是一样的，但广东不是直辖市，这里可以忽略
                            Region pp = regionDAO.getByAddressCode(district.getParentCode());
                            if (pp.getParentCode().equals(province.getAddressCode())) {
                                area = district;
                            }
                        }
                        if (Objects.nonNull(area)) {
                            addressVO.setAreaCode(area.getAddressCode());
                            Region city = regionDAO.getByAddressCode(area.getParentCode());
                            if (Objects.nonNull(city)) {
                                addressVO.setCityCode(city.getAddressCode());
                            }
                        }

                    }
                }
            }

            if (StrUtil.isBlank(addressVO.getCityCode())) {
                if (StrUtil.isBlank(cityCN)) {
                    if (StrUtil.isNotBlank(areaCN)) {
                        List<Region> areas = regionDAO.findAllByNameAndLevel(areaCN, DISTRICT);
                        if (CollectionUtils.isNotEmpty(areas) && StrUtil.isNotBlank(addressVO.getProvinceCode())) {
                            areas.forEach(area -> {
                                Region city = regionDAO.getByAddressCodeAndLevel(area.getParentCode(), CITY);
                                if (city.getParentCode().equals(addressVO.getProvinceCode())) {
                                    addressVO.setCityCode(city.getAddressCode());
                                    addressVO.setAreaCode(area.getAddressCode());
                                }

                            });
                        }

                    }

                } else {
                    Region city = regionDAO.getByNameAndLevel(cityCN, CITY);
                    if (Objects.nonNull(city)) {
                        addressVO.setCityCode(city.getAddressCode());
                    }
                }

            }

            if (StrUtil.isBlank(addressVO.getAreaCode())) {
                if (StrUtil.isNotBlank(areaCN)) {
                    List<Region> areas = regionDAO.findAllByNameAndLevel(areaCN, DISTRICT);
                    if (CollectionUtils.isNotEmpty(areas)) {

                        areas.forEach(area -> {
                            Region city = regionDAO.getByAddressCodeAndLevel(area.getParentCode(), CITY);
                            if (StrUtil.isNotBlank(addressVO.getCityCode()) && city.getAddressCode().equals(addressVO.getCityCode())) {
                                addressVO.setAreaCode(area.getAddressCode());
                            }
                        });

                    }
                }
            }

        }

        if (StrUtil.isBlank(addressVO.getProvinceCode())) {
            String province = idCardNo.substring(0, 2) + "0000";
            Region region = regionDAO.getByAddressCode(province);
            if (Objects.nonNull(region)) {
                addressVO.setProvinceCode(province);
            }
        }
        if (StrUtil.isBlank(addressVO.getCityCode())) {
            String city = idCardNo.substring(0, 4) + "00";
            Region region = regionDAO.getByAddressCode(city);
            if (Objects.nonNull(region)) {
                addressVO.setCityCode(city);
            }
        }
        if (StrUtil.isBlank(addressVO.getAreaCode())) {
            String area = idCardNo.substring(0, 6);
            Region region = regionDAO.getByAddressCode(area);
            if (Objects.nonNull(region)) {
                addressVO.setAreaCode(area);
            }
        }

        if (StrUtil.isEmpty(addressVO.getCityCode()) || StrUtil.isEmpty(addressVO.getAreaCode())) {
            addressVO.setAreaCode(null);
            addressVO.setProvinceCode(null);
            addressVO.setCityCode(null);
        }
        addressVO.setNewAddress(address);
        return addressVO;

    }

    @Override
    public void regeneration() {
        String key = RedisCacheKeys.getProvinceCityDistrictCodeKey();
        if (redisHelper.hasKey(key)) {
            redisHelper.delKey(key);
        }
        String key2 = RedisCacheKeys.getDistrictCodeName();
        if (redisHelper.hasKey(key2)) {
            redisHelper.delKey(key2);
        }

    }
}