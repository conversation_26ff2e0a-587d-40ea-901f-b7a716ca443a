package com.endovas.cps.enums;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

public enum RegionEnum {

    province("province"),
    city("city"),
    district("district"),
    street("street");
    public final String name;
    private static List<String> names = Lists.newArrayList();

    static {
        for (RegionEnum e : values()) {
            names.add(e.name);
        }
    }
    public static boolean isValid(String name) {
            return names.contains(name);
        }

    RegionEnum(String name) {
        this.name = name;
    }

    public static List<String> level3() {
        return Arrays.asList(RegionEnum.province.name(), RegionEnum.city.name(), RegionEnum.district.name());
    }

    public static List<String> level2() {
        return Arrays.asList(RegionEnum.province.name(), RegionEnum.city.name());
    }

}
