package com.endovas.cps.pojo.vo;

import cn.hutool.core.util.ObjectUtil;
import com.endovas.cps.entity.Region;
import com.endovas.cps.enums.RegionEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午5:42
 */
/**
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午5:42
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RegionVO extends BaseVO {
    @ApiModelProperty(value = "名称")
    private String label;
    @ApiModelProperty(value = "地址代码")
    private String value;
    @ApiModelProperty(value = "城市中心坐标")
    private String center;
    @ApiModelProperty(value = "数据级别，city、district、province")
    private String level;
    @ApiModelProperty(value = "子节点")
    private List<RegionVO> children;

    public static Function<Region, RegionVO> level3(List<Region> list) {
        return input -> {
            RegionVO vo = RegionVO.entityToVO.apply(input);
            vo.children = list.stream().filter(x -> x.getParentCode().equals(input.getAddressCode())).sorted(Comparator.comparing(Region::getAddressCode)).map(RegionVO.listToVO(list)).collect(Collectors.toList());
            if (vo.children.isEmpty()) {
                vo.children = Arrays.asList(ObjectUtil.clone(vo));
                vo.children.get(0).setChildren(Arrays.asList(ObjectUtil.clone(vo)));
            }
            return vo;
        };
    }

    public static Function<Region, RegionVO> listToVO(List<Region> list) {
        return input -> {
            if (!RegionEnum.level3().contains(input.getLevel())) {
                return null;
            }
            RegionVO vo = RegionVO.entityToVO.apply(input);
            vo.children = list.stream().filter(x -> x.getParentCode().equals(input.getAddressCode())).sorted(Comparator.comparing(Region::getAddressCode)).map(RegionVO.entityToVO).collect(Collectors.toList());
            if (vo.children.isEmpty()) {
                vo.children = Arrays.asList(ObjectUtil.clone(vo));
            }
            return vo;
        };
    }

    public static Function<Region, RegionVO> entityToVO = input -> {
        RegionVO vo = new RegionVO();
        vo.label = input.getName();
        vo.value = input.getAddressCode();
        vo.center = input.getCenter();
        vo.level = input.getLevel();
        return vo;
    };
}
