package com.endovas.cps.pojo.vo;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.Region;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午5:42
 */
@Setter
@Getter
public class RegionListVO extends BaseVO implements BeanConvert<RegionListVO, Region> {
    @ApiModelProperty(value = "名称")
    private String label;
    @ApiModelProperty(value = "国际化编码")
    private String nameCode;

    @ApiModelProperty(value = "地址代码")
    private String value;
    @ApiModelProperty(value = "城市中心坐标")
    private String center;

    @Override
    public RegionListVO convertFrom(Region input) {
        BeanUtil.copyProperties(input, this);
        this.label = input.getName();
        this.value = input.getAddressCode();
        return this;
    }




}
