package com.endovas.cps.controller.base;

import com.endovas.cps.enums.RegionEnum;
import com.endovas.cps.pojo.vo.RegionVO;
import com.endovas.cps.service.RegionService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.RenderJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午2:33
 */
@Slf4j
@RestController
@RequestMapping(path = Project.ANON + "/region")
@Api(value = "省市区", tags = "省市区接口")
@AllArgsConstructor
public class RegionController {
    private final RegionService regionService;

    @ApiOperation(value = "过滤查询", response = RegionVO.class)
    @GetMapping("/query")
    public String query(@ApiParam("国家:country 省:province 市:city 区:district") String level, String parentCode) {
        if (!RegionEnum.isValid(level)) {
            return RenderJson.fail("传入的参数不正确");
        }
        return RenderJson.success(regionService.findByLevel(level, parentCode));
    }

}
