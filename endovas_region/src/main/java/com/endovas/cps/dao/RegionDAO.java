package com.endovas.cps.dao;

import com.endovas.cps.entity.Region;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午2:24
 */
@Repository
public interface RegionDAO extends MysqlBaseRepo<Region> {
    List<Region> findByLevelIn(List<String> level);

    List<Region> findByLevelOrderByAddressCodeAsc(String level);

    List<Region> findByLevelAndParentCodeOrderByAddressCodeAsc(String level, String parentCode);

    Region getByNameAndLevel(String name, String level);

    Region getByAddressCode(String code);

    List<Region> findAllByNameAndLevel(String name, String level);

    Region getByAddressCodeAndLevel(String code, String level);
}
