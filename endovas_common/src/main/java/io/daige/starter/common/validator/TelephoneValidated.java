package io.daige.starter.common.validator;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import io.daige.starter.common.validator.annotation.Telephone;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/7/27
 * Time: 下午5:29
 */
public class TelephoneValidated implements ConstraintValidator<Telephone, String> {
    private boolean require;

    @Override
    public void initialize(Telephone telephone) {
        this.require = telephone.require();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (require) {
            if (StrUtil.isEmpty(value)) {
                return false;
            }
            return PhoneUtil.isMobile(value);
        }
        if (require == false && StrUtil.isNotBlank(value)) {
            return PhoneUtil.isMobile(value);
        }
        return true;
    }
}
