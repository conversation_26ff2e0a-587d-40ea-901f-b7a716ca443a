package io.daige.starter.common.database.mysql;

import cn.hutool.core.util.StrUtil;
import io.daige.starter.common.utils.CustomSecureUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.AttributeConverter;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 功能:
 *  Mysql入库出库自动加密敏感数据
 *
 * 支持jpa和mybatis的加密
 * jpa使用方式:
 *   在具体的orm实体字段上增加 @Convert(converter = AttributeEncrypt.class)
 * mybatis使用方式
 *   在具体的orm实体类上增加    @TableName(value = "表名",autoResultMap = true)
 *   在具体的orm实体字段上增加  @TableField(typeHandler = AttributeEncrypt.class)
 * 注意:mybatis的复杂语句映射未测试
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/6/29
 * Time: 下午5:48
 */
public class AttributeEncrypt extends BaseTypeHandler<String> implements AttributeConverter<String, String> {

    private static String attributeEncryptKey;

    @Value("${attribute-encrypt-key}")
    public void setAttributeEncryptKey(String attributeEncryptKey) {
        AttributeEncrypt.attributeEncryptKey = attributeEncryptKey;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, CustomSecureUtil.encryptHex(attributeEncryptKey,parameter));
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return StrUtil.isEmpty(rs.getString(columnName)) ? rs.getString(columnName) : CustomSecureUtil.decryptStr(attributeEncryptKey,rs.getString(columnName));
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return StrUtil.isEmpty(rs.getString(columnIndex)) ? rs.getString(columnIndex) : CustomSecureUtil.decryptStr(attributeEncryptKey,rs.getString(columnIndex));
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return StrUtil.isEmpty(cs.getString(columnIndex)) ? cs.getString(columnIndex) : CustomSecureUtil.decryptStr(attributeEncryptKey,cs.getString(columnIndex));
    }

    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (StrUtil.isEmpty(attribute)) {
            return attribute;
        }

        return CustomSecureUtil.encryptHex(attributeEncryptKey,attribute);

    }

    @Override
    public String convertToEntityAttribute(String dbData) {
        if (StrUtil.isEmpty(dbData)) {
            return dbData;
        }
        return CustomSecureUtil.decryptStr(attributeEncryptKey,dbData);
    }
}
