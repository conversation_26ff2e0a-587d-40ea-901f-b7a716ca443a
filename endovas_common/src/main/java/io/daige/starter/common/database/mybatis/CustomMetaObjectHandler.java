package io.daige.starter.common.database.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.utils.ContextUtil;
import io.daige.starter.component.idGenerator.UidGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

/**
 * MyBatis Plus 元数据处理类
 * 用于自动 注入createTime, updateTime, createdBy, updatedBy 等字段
 * <p>
 * 判断逻辑：
 * 1. insert 方法， createTime, updateTime, createdBy, updatedBy 字段，字段为空则自动生成，不为空则使用传递进来的
 * 2. update 方法，updateTime, updatedBy 字段，字段为空则自动生成，不为空则使用传递进来的
 * <p>
 * 注入值：
 * createTime：LocalDateTime.now()
 * updateTime：LocalDateTime.now()
 * createdBy：BaseContextHandler.getUserId()
 * updatedBy：BaseContextHandler.getUserId()
 */
@Slf4j
public class CustomMetaObjectHandler implements MetaObjectHandler {

    /**
     * 字符串类型判断符
     */
    private static final String STRING_TYPE = "java.lang.String";

    private UidGenerator uidGenerator;

    public CustomMetaObjectHandler() {
        super();
    }

    /**
     * 注意：不支持 复合主键 自动注入！！
     * <p>
     * 所有的继承了Entity、SuperEntity的实体，在insert时，
     * createdBy, updatedBy: 自动赋予 当前线程上的登录人id
     * createTime, updateTime: 自动赋予 服务器的当前时间
     * <p>
     * 未继承任何父类的实体，且主键标注了 @TableId(value = "xxx", type = IdType.INPUT) 自动注入 主键
     * 主键的字段名称任意
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        fillCreated(metaObject);
        fillUpdated(metaObject);
    }


    private void fillCreated(MetaObject metaObject) {
        // 设置创建时间和创建人
        if (metaObject.getOriginalObject() instanceof MysqlBase) {
            created(metaObject);
            return;
        }

        if (metaObject.hasGetter(MysqlBase.CREATOR_ID)) {
            Object oldVal = metaObject.getValue(MysqlBase.CREATOR_ID);
            if (oldVal == null) {
                this.setFieldValByName(MysqlBase.CREATOR_ID, ContextUtil.getUserId(), metaObject);
            }
        }
        if (metaObject.hasGetter(MysqlBase.CREATE_TIME)) {
            Object oldVal = metaObject.getValue(MysqlBase.CREATE_TIME);
            if (oldVal == null) {
                this.setFieldValByName(MysqlBase.CREATE_TIME, LocalDateTime.now(), metaObject);
            }
        }

    }

    private void created(MetaObject metaObject) {
        MysqlBase entity = (MysqlBase) metaObject.getOriginalObject();
        if (entity.getCreateTime() == null) {
            this.setFieldValByName(MysqlBase.CREATE_TIME, LocalDateTime.now(), metaObject);
        }
        if (entity.getCreatorId() == null || entity.getCreatorId().equals(0)) {
            Object userIdVal = STRING_TYPE.equals(metaObject.getGetterType(MysqlBase.CREATOR_ID).getName()) ? String.valueOf(ContextUtil.getUserId()) : ContextUtil.getUserId();
            this.setFieldValByName(MysqlBase.CREATOR_ID, userIdVal, metaObject);
        }
    }


    private void fillUpdated(MetaObject metaObject) {
        // 修改人 修改时间
        if (metaObject.getOriginalObject() instanceof MysqlBase) {
            update(metaObject);
            return;
        }

        if (metaObject.hasGetter(MysqlBase.UPDATER_ID)) {
            this.setFieldValByName(MysqlBase.UPDATER_ID, ContextUtil.getUserId(), metaObject);
        }
        if (metaObject.hasGetter(MysqlBase.UPDATE_TIME)) {
            this.setFieldValByName(MysqlBase.UPDATE_TIME, LocalDateTime.now(), metaObject);
        }
    }

    private void update(MetaObject metaObject) {
        Object userIdVal = STRING_TYPE.equals(metaObject.getGetterType(MysqlBase.UPDATER_ID).getName()) ? String.valueOf(ContextUtil.getUserId()) : ContextUtil.getUserId();
        this.setFieldValByName(MysqlBase.UPDATER_ID, userIdVal, metaObject);
        this.setFieldValByName(MysqlBase.UPDATE_TIME, LocalDateTime.now(), metaObject);
    }

    /**
     * 所有的继承了Entity、SuperEntity的实体，在update时，
     * updatedBy: 自动赋予 当前线程上的登录人id
     * updateTime: 自动赋予 服务器的当前时间
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("start update fill ....");
        fillUpdated(metaObject);
    }
}
