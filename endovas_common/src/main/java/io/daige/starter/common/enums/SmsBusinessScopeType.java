package io.daige.starter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SmsBusinessScopeType implements BaseEnum {
    LOGIN("登录"),
    MODIFY_PASS("修改密码"),
    FORGET_PASS("忘记密码"),
    LOGOFF("注销")
    ;

    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static SmsBusinessScopeType match(String val, SmsBusinessScopeType def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static SmsBusinessScopeType get(String val) {
        return match(val, null);
    }

    public boolean eq(SmsBusinessScopeType val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
