package io.daige.starter.common.cache;

/**
 * redis缓存的key全部统一存储在这里
 * 并提供对应的key的get方法
 * 注意，key本身不要设置为public，这样容易散落在程序的各个地方，并且容易在其他对方再次随意拼接，导致后面key无法维护
 */
public class RedisCacheKeys {
    private static final String PROJECT_KEY_PREFIX = "endovas.cps.";
    private static final String CURRENT_LOGIN_USER = PROJECT_KEY_PREFIX + "c:u:%s";
    private static final String TOKEN_PREFIX = PROJECT_KEY_PREFIX + "token:pre:%s";
    private static final String LOGIN_FAILED_COUNT = PROJECT_KEY_PREFIX + "login.failed.count";
    private static final String USER_PRIVACY = PROJECT_KEY_PREFIX + "user.privacy";

    private static final String PERMISSION_LIST = PROJECT_KEY_PREFIX + "permission:list";

    private static final String PERMISSION_USER_MENU_LIST = PROJECT_KEY_PREFIX + "permission:userMenuList:%s";
    private static final String PERMISSION_USER_MENU_BTN_LIST = PROJECT_KEY_PREFIX + "permission:userMenuBtnList:%s";
    private static final String PROVINCE_CITY_DISTRICT_CODE_KEY = PROJECT_KEY_PREFIX + "province.city.district";
    private static final String PROVINCE_CITY_CODE_KEY = PROJECT_KEY_PREFIX + "province.city";
    private static final String DISTRICT_CODE_NAME = PROJECT_KEY_PREFIX + "districtCodeName";
    private static final String SYSTEM_DICT_KEY = PROJECT_KEY_PREFIX + "system_dict";
    private static final String SYSTEM_CONFIG_KEY = PROJECT_KEY_PREFIX + "system_config";

    private static final String MEETING_CREATE_ROOM_KEY = PROJECT_KEY_PREFIX + "m.c:%s";
    private static final String MEETING_JOIN_ROOM_ERROR_KEY = PROJECT_KEY_PREFIX + "m.j.e:%s";
    private static final String MEETING_REPEAT_JOIN_ROOM_KEY = PROJECT_KEY_PREFIX + "m.r.j:%s";
    private static final String MEETING_ASR_KEY = PROJECT_KEY_PREFIX + "m.asr:%s";

    private static final String MEASUREMENT_TASK_SEQ_KEY = PROJECT_KEY_PREFIX+ "measurementtask:seq:%s";
    private static final String LABEL_TASK_SEQ_KEY = PROJECT_KEY_PREFIX+ "labeltask:seq:%s";
    private static final String MEASUREMENT_OPERATION_TOKEN_KEY = PROJECT_KEY_PREFIX+ "measurementoperation:token:%s";
    private static final String EQUIP_INUSE_USERID_KEY = PROJECT_KEY_PREFIX+ "equipInuse:equipId:%s"; // 设备id正在被userid使用


    public static String getPermissionList() {
        return PERMISSION_LIST;
    }

    public static String getDistrictCodeName() {
        return DISTRICT_CODE_NAME;
    }

    public static String getTokenKey(String userId) {
        return String.format(CURRENT_LOGIN_USER, userId);
    }

    public static String getTokenPrefixKey(String token) {
        return String.format(TOKEN_PREFIX, token);
    }

    public static String getMenuKey(String userId) {
        return String.format(PERMISSION_USER_MENU_LIST, userId);
    }

    public static String getMenuBtnKey(String userId) {
        return String.format(PERMISSION_USER_MENU_BTN_LIST, userId);
    }

    public static String getProvinceCityDistrictCodeKey() {
        return PROVINCE_CITY_DISTRICT_CODE_KEY;
    }

    public static String getProvinceCityCodeKey() {
        return PROVINCE_CITY_CODE_KEY;
    }


    public static String loginFailedCountCacheKey(String account) {
        return String.format(LOGIN_FAILED_COUNT + ".%s", account);
    }

    public static String getPrivacyKey(String id) {
        return String.format(USER_PRIVACY + ".%s", id);
    }

    public static String getSystemConfigKey(String key) {
        return String.format(SYSTEM_CONFIG_KEY + ".%s", key);
    }

    public static String getSystemDictKey(String groupCode) {
        return String.format(SYSTEM_DICT_KEY + ".%s", groupCode);
    }

    public static String getMeetingCreateRoomKey(String uid) {
        return String.format(MEETING_CREATE_ROOM_KEY,  uid);
    }

    public static String getMeetingJoinRoomErrorKey(String uid) {
        return String.format(MEETING_JOIN_ROOM_ERROR_KEY,  uid);
    }
    public static String getMeetingRepeatJoinRoomKey(String roomId, String uid) {
        return String.format(MEETING_REPEAT_JOIN_ROOM_KEY, roomId + "_" + uid);
    }
    public static String getMeetingAsrKey(String roomId, String uid ){
        return String.format(MEETING_ASR_KEY, roomId + "_" + uid);
    }

    public static String getMeasurementTaskSeqKey(String dateStr) {
        return String.format(MEASUREMENT_TASK_SEQ_KEY, dateStr);
    }

    public static String getLabelTaskSeqKey(String dateStr) {
        return String.format(LABEL_TASK_SEQ_KEY, dateStr);
    }

    public static java.lang.String getMeasurementOperationTokenKey(String measurementOperationId) {
        return String.format(MEASUREMENT_OPERATION_TOKEN_KEY, measurementOperationId);
    }

    public static String getEquipInuseUserIdKey(String equipId) {
        return String.format(EQUIP_INUSE_USERID_KEY, equipId);
    }
}
