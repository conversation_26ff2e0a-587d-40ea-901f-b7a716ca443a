package io.daige.starter.common.wscoder;

import cn.hutool.json.JSONUtil;
import io.daige.starter.common.render.ToClientMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.websocket.EncodeException;
import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

/**
 * @author: wk
 * @Date: 2025/1/2
 * @Time: 17:26
 */
public class ToClientMsgEncoder implements Encoder.Text<ToClientMsg>{
    private static final Logger log = LoggerFactory.getLogger(ToClientMsgEncoder.class);

    /**
     * 这里的参数 ToClientMsg 要和  Encoder.Text<T>保持一致
     * @param msg
     * @return
     * @throws EncodeException
     */
    @Override
    public String encode(ToClientMsg msg) throws EncodeException {
        /*
         * 这里是重点，只需要返回Object序列化后的json字符串就行
         */
        try {
            return JSONUtil.toJsonStr(msg);
        }catch (Exception e){
            log.info("ToClientMsgEncoder编码异常:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public void init(EndpointConfig endpointConfig) {
    }

    @Override
    public void destroy() {
    }
}
