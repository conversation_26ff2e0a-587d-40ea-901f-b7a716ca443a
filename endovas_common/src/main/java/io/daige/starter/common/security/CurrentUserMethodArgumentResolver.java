package io.daige.starter.common.security;

import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

import static io.daige.starter.common.constant.BizCommonConst.CURRENT_USER;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/8/29
 * Time: 上午1:52
 */
public class CurrentUserMethodArgumentResolver implements HandlerMethodArgumentResolver {
    /**
     * supportsParameter：用于判定是否需要处理该参数分解，返回true为需要，并会去调用下面的方法resolveArgument。
     *
     * @param parameter
     * @return
     */
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        //判断是否能转成User 类型
        return parameter.getParameterType().isAssignableFrom(LoginUser.class)
                //是否有CurrentUser注解
                && parameter.hasParameterAnnotation(CurrentUser.class);
    }

    /**
     * resolveArgument：真正用于处理参数分解的方法，返回的Object就是controller方法上的形参对象。
     *
     * @param parameter
     * @param mavContainer
     * @param webRequest
     * @param binderFactory
     * @return
     * @throws Exception
     */
    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        LoginUser loginVO = (LoginUser) webRequest.getAttribute(CURRENT_USER, RequestAttributes.SCOPE_REQUEST);
        if (loginVO != null) {
            return loginVO;
        }
        throw new MissingServletRequestPartException(CURRENT_USER);
    }
}
