package io.daige.starter.common.validator;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import io.daige.starter.common.validator.annotation.Email;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/7/27
 * Time: 下午5:35
 */
public class EmailValidated implements ConstraintValidator<Email, String> {
    private boolean require;

    @Override
    public void initialize(Email email) {
        this.require = email.require();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (require) {
            if (StrUtil.isEmpty(value)) {
                return false;
            }
            return Validator.isEmail(value);
        }
        if (require == false && StrUtil.isNotBlank(value)) {
            return Validator.isEmail(value);
        }
        return true;
    }
}
