package io.daige.starter.common.cache;

public class RedisCacheExpired {

    public static final int oneMinute = 60;
    public static final int oneHour = RedisCacheExpired.oneMinute * 60;
    public static final int oneDay = RedisCacheExpired.oneHour * 24;
    public static final int oneWeek = RedisCacheExpired.oneDay * 7;
    public static final int oneMonth = RedisCacheExpired.oneDay * 30;
    public static final int oneYear = RedisCacheExpired.oneDay * 365;

}
