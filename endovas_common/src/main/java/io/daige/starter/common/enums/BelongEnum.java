package io.daige.starter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 终端类型
 * </p>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BelongEnum implements BaseEnum {
    PLATFORM_USER("平台"),
    MED_AGENT_USER("代理商"),
    HOSPITAL_USER("医院"),
    EXPERT("专家"),

    ;

    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static BelongEnum match(String val, BelongEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static BelongEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(BelongEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
