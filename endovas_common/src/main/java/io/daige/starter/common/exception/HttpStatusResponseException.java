package io.daige.starter.common.exception;


/**
 * 用于标识返回的http status
 */
public class HttpStatusResponseException extends RuntimeException {

    private Integer httpStatus;

    public HttpStatusResponseException(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }

    public HttpStatusResponseException(Integer httpStatus, String message) {
        super(message);
        this.httpStatus = httpStatus;
    }


    public HttpStatusResponseException() {
    }

    public HttpStatusResponseException(String message, Throwable e) {
        super(message, e);
    }

    public Integer getHttpStatus() {
        return httpStatus;
    }
}
