package io.daige.starter.common.validator.annotation;

import io.daige.starter.common.validator.IDCardNoValidated;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/7/27
 * Time: 下午5:28
 */
@Constraint(validatedBy = {IDCardNoValidated.class})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.ANNOTATION_TYPE, ElementType.METHOD, ElementType.FIELD})
public @interface IDCardNo {
    String message() default "身份证号码无效!";

    boolean require() default false;

    // 以下两行为固定模板
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
