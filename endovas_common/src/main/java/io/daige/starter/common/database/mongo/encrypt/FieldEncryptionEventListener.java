package io.daige.starter.common.database.mongo.encrypt;

import io.daige.starter.common.database.mongo.encrypt.reflection.Node;
import io.daige.starter.common.database.mongo.encrypt.reflection.ReflectionCache;
import org.bson.Document;
import org.springframework.data.mongodb.core.mapping.event.AfterLoadEvent;
import org.springframework.data.mongodb.core.mapping.event.BeforeSaveEvent;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Does all reflection at startup. There is no reflection used at runtime.
 * Does not support polymorphism and does not need '_class' fields either.
 */
public class FieldEncryptionEventListener extends AbstractEncryptionEventListener<FieldEncryptionEventListener> {
    ReflectionCache reflectionCache = new ReflectionCache();

    public FieldEncryptionEventListener(String key) {
        super(key);
    }

    Node node(Class clazz) {
        List<Node> children = reflectionCache.reflectRecursive(clazz);
        if (!children.isEmpty()) return new Node("", children, Node.Type.DOCUMENT);
        return Node.EMPTY;
    }

    @Override
    public void onAfterLoad(AfterLoadEvent event) {
        Document document = event.getDocument();

        Node node = node(event.getType());
        if (node == Node.EMPTY) return;
        cryptFields(document, node, new Decoder());
    }

    @Override
    public void onBeforeSave(BeforeSaveEvent event) {
        Document document = event.getDocument();

        Node node = node(event.getSource().getClass());
        if (node == Node.EMPTY) return;
        cryptFields(document, node, new Encoder());
    }

    void cryptFields(Object o, Node node, Function<Object, Object> crypt) {
        try {
            switch (node.type) {
                case MAP:
                    cryptMap((Document) o, node, crypt);
                    break;

                case DOCUMENT:
                    cryptDocument((Document) o, node, crypt);
                    break;

                case LIST:
                    cryptList((List) o, node, crypt);
                    break;

                default:
                    throw new IllegalArgumentException("Unknown class field to crypt for field " + node.fieldName + ": " + o.getClass());
            }
        } catch (ClassCastException e) {
            throw e;
        }
    }

    void cryptList(List list, Node node, Function<Object, Object> crypt) {
        if (node.type != Node.Type.LIST)
            throw new IllegalArgumentException("Expected list for " + node.fieldName + ", got " + node.type);

        Node mapChildren = node.children.get(0);
        for (int i = 0; i < list.size(); i++) {
            cryptFields(list.get(i), mapChildren, crypt);
        }
    }

    void cryptMap(Document document, Node node, Function<Object, Object> crypt) {
        Node mapChildren = node.children.get(0);
        for (Map.Entry<String, Object> entry : document.entrySet()) {
            cryptFields(entry.getValue(), mapChildren, crypt);
        }
    }

    void cryptDocument(Document document, Node node, Function<Object, Object> crypt) {
        for (Node childNode : node.children) {
            Object value = document.get(childNode.documentName);
            if (value == null) continue;

            if (childNode.type == Node.Type.DIRECT) {
                document.put(childNode.documentName, crypt.apply(value));
            } else {
                cryptFields(value, childNode, crypt);
            }
        }
    }
}
