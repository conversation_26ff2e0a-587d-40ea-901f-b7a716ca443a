package io.daige.starter.common.database.mybatis;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import io.daige.starter.common.annotation.DecryptField;
import io.daige.starter.common.utils.CustomSecureUtil;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Properties;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/29
 * Time: 下午7:32
 */

@Component
@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
public class QueryInterceptor implements Interceptor {
    private final static String ATTRIBUTE_ENCRYPT_CLASS = "io.daige.starter.common.database.mysql.AttributeEncrypt";
    private static String attributeEncryptKey;

    @Value("${attribute-encrypt-key}")
    public void setAttributeEncryptKey(String attributeEncryptKey) {
        QueryInterceptor.attributeEncryptKey = attributeEncryptKey;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //取出查询的结果
        Object resultObject = invocation.proceed();
        if (Objects.isNull(resultObject)) {
            return null;
        }
        //基于selectList
        if (resultObject instanceof ArrayList) {
            ArrayList resultList = (ArrayList) resultObject;
            if (!CollectionUtils.isEmpty(resultList)) {
                for (Object result : resultList) {
                    decrypt(result);
                }
            }
            //基于selectOne
        } else {
            decrypt(resultObject);
        }
        return resultObject;
    }


    public <T> T decrypt(T result) throws IllegalAccessException {
        //取出resultType的类
        Class<?> resultClass = result.getClass();
        Field[] declaredFields = resultClass.getDeclaredFields();
        for (Field field : declaredFields) {
            //取出所有被TableField注解的字段,用于实体层(po)
            TableField decryptTransaction = field.getAnnotation(TableField.class);
            //取出所有被DecryptField注解的字段,用于实体层(vo)
            DecryptField decryptField = field.getAnnotation(DecryptField.class);

            if (Objects.nonNull(decryptField) ||
                    (Objects.nonNull(decryptTransaction) && Objects.nonNull(decryptTransaction.typeHandler()) && ATTRIBUTE_ENCRYPT_CLASS.equals(decryptTransaction.typeHandler().getName()))) {
                Object object = ReflectUtil.getFieldValue(result,field);
                if (Objects.isNull(object)) {
                    continue;
                }
                //String的解密
                if (object instanceof String) {
                    String value = (String) object;
                    if (StrUtil.isBlank(value)) {
                        continue;
                    }
                    //对注解的字段进行逐一解密
                    try {
                        field.set(result, CustomSecureUtil.decryptStr(attributeEncryptKey,value));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
