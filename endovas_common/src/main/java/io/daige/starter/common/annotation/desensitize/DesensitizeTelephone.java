package io.daige.starter.common.annotation.desensitize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要脱敏的手机号
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/8/19
 * Time: 下午12:31
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DesensitizeTelephone {
}
