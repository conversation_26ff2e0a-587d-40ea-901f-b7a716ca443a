package io.daige.starter.common.database.mysql;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.daige.starter.common.database.jpa.GenId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class MysqlBase implements Serializable {
    public static final String FIELD_ID = "id";
    public static final String UPDATE_TIME = "updateTime";
    public static final String COL_UPDATE_TIME = "update_time";
    public static final String UPDATER_ID = "updaterId";
    public static final String COL_UPDATER_ID = "updater_id";
    public static final String CREATE_TIME = "createTime";
    public static final String COL_CREATE_TIME = "create_time";
    public static final String CREATOR_ID = "creatorId";
    public static final String COL_CREATOR_ID = "creator_id";
    public static final String COL_DEL = "is_del";
    public static final String DEL = "del";
    public static final String VERSION = "version";

    public static final String[] SKIPLIST = new String[]{FIELD_ID, UPDATE_TIME, CREATE_TIME, VERSION};

    private static final long serialVersionUID = 5169873634279173683L;

    @Id
    @TableId
    @ApiModelProperty(value = "唯一标识")
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "custom-uuid")
    @GenericGenerator(name = "custom-uuid", strategy = GenId.GEN_STRATEGY_CLASS)
    private String id;


    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    @CreatedBy
    private String creatorId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @CreatedDate
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @LastModifiedBy
    private String updaterId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @LastModifiedDate
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "乐观锁版本号")
    @Version
    @com.baomidou.mybatisplus.annotation.Version
    @Column(name = "version", columnDefinition = "bigint default 0")
    private Long version;

    @ApiModelProperty(value = "删除标志")
    @TableField("is_del")
    @Column(name = "is_del")
    private Boolean del = false;

    public String getId() {
        if (StrUtil.isEmpty(this.id)) {
            this.id = GenId.generate();
        }
        return this.id;
    }
}
