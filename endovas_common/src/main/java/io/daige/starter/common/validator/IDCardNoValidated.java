package io.daige.starter.common.validator;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import io.daige.starter.common.validator.annotation.IDCardNo;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/7/27
 * Time: 下午5:35
 */
public class IDCardNoValidated implements ConstraintValidator<IDCardNo, String> {
    private boolean require;

    @Override
    public void initialize(IDCardNo idCardNo) {
        this.require = idCardNo.require();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (require) {
            if (StrUtil.isEmpty(value)) {
                return false;
            }
            return IdcardUtil.isValidCard(value);
        }
        if (require == false && StrUtil.isNotBlank(value)) {
            return IdcardUtil.isValidCard(value);
        }
        return true;
    }
}
