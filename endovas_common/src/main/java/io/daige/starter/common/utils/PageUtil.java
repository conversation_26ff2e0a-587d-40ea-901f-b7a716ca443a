package io.daige.starter.common.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.google.common.collect.Lists;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;

/**
 *
 */
public class PageUtil {

    /**
     * JPA分页封装
     *
     * @param page
     * @return
     */
    public static Pageable initJPAPage(PageFO page) {

        Pageable pageable;
        int pageNumber = page.getPageNumber();
        int pageSize = page.getPageSize();
        String sort = page.getSort();
        String order = page.getOrder();

        if (pageNumber < 1) {
            pageNumber = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100;
        }
        if (StrUtil.isNotBlank(sort)) {
            Sort.Direction d;
            if (StrUtil.isBlank(order)) {
                d = Sort.Direction.DESC;
            } else {
                d = Sort.Direction.valueOf(order.toUpperCase());
            }
            Sort s = Sort.by(d, sort);
            pageable = PageRequest.of(pageNumber - 1, pageSize, s);
        } else {
            pageable = PageRequest.of(pageNumber - 1, pageSize);
        }
        return pageable;
    }

    public static com.baomidou.mybatisplus.extension.plugins.pagination.Page initMybatisPage(PageFO page) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page pageable;
        int pageNumber = page.getPageNumber();
        int pageSize = page.getPageSize();
        String sort = page.getSort();
        String order = page.getOrder();
        if (pageNumber < 1) {
            pageNumber = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100;
        }
        if (StrUtil.isNotBlank(sort)) {
            sort = StrUtil.toUnderlineCase(sort);
            OrderItem orderItem;
            if (StrUtil.isBlank(order) || "desc".equals(order)) {
                orderItem = OrderItem.desc(sort);
            } else {
                orderItem = OrderItem.asc(sort);
            }
            pageable = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(pageNumber, pageSize);
            pageable.setOrders(Lists.newArrayList(orderItem));
        } else {
            pageable = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(pageNumber, pageSize);
        }
        return pageable;
    }


    //mybatis 分页
    public static PageVO convert(IPage page) {
        return new PageVO(page.getTotal(), page.getRecords(), Long.valueOf(page.getCurrent()).intValue(), Long.valueOf(page.getSize()).intValue());
    }

    //jpa分页
    public static PageVO convert(Page page) {
        return new PageVO(page.getTotalElements(), page.getContent(), page.getNumber() + 1, page.getSize());
    }

    public static PageVO empty(PageFO pageFO) {
        return new PageVO(0L, new ArrayList(), pageFO.getPageNumber(), Long.valueOf(pageFO.getPageSize()).intValue());
    }

}
