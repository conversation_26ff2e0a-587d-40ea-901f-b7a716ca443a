package io.daige.starter.common.database.mysql;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;

/**
 * E 实体
 * ID 唯一主键
 */
// 自定义接口 不会创建接口的实例 必须加此注解
@NoRepositoryBean
public interface MysqlBaseRepo<E> extends JpaRepository<E, String>, JpaSpecificationExecutor<E> {


}
