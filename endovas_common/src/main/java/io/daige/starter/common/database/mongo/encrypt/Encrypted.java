package io.daige.starter.common.database.mongo.encrypt;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * mongodb的字段数据加密
 * 使用方式 MongodbConfig加入如下代码:
 *
 *   @Value("${attribute-encrypt-key}")
 *     private String attributeEncryptKey;
 *
 *     @Bean
 *     AbstractEncryptionEventListener encryptionEventListener() {
 *         AbstractEncryptionEventListener eventListener = new FieldEncryptionEventListener(attributeEncryptKey);
 *         return eventListener;
 *     }
 * 然后在mongodb的实体上增加@Encrypted注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.ANNOTATION_TYPE})
public @interface Encrypted {
}
