package io.daige.starter.common.utils;


import cn.hutool.core.util.ArrayUtil;
import io.daige.starter.common.constant.CustomResultCodeConst;
import io.daige.starter.common.exception.BusinessAssertException;

import java.time.LocalDateTime;
import java.util.Collection;



/**
 * 断言
 *
 * @date 2019-07-22 14:44
 * @since 4.0
 */
public final class BizAssert {
    private BizAssert() {
    }

    /**
     * Fails a test with the given message.
     *
     * @param message the identifying message for the {@link BusinessAssertException} (<code>null</code>
     *                okay)
     * @
     * @see BusinessAssertException
     */
    public static void fail(int code, String message) {
        throw new BusinessAssertException(code, message);
    }

    public static void fail(BusinessAssertException businessAssertException) {
        if (businessAssertException != null) {
           throw businessAssertException;
        }
        fail(CustomResultCodeConst.FAIL, "参数验证异常");
    }

    /**
     * Fails a test with no message.
     *
     * @
     */
    public static void fail() {
        fail(CustomResultCodeConst.FAIL, "参数验证异常");
    }

    public static void fail(String msg) {
        String message = msg;
        if (message == null || "".equals(message)) {
            message = "参数验证异常";
        }
        fail(CustomResultCodeConst.FAIL, message);
    }

    /**
     * 断言条件为真。如果不是，它会抛出一个带有给定消息的异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误
     * @param condition     被检查的条件
     * @
     */
    public static void isTrue(boolean condition, BusinessAssertException businessAssertException) {
        if (!condition) {
            fail(businessAssertException);
        }
    }

    /**
     * 断言条件为真。如果不是，它会抛出一个参数检测异常
     * {@link BusinessAssertException}
     *
     * @param condition 被检查的条件
     * @
     */
    public static void isTrue(Boolean condition, String exceptionMessage) {
        if (!condition) {
            fail(exceptionMessage);
        }
    }

    /**
     * 断言条件为真。如果不是，它会抛出一个参数检测异常
     * {@link BusinessAssertException}
     *
     * @param condition 被检查的条件
     */
    public static void isTrue(boolean condition) {
        if (!condition) {
            fail();
        }
    }

    /**
     * 断言条件为假。如果不是，它会抛出一个带有给定消息的异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误码
     * @param condition     被检查的条件
     */
    public static void isFalse(boolean condition, BusinessAssertException businessAssertException) {
        if (condition) {
            fail(businessAssertException);
        }
    }

    public static void isFalse(boolean condition, String exceptionMessage) {
        if (condition) {
            fail(exceptionMessage);
        }
    }


    /**
     * 断言检查这个对象不是 Null。 如果是null，用给定的错误码<code>businessAssertException</code>抛出异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误码
     * @param object        检查对象
     * @
     */
    public static void notNull(Object object, BusinessAssertException businessAssertException) {
        if (object == null) {
            fail(businessAssertException);
        }
    }

    public static void notNull(Object object) {
        if (object == null) {
            fail();
        }
    }

    public static void notNull(Object object, String message) {
        if (object == null) {
            fail(message);
        }
    }

    /**
     * 断言检查这个对象是 Null。 如果不是null，用给定的错误码<code>businessAssertException</code>抛出异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误码
     * @param object        检查对象
     * @
     */
    public static void isNull(Object object, BusinessAssertException businessAssertException) {
        if (object != null) {
            fail(businessAssertException);
        }
    }


    /**
     * 断言集合不为空，如果为null或者empty，用指定错误码抛出异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误码
     * @param collection    集合
     * @
     */
    public static void notEmpty(Collection<?> collection, BusinessAssertException businessAssertException) {
        if (collection == null || collection.isEmpty()) {
            fail(businessAssertException);
        }
    }

    /**
     * 断言集合不为空，如果为null或者empty，用指定错误码抛出异常
     * {@link BusinessAssertException}
     *
     * @param exceptionMsg 错误码
     * @param collection   集合
     */
    public static void notEmpty(Collection<?> collection, String exceptionMsg) {
        if (collection == null || collection.isEmpty()) {
            fail(exceptionMsg);
        }
    }

    public static <T> void notEmpty(T[] array, BusinessAssertException businessAssertException) {
        if (ArrayUtil.hasNull(array)) {
            fail(businessAssertException);
        }
    }

    /**
     * 断言字符串不为空，如果为null或者empty，用指定错误码抛出异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误码
     * @param value         字符串
     * @
     */
    public static void notEmpty(String value,BusinessAssertException businessAssertException) {
        if (value == null || value.isEmpty()) {
            fail(businessAssertException);
        }
    }

    public static void notEmpty(String value, String exceptionMsg) {
        if (value == null || value.isEmpty()) {
            fail(exceptionMsg);
        }
    }

    public static void notEmpty(String value) {
        if (value == null || value.isEmpty()) {
            fail();
        }
    }

    /**
     * 断言2个对象不是相等的。如果相等则抛出异常
     * {@link BusinessAssertException}。
     * 如果<code>unexpected</code> 和 <code>actual</code> 是 <code>null</code>,
     * 他们被认为是相等的。
     *
     * @param businessAssertException 错误码
     * @param unexpected    意想不到的值
     * @param actual        要检查的值 <code>unexpected</code>
     * @
     */
    public static void notEquals(Object unexpected, Object actual, BusinessAssertException businessAssertException) {
        if (unexpected == null && actual == null) {
            fail(businessAssertException);
        }
        if (unexpected != null && actual != null && unexpected.equals(actual)) {
            fail(businessAssertException);
        }

    }

    /**
     * 断言2个字符串是否相等，如果不等用指定错误码抛出异常
     * {@link BusinessAssertException}
     *
     * @param businessAssertException 错误码
     * @param expected      预期的值
     * @param actual        需要比较的字符串<code>expected</code>
     * @
     */
    public static void equals(String expected, String actual, BusinessAssertException businessAssertException) {
        if (expected == null && actual == null) {
            return;
        }
        if (expected != null && expected.equals(actual)) {
            return;
        }
        fail(businessAssertException);
    }

    public static void equals(String expected, String actual, String exceptionMsg) {
        if (expected == null && actual == null) {
            return;
        }
        if (expected != null && expected.equals(actual)) {
            return;
        }
        fail(exceptionMsg);
    }

    public static void equals(Object expected, Object actual, String exceptionMsg) {
        if (expected == null && actual == null) {
            return;
        }
        if (expected != null && expected.equals(actual)) {
            return;
        }
        fail(exceptionMsg);
    }


    /**
     * 断言 预期值（expected） 大于 实际值（actual）
     *
     * @param expected     预期值
     * @param actual       实际值
     * @param exceptionMsg 异常消息
     */
    public static void gt(LocalDateTime expected, LocalDateTime actual, String exceptionMsg) {
        if (expected == null || actual == null) {
            fail(exceptionMsg);
            return;
        }

        if (expected.isAfter(actual)) {
            fail(exceptionMsg);
        }
    }


}
