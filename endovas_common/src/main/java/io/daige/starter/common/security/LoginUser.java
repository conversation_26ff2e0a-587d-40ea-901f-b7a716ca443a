package io.daige.starter.common.security;

import io.daige.starter.common.enums.BelongEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
public class LoginUser implements Serializable {
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "组织ID")
    private String orgId;
    @ApiModelProperty(value = "登录账号")
    private String account;
    @ApiModelProperty(value = "手机号")
    private String telephone;


    @ApiModelProperty(value = "权限列表代码")
    private List<String> permissions;

    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty(value = "角色名")
    private String roleName;
    @ApiModelProperty(value = "是否记住登录状态")
    private Boolean saveLogin;

    @ApiModelProperty(value = "数据是否脱敏")
    private Boolean desensitize;
    @ApiModelProperty(value = "特别字段权限")
    private List<String> specialFields;


    private Integer dataType;

    private String medAgentId;

    private String hospitalId;

    private BelongEnum belong;
    private Boolean privacy;

}
