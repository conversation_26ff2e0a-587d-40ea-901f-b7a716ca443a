package io.daige.starter.common.utils;

import cn.hutool.crypto.SecureUtil;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/12/16
 * Time: 下午12:39
 */
public class CustomSecureUtil {
    private CustomSecureUtil() {

    }
    public static byte[] decrypt(String key,byte[] bytes){
        return SecureUtil.aes(key.getBytes()).decrypt(bytes);
    }
    public static byte[] encrypt(String key,byte[] bytes){
        return SecureUtil.aes(key.getBytes()).encrypt(bytes);
    }

    public static String encryptHex(String key, String data) {
        return SecureUtil.aes(key.getBytes()).encryptHex(data);
    }

    public static String decryptStr(String key, String data) {
        return SecureUtil.aes(key.getBytes()).decryptStr(data);
    }
}
