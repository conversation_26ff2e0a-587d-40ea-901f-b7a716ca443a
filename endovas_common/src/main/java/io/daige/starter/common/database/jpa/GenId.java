package io.daige.starter.common.database.jpa;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.component.idGenerator.service.IdGeneratorService;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.UUIDGenerator;

import java.io.Serializable;

public class GenId extends UUIDGenerator {
    public final static String GEN_STRATEGY_CLASS = "io.daige.starter.common.database.jpa.GenId";

    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        String id = ((MysqlBase) object).getId();
        if (StrUtil.isBlank(id)) {
            IdGeneratorService idGeneratorService = SpringUtil.getBean(IdGeneratorService.class);
            return idGeneratorService.getUID();
        }
        return id;
    }

    public static String generate() {
        IdGeneratorService idGeneratorService = SpringUtil.getBean(IdGeneratorService.class);
        return String.valueOf(idGeneratorService.getUID());
    }
}
