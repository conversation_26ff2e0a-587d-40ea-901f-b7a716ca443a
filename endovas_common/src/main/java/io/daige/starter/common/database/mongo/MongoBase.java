package io.daige.starter.common.database.mongo;

import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2020/12/15
 * Time: 下午7:32
 */
@Getter
@Setter
public abstract class MongoBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    private String id = new ObjectId().toString();
    private LocalDateTime createTime = LocalDateTime.now();
    @LastModifiedDate
    private LocalDateTime updateTime;
    public static final String CREATE_TIME = "createTime";
    public static final String UPDATE_TIME = "updateTime";
    public static final String FIELD_ID = "id";
}
