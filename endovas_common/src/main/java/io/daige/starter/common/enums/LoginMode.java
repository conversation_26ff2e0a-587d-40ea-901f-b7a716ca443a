package io.daige.starter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 用户
 * </p>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LoginMode implements BaseEnum {

    Account("账户模式"),
    Tel("手机模式"),
    ;

    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static LoginMode match(String val, LoginMode def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static LoginMode get(String val) {
        return match(val, null);
    }

    public boolean eq(LoginMode val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
