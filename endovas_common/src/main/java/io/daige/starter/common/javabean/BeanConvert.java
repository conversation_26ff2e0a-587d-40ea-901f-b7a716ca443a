package io.daige.starter.common.javabean;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/8/27
 * Time: 下午2:44
 */
public interface BeanConvert<T, S> {
    default String[] skipList() {
        return new String[0];
    }

    default T convertFrom(S input) {
        BeanUtil.copyProperties(input, this, skipList());
        return (T) this;
    }

    default S convertTo(S input) {
        BeanUtil.copyProperties(this, input, skipList());
        return (S) this;
    }

    default T convertFrom(S input, boolean ignoreNullValue) {
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(ignoreNullValue).setIgnoreProperties(skipList());
        BeanUtil.copyProperties(input, this, copyOptions);
        return (T) this;
    }

    default S convertTo(S input, boolean ignoreNullValue) {
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(ignoreNullValue).setIgnoreProperties(skipList());
        BeanUtil.copyProperties(this, input, copyOptions);
        return (S) this;
    }


}

