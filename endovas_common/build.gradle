dependencies {
    api 'org.springframework.boot:spring-boot-starter-web'
    api 'org.springframework.boot:spring-boot-starter-security'
    api ('org.springframework.boot:spring-boot-starter-data-mongodb') {
        exclude(module:'spring-data-mongodb')
    }

    api 'org.springframework.boot:spring-boot-starter-data-jpa'
    api 'com.alibaba:druid-spring-boot-starter:1.1.20'
    api 'com.baomidou:mybatis-plus-boot-starter:3.4.0'

    api('mysql:mysql-connector-java:8.0.17') {
        exclude(module: 'protobuf-java')
    }
    api group: 'com.fasterxml.jackson.core', name: 'jackson-core', version: '2.12.0'
    api group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.12.0'
    api group: 'com.fasterxml.jackson.core', name: 'jackson-annotations', version: '2.12.0'
    api group: 'com.fasterxml.jackson.datatype', name: 'jackson-datatype-jsr310', version: '2.12.0'

    api 'io.daige.starter.component:redis-spring-boot-starter:0.0.5'

    api 'org.apache.httpcomponents:httpclient:4.5.14'
    api 'org.apache.httpcomponents:httpmime:4.5.14'

    api 'com.sun.mail:javax.mail:1.6.2'
    api 'org.jsoup:jsoup:1.18.3'
    api fileTree(dir: 'lib', includes: ['*jar']) // 驱动
}
