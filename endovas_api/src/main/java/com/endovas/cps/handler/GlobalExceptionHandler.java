package com.endovas.cps.handler;


import io.daige.starter.common.constant.CustomResultCodeConst;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.exception.HttpStatusResponseException;
import io.daige.starter.common.exception.NotLoginException;
import io.daige.starter.common.render.RenderJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局错误捕获
 * Created by IntelliJ IDEA.
 * User: frandy
 * Date: 19-5-31
 * Time: 下午2:30
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public String httpRequestMethodNotSupportedException(Exception e, HttpServletRequest request) {
        return RenderJson.fail("请求方式不正确");
    }


    @ExceptionHandler(value = Exception.class)
    public Object errorHandler(Exception e) {

        if (e instanceof NoHandlerFoundException) {
            return RenderJson.fail("您访问的接口不存在");
        }
        if (e instanceof NotLoginException) {
            return RenderJson.fail(e.getMessage(), CustomResultCodeConst.NOT_LOGIN);
        }

        if (e instanceof BusinessAssertException) {
            return RenderJson.fail(e.getMessage());
        }
        if (e instanceof HttpStatusResponseException) {
            HttpStatusResponseException x = (HttpStatusResponseException) e;
            return new ResponseEntity(e.getMessage(), HttpStatus.valueOf(x.getHttpStatus()));
        }
        if (e instanceof BindException) {
            return RenderJson.fail(((BindException) e).getFieldError().getDefaultMessage());
        }
        if (e instanceof MethodArgumentNotValidException) {
            return RenderJson.fail(((MethodArgumentNotValidException) e).getBindingResult().getFieldError().getDefaultMessage());
        }

        log.error(e.getMessage(), e);
        e.printStackTrace();
        return RenderJson.error("网络信号不佳，请稍后再试");
    }
}
