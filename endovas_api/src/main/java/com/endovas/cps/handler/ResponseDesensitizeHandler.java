package com.endovas.cps.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.annotation.desensitize.*;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.security.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.reflect.FieldUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * 返回值脱密处理器
 * 需要脱敏的话需要使用BaseResut返回值,不能使用String
 */
@Slf4j
@ControllerAdvice
public class ResponseDesensitizeHandler implements ResponseBodyAdvice {

    // 用于判断是否是公司自定义的类，基础类无需处理例如：返回 List<String> 其中 String 无需处理
    private static final String COMPANY_CLASS_PATH = "com.endovas";

    // 脱敏注解
    private static final List<Class> DESENSITIZE_ANNOTATION = Arrays.asList(
            DesensitizeTelephone.class,
            DesensitizeAddress.class,
            DesensitizeAttachment.class,
            DesensitizeBankCardNo.class,
            DesensitizeIdCardNo.class
    );

    // 特殊值隐藏注解
    private static final List<Class> HIDESPECIALDATA_ANNOTATION = Arrays.asList(
            HideSpecialData.class
    );


    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        try {
            Type baseResultType = returnType.getGenericParameterType();
            if (baseResultType instanceof ParameterizedType) {

                if (((ParameterizedType) baseResultType).getRawType().getTypeName().equals(BaseResult.class.getTypeName())) {
                    ParameterizedType responseType = (ParameterizedType) baseResultType;
                    Type[] responseTypeList = responseType.getActualTypeArguments();

                    for (Type rt : responseTypeList) {
                        if (rt instanceof ParameterizedType) {


                            if (((ParameterizedType) rt).getRawType().getTypeName().equals(PageVO.class.getTypeName())) {

                                Type[] pageTypeList = ((ParameterizedType) rt).getActualTypeArguments();
                                for (Type pt : pageTypeList) {
                                    if (check(pt)) {
                                        return true;
                                    }
                                }

                            } else if (((ParameterizedType) rt).getRawType().getTypeName().equals(List.class.getTypeName())) {

                                Type[] listTypeList = ((ParameterizedType) rt).getActualTypeArguments();
                                for (Type pt : listTypeList) {
                                    if (check(pt)) {
                                        return true;
                                    }
                                }
                            }
                        } else {
                            if (check(rt)) {
                                return true;
                            }
                        }
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }


    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {

        // 获取登录用户
        ServletServerHttpRequest r = (ServletServerHttpRequest) request;
        LoginUser loginVO = (LoginUser) r.getServletRequest().getAttribute(BizCommonConst.CURRENT_USER);

        if (loginVO == null) {
            return body;
        }

        // 判断是否需要脱敏处理
        if (Objects.nonNull(loginVO.getDesensitize()) && BooleanUtil.isTrue(loginVO.getDesensitize())) {
            handleBody(body, DESENSITIZE_ANNOTATION, loginVO);
        }

        // 特殊字段隐藏处理
        handleBody(body, HIDESPECIALDATA_ANNOTATION, loginVO);

        return body;
    }


    /**
     * 检查返回值是否包含需要处理的注解
     *
     * @param type
     * @return
     */
    private boolean check(Type type) {
        try {
            Field[] fields = Class.forName(type.getTypeName()).getDeclaredFields();
            for (Field f : fields) {

                if (f.getGenericType() instanceof ParameterizedType) {
                    Type[] t = ((ParameterizedType) f.getGenericType()).getActualTypeArguments();
                    if (t[0].getClass().getTypeName().contains(COMPANY_CLASS_PATH) && check(t[0])) {
                        return true;
                    }
                }

                Annotation[] alist = f.getDeclaredAnnotations();
                for (Annotation a : alist) {
                    if (DESENSITIZE_ANNOTATION.contains(a.annotationType())
                            || HIDESPECIALDATA_ANNOTATION.contains(a.annotationType())) {
                        return true;
                    }
                }
            }
            return false;
        } catch (ClassNotFoundException e) {
            return false;
        } finally {
        }
    }


    /**
     * 处理返回值数据
     *
     * @param body
     * @param annotationList
     */
    private void handleBody(Object body, List<Class> annotationList, LoginUser loginUser) {
        BaseResult result = (BaseResult) body;
        Object data = result.getResponse();
        if (data instanceof PageVO) {
            PageVO pageData = (PageVO) data;
            List originDataList = pageData.getContent();
            handleListData(originDataList, annotationList, loginUser);
        } else if (data instanceof List) {
            List originDataList = (List) data;
            handleListData(originDataList, annotationList, loginUser);
        } else {
            handleSingleData(data, annotationList, loginUser);
        }
    }


    /**
     * 处理列表数据
     *
     * @param originDataList
     * @return
     */
    private List handleListData(List originDataList, List<Class> annotationList, LoginUser loginUser) {
        if (CollectionUtil.isEmpty(originDataList)) {
            return originDataList;
        }
        for (Object o : originDataList) {
            if (o instanceof List) {
                handleListData((List) o, annotationList, loginUser);
            } else {
                handleSingleData(o, annotationList, loginUser);
            }
        }
        return originDataList;
    }

    /**
     * 处理单个数据
     *
     * @param originData
     * @return
     */
    private Object handleSingleData(Object originData, List<Class> annotationList, LoginUser loginUser) {
        try {

            if (Objects.isNull(originData)) {
                return originData;
            }

            Field[] fieldList = originData.getClass().getDeclaredFields();
            for (Field f : fieldList) {
                Annotation[] aArr = f.getDeclaredAnnotations();
                List<Annotation> aList = Arrays.asList(aArr);
                // 检查是否包含附件类型的注解，如果包含则不再继续递归
                boolean matchAttach = aList.stream().anyMatch(a -> !DesensitizeAttachment.class.equals(a.annotationType()));

                if (BaseVO.class.isAssignableFrom(f.getType())) {
                    handleSingleData(FieldUtils.readField(f, originData, true), annotationList, loginUser);
                } else if (List.class.isAssignableFrom(f.getType()) && !matchAttach) {
                    if (f.getGenericType() instanceof ParameterizedType) {
                        Type[] t = ((ParameterizedType) f.getGenericType()).getActualTypeArguments();
                        if (t[0].getTypeName().contains(COMPANY_CLASS_PATH) && check(t[0])) {
                            handleListData((List) FieldUtils.readField(f, originData, true), annotationList, loginUser);
                        }
                    }
                }

                for (Annotation a : aList) {
                    if (!annotationList.contains(a.annotationType())) {
                        continue;
                    }

                    if (a.annotationType().equals(DesensitizeTelephone.class)) {
                        Object val = FieldUtils.readField(f, originData, true);
                        String d = (String) val;
                        StringBuffer sb = new StringBuffer();
                        if (d != null && d.length() > 3) {
                            sb.append(d, 0, 3);
                            sb.append("*****");
                            sb.append(d, d.length() - 3, d.length());
                        }
                        FieldUtils.writeField(f, originData, sb.toString(), true);
                    }

                    if (a.annotationType().equals(DesensitizeAddress.class)) {
                        Object val = FieldUtils.readField(f, originData, true);
                        String d = (String) val;
                        StringBuffer sb = new StringBuffer();
                        if (d != null && d.length() > 10) {
                            sb.append(d, 0, 10);
                            sb.append("**********");
                        } else {
                            sb.append("**********");
                        }
                        FieldUtils.writeField(f, originData, sb.toString(), true);
                    }

                    if (a.annotationType().equals(DesensitizeAttachment.class)) {
                        // 注解在附件列表上，例如 List<AttachmentVO>
                        if (List.class.isAssignableFrom(f.getType())) {
                            List<AttachmentVO> valList = (List<AttachmentVO>) FieldUtils.readField(f, originData, true);
                            if (CollectionUtil.isNotEmpty(valList)) {
                                valList.forEach(v -> v.setUrl("/desensitize.png"));
                                FieldUtils.writeField(f, originData, valList, true);
                            }
                        }

                        if (AttachmentVO.class.equals(f.getType())) {
                            // 注解在 AttachmentVO 对象上
                            AttachmentVO val = (AttachmentVO) FieldUtils.readField(f, originData, true);
                            if (val != null) {
                                val.setUrl("/desensitize.png");
                                FieldUtils.writeField(f, originData, val, true);
                            }
                        }

                        if (String.class.equals(f.getType())) {
                            // 注解在 url 字符串上
                            String val = (String) FieldUtils.readField(f, originData, true);
                            if (StrUtil.isNotBlank(val)) {
                                FieldUtils.writeField(f, originData, "/desensitize.png", true);
                            }
                        }
                    }

                    if (a.annotationType().equals(DesensitizeBankCardNo.class)) {
                        Object val = FieldUtils.readField(f, originData, true);
                        String d = (String) val;
                        StringBuffer sb = new StringBuffer();
                        if (d != null && d.length() > 3) {
                            sb.append(d, 0, 3);
                            sb.append("**********");
                            sb.append(d, d.length() - 3, d.length());
                        }
                        FieldUtils.writeField(f, originData, sb.toString(), true);
                    }

                    if (a.annotationType().equals(DesensitizeIdCardNo.class)) {
                        Object val = FieldUtils.readField(f, originData, true);
                        String d = (String) val;
                        StringBuffer sb = new StringBuffer();
                        if (d != null && d.length() > 3) {
                            sb.append(d, 0, 3);
                            sb.append("********");
                            sb.append(d, d.length() - 3, d.length());
                        }
                        FieldUtils.writeField(f, originData, sb.toString(), true);
                    }

                    if (a.annotationType().equals(HideSpecialData.class)) {
                        List<String> specialFields = loginUser.getSpecialFields();
                        if (specialFields.contains(f.getName())) {
                            Object val = FieldUtils.readField(f, originData, true);
                            FieldUtils.writeField(f, originData, val, true);
                        } else {
                            FieldUtils.writeField(f, originData, null, true);
                        }
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error("处理脱密异常", e);
        } catch (Exception e) {
            log.error("处理脱密异常", e);
        }
        return originData;
    }


}
