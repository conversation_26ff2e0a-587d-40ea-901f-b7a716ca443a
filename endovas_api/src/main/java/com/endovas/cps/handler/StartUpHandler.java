package com.endovas.cps.handler;

import cn.hutool.crypto.SecureUtil;
import com.endovas.cps.config.properties.InitAccountProperties;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.RoleDAO;
import com.endovas.cps.dao.UserRoleDAO;
import com.endovas.cps.dao.measurement.MeasurementOperationDAO;
import com.endovas.cps.dao.platform.EquipmentDAO;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.UserRole;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.DefaultRoleCodeEnum;
import com.endovas.cps.enums.EquipmentStatusEnum;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.mailattach.NeteaseMailService;
import com.endovas.cps.service.mailattach.QQMailService;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.enums.BelongEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 * User: frandy
 * Date: 19-11-19
 * Time: 下午3:40
 */

@Component
@Order(1)
public class StartUpHandler implements ApplicationRunner {
    @Autowired
    PlatformUserDAO platformUserDAO;
    @Autowired
    private InitAccountProperties initAccountProperties;
    @Autowired
    private RoleDAO roleDAO;
    @Autowired
    private UserRoleDAO userRoleDAO;
    @Autowired
    private EquipmentDAO equipmentDAO;
    @Autowired
    private MeasurementOperationDAO measurementOperationDAO;

    @Autowired
    private QQMailService qqMailService;
    @Autowired
    private NeteaseMailService neteaseMailService;
    @Autowired
    private FileService fileService;


    @Override
    public void run(ApplicationArguments args) {
        //初始化用户信息和角色
        if (platformUserDAO.findAll().isEmpty()) {

            PlatformUser platformUser = new PlatformUser();
            String salt = PlatformUser.genSalt();
            platformUser.setSalt(salt);
            platformUser.setLastPasswordModifyTime(LocalDateTime.now());
            platformUser.setPassword(SecureUtil.sha256(initAccountProperties.getPassword() + salt));
            platformUser.setAccount(initAccountProperties.getAccount());
            platformUser.setTelephone(initAccountProperties.getTelephone());
            platformUser.setStatus(UserStatusEnum.NORMAL.getCode());
            platformUserDAO.save(platformUser);

            Role platformSuperAdmin = new Role();

            platformSuperAdmin.setId("245090556399591428");
            platformSuperAdmin.setName("平台超级管理员");
            platformSuperAdmin.setActiveStatus(true);
            platformSuperAdmin.setBelong(BelongEnum.PLATFORM_USER.getCode());
            platformSuperAdmin.setDataType(BizCommonConst.ROLE_DATA_TYPE_ALL);
            platformSuperAdmin.setCode(DefaultRoleCodeEnum.ROLE_NAME_PLATFORM_SUPER_ADMIN.getCode());
            roleDAO.save(platformSuperAdmin);



            Role medAgent = new Role();
            medAgent.setName("代理商管理员");
            medAgent.setBelong(BelongEnum.PLATFORM_USER.getCode());
            medAgent.setActiveStatus(true);
            medAgent.setDataType(BizCommonConst.ROLE_DATA_TYPE_ALL);
            medAgent.setCode(DefaultRoleCodeEnum.ROLE_NAME_MED_AGENT_ADMIN.getCode());
            roleDAO.save(medAgent);

            Role hospital = new Role();
            hospital.setName("医院管理员");
            hospital.setBelong(BelongEnum.PLATFORM_USER.getCode());
            hospital.setActiveStatus(true);
            hospital.setDataType(BizCommonConst.ROLE_DATA_TYPE_ALL);
            hospital.setCode(DefaultRoleCodeEnum.ROLE_NAME_HOSPITAL_ADMIN.getCode());
            roleDAO.save(medAgent);

            UserRole userRole = new UserRole();
            userRole.setRoleId(platformSuperAdmin.getId());
            userRole.setUserId(platformUser.getId());
            userRoleDAO.save(userRole);

        }

        /**
         * 单点部署，系统启动时，将所有设备的状态设置为空闲，并将所有测量任务的设备使用状态设置为否
         */
        equipmentDAO.updateEquipStatus(EquipmentStatusEnum.IDLE.getCode());
        measurementOperationDAO.updateIsUsingEquip(false);
    }
}
