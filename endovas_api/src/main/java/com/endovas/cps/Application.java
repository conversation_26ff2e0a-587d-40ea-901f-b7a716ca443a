package com.endovas.cps;

import io.daige.starter.component.jpa.annotation.EnableJpaSoftDeleteRepositories;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@SpringBootApplication
@EnableJpaAuditing
@EnableMongoAuditing
@EntityScan(basePackages = {"com.endovas.cps.entity","io.daige.starter.component.idGenerator.worker.entity"})
@EnableJpaRepositories(basePackages={"com.endovas.cps.dao"})
@EnableJpaSoftDeleteRepositories(basePackages = {"com.endovas.cps.dao"})
@EnableScheduling
@EnableAsync
public class Application {
	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

}
