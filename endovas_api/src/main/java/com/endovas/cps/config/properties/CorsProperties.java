package com.endovas.cps.config.properties;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/8/30
 * Time: 下午5:54
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cors")
public class CorsProperties {
    private String pathPattern="/**";
    private List<String> allowedOrigins = Lists.newArrayList();
    private List<String> allowedHeaders = Lists.newArrayList("*");
    private List<String> allowedMethods = Lists.newArrayList("GET", "POST", "PUT", "DELETE", "OPTIONS");
}
