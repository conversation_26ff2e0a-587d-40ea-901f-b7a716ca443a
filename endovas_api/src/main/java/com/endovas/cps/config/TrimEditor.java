package com.endovas.cps.config;

import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.support.WebBindingInitializer;

/**
 * Created by IntelliJ IDEA.
 * 去除表单提交时字符串的前后空格
 *
 * @author: bin.yu
 * Date: 2021/3/23
 * Time: 下午5:52
 */
@Configuration
@ControllerAdvice
public class TrimEditor implements WebBindingInitializer {

    @InitBinder
    @Override
    public void initBinder(WebDataBinder webDataBinder) {
        webDataBinder.registerCustomEditor(String.class, new StringTrimmerEditor(false));
    }
}
