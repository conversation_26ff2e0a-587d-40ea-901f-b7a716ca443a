package com.endovas.cps.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.pojo.dto.mailattach.MailAttachConfigDTO;
import com.endovas.cps.service.DictService;
import com.endovas.cps.service.mailattach.MailAttachService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @author: wk
 * @Date: 2025/2/6
 * @Time: 11:58
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FetchBigFileLinkJob {

    private final DictService dictService;
    private final String MAIL_ATTACH_CONFIG_KEY = "MAIL_ATTACH_CONFIG";
    private final MailAttachService mailAttachService;

    @Scheduled(cron = "0/30 * * * * ?")
    public void fetchBigFileLink() {

        // 获取邮件收取处理的配置
        String value = dictService.getOneParamValue(MAIL_ATTACH_CONFIG_KEY);
        if (Objects.isNull(value)) {
            log.error("邮件处理配置未完善");
            return;
        }
        List<MailAttachConfigDTO> configList = JSONUtil.toBean(value, new TypeReference<List<MailAttachConfigDTO>>(){}.getType(), true);
        if(CollectionUtil.isEmpty(configList)) {
            log.error("邮件处理配置未完善");
            return;
        }

        try {
            mailAttachService.extractBigFileLink(configList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

