package com.endovas.cps.job;

import com.endovas.cps.service.mailattach.MailAttachDownloadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class DownloadBigFileJob {
    private final MailAttachDownloadService mailAttachDownloadService;

    @Scheduled(cron = "0 */2 * * * ?") // 每2分钟执行一次
    public void downloadMailAttachments() {
        mailAttachDownloadService.processDownloadLinks();
    }
}