package com.endovas.cps.controller.callback;

import cn.hutool.json.JSONUtil;
import com.endovas.cps.service.attachment.FileService;
import com.google.common.collect.Maps;
import io.daige.starter.common.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 11:33
 */
@Slf4j
@RestController
@RequestMapping(Project.ANON + "/aliOss/callback")
@RequiredArgsConstructor
public class AliOssCallBackController {
    private final FileService fileService;

    @PostMapping("/process")
    public String process(HttpServletRequest request, HttpServletResponse response) throws IOException, ExecutionException, InterruptedException {
        String ossFilePath = request.getParameter("object");
        fileService.aliOssUploaded( ossFilePath);
        Map<String, String> result = Maps.newHashMap();
        result.put("status", "ok");
        return JSONUtil.toJsonStr(result);
    }
}
