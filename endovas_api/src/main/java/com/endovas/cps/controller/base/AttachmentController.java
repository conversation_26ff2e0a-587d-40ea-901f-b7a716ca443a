package com.endovas.cps.controller.base;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.endovas.cps.config.properties.GuacamoleProperties;
import com.endovas.cps.enums.ImgExtEnum;
import com.endovas.cps.pojo.dto.AttachmentUploadFileDTO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationDetailVO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.measurement.MeasurementOperationService;
import com.google.common.collect.Lists;
import io.daige.starter.common.Project;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.exception.HttpStatusResponseException;
import io.daige.starter.common.render.RenderJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

@RestController
@Api(tags = "上传接口")
@RequestMapping(Project.BASE + "/attachment")
@Slf4j
@RequiredArgsConstructor
public class AttachmentController {
    private final AttachmentService attachmentService;
    private final MeasurementOperationService measurementOperationService;
    private final GuacamoleProperties guacamoleProperties;
    private final List<String> filesExts = Lists.newArrayList("rar", "zip", "doc", "docx", "pdf", "png", "jpg");


    /**
     * @param file
     * @param id
     * @param targetId       要和附件绑定的业务ID,如果业务ID不存在,可以不传,但具体的业务接口逻辑中需要调用attachmentService.buildRelationByTargetId建立关联
     * @param attachmentType
     * @param orderCol
     * @return
     * @See com.daige.service.attachment.AttachmentService.class#buildRelationByTargetId
     */
    @PostMapping(value = "/uploadImage")
    @ApiOperation(value = "上传图片-带类型")
    public String uploadImage(MultipartFile file,
                              String id,
                              String targetId,
                              @ApiParam("附件类型 见枚举") String attachmentType,
                              Integer orderCol) {

        String ext = FileUtil.extName(file.getOriginalFilename());

        if (!ImgExtEnum.isValid(ext)) {
            return RenderJson.fail("请上传正确的文件格式 [JPG, JPEG, PNG，BMP]");
        }
        if (file.getSize() > BizCommonConst.FILE_MAX_SIZE) {
            throw new HttpStatusResponseException(500, "请上传小于5M的附件");
        }

        AttachmentUploadFileDTO attachmentUploadFileDTO = new AttachmentUploadFileDTO();
        attachmentUploadFileDTO.setId(id);
        attachmentUploadFileDTO.setAttachmentType(attachmentType);
        attachmentUploadFileDTO.setOrderCol(orderCol);
        attachmentUploadFileDTO.setTargetId(targetId);
        attachmentUploadFileDTO.setContentType(file.getContentType());
        attachmentUploadFileDTO.setBody(file);
        return RenderJson.success(attachmentService.uploadFile(attachmentUploadFileDTO));
    }

    @PostMapping(value = "/uploadFile")
    @ApiOperation(value = "上传文件")
    public String uploadFile(MultipartFile file,
                             String id,
                             String targetId,
                             @ApiParam("附件类型 见枚举") String attachmentType,
                             Integer orderCol) {

        String ext = FileUtil.extName(file.getOriginalFilename());
        if (StrUtil.isEmpty(ext)) {
            return RenderJson.fail("请正确上传文件");
        }
        ext = ext.toLowerCase();
        if (!filesExts.contains(ext)) {
            return RenderJson.fail("请上传正确的文件格式 [rar,zip,doc,docx,pdf,png,jpg]");
        }

        AttachmentUploadFileDTO attachmentUploadFileDTO = new AttachmentUploadFileDTO();
        attachmentUploadFileDTO.setId(id);
        attachmentUploadFileDTO.setAttachmentType(attachmentType);
        attachmentUploadFileDTO.setOrderCol(orderCol);
        attachmentUploadFileDTO.setTargetId(targetId);
        attachmentUploadFileDTO.setContentType(file.getContentType());
        attachmentUploadFileDTO.setBody(file);
        return RenderJson.success(attachmentService.uploadFile(attachmentUploadFileDTO));
    }

    @PostMapping(value = "/delFile")
    @ApiOperation(value = "删除文件")
    public String delFile(String id) {
        attachmentService.deleteFile(id);
        return RenderJson.success("删除成功");
    }

    /**
     * 获取测量操作录频
     * @param measurementOpId
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/viewMeasurementRecord/{measurementOpId}")
    public void viewMeasurementRecord(@PathVariable String measurementOpId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MeasurementOperationDetailVO vo = measurementOperationService.getRecord(measurementOpId);
        String filePath = guacamoleProperties.getScreenSaveDir();
        if (!guacamoleProperties.getScreenSaveDir().endsWith("/")) {
            filePath = filePath + "/";
        }
//        filePath = "/data/4207481183002435584-4275899462274326528.guac";
        filePath = filePath + vo.getScreenRecordFileName();

        File recordFile = new File(filePath);
        if (!recordFile.exists()) {
            throw new BusinessAssertException("操作录频已被清除");
        }
        String fileName = vo.getScreenRecordFileName();
        download(response, request.getHeader("range"), new FileInputStream(recordFile), "application/octet-stream", fileName, recordFile.length());
    }

    @GetMapping(value = "/view")
    public void view(String file, HttpServletRequest request, HttpServletResponse response) throws IOException {
        OSSObject ossObject = attachmentService.viewFile(file);
        ObjectMetadata objectMetadata = ossObject.getObjectMetadata();
        String fileName = FileUtil.getName(file);
        download(response, request.getHeader("range"), ossObject.getObjectContent(), objectMetadata.getContentType(), fileName, objectMetadata.getContentLength());
        ossObject.close();
    }

    private void download(HttpServletResponse response, String range, InputStream inputStream, String contentType, String fileName, long size) {
        ServletOutputStream outputStream = null;
        try {
            if (Objects.isNull(inputStream)) {
                throw new BusinessAssertException("文件不存在");
            }
            response.setContentType(contentType);
            response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("UTF-8")));
            response.setHeader("Content-Length", String.valueOf(size));

            if (contentType.contains("video")) {
                long partLen = 1024 * 1024 * 2;
                long start = Long.parseLong(range.substring(range.indexOf("=") + 1, range.indexOf("-")));
                long end = Math.min(start + partLen - 1, size - 1);
                response.setHeader("Accept-Ranges", "bytes");
                response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + size);

            }
            outputStream = response.getOutputStream();

            byte[] data = new byte[1024 * 1024];
            int len;
            while ((len = inputStream.read(data)) > -1) {
                outputStream.write(data, 0, len);
            }
        } catch (Exception e) {

        } finally {
            if (Objects.nonNull(outputStream)) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (Exception e) {

                }
            }
            if (Objects.nonNull(inputStream)) {
                try {
                    inputStream.close();
                } catch (Exception e) {

                }
            }
        }

    }


}


