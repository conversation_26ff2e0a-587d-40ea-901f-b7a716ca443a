package com.endovas.cps.controller.base;

import com.endovas.cps.pojo.vo.AliStsTokenVO;
import com.endovas.cps.service.attachment.FileService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 13:31
 */
@RestController
@Api(tags = "上传接口(阿里直传)")
@RequestMapping(Project.BASE + "/attachment")
@Slf4j
@RequiredArgsConstructor
public class AliFileController {
    private final FileService fileService;

    @ApiOperation(value = "获取aliOss上传用的临时token")
    @PostMapping("/getStsToken")
    public BaseResult<AliStsTokenVO> aliOssStsToken() throws Exception {
        return Render.success(fileService.getAliStsToken());
    }
}
