package com.endovas.cps.controller.base;

import com.endovas.cps.pojo.vo.AnnouncementVO;
import com.endovas.cps.service.announcement.AnnouncementService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:18
 */
@RestController
@Api(tags = "公告相关接口")
@RequestMapping(Project.BASE + "/announcement")
@Slf4j
@RequiredArgsConstructor
public class AnnouncementController {
    private final AnnouncementService announcementService;

    @GetMapping("/list")
    public BaseResult<PageVO<AnnouncementVO>> list(PageFO input) {
        return Render.success(announcementService.list(input));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "查看详情", response = AnnouncementVO.class)
    public String detail(String id) {
        return RenderJson.success(announcementService.detail(id));
    }

}
