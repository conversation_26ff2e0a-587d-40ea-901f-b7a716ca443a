server:
  port: 8888
  servlet:
    context-path: /cps/
  tomcat:
    uri-encoding: UTF-8
    max-swallow-size: 200MB
    max-http-form-post-size: 200MB

swagger2:
  docket:
    base:
      base-package: com.endovas.cps.controller.base
      title: 公共接口
    platform:
      base-package: com.endovas.cps.controller.web.platform
      title: 平台接口
  enabled: true

logging:
  level:
    org.springframework.data.mongodb.core: ERROR

spring:
  datasource:
    username: root
    password: chengmi888
    url: jdbc:mysql://*************:3308/endovas_cps?useLegacyDatetimeCode=false&serverTimezone=GMT%2b8&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&autoReconnect=true&useSSL=false&nullNamePatternMatchesAll=true
  data:
    mongodb:
      host: *************
      port: 27017
      database: endovas_cps
  jpa:
    #展示sql，正式环境 false
    show-sql: true
    # 自动生成表结构 正式环境 false
    generate-ddl: true
    hibernate:
      ddl-auto: update
  redis:
    database: 12
    host: *************
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: true
      static-locations: classpath:/static/,file:/tmp/
  servlet:
    multipart:
      max-file-size: 2GB
      max-request-size: 20GB


mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

sms:
  end-point: ecs.cn-hangzhou.aliyuncs.com
  access-key: LTAIdElG08VXaJF6
  access-key-secret: ******************************
  sign-name: 玳鸽科技
  fake-action: true


captcha:
  #  短信消息关闭，生产环境开启
  message-enabled: false

init:
  account: admin
  password: admin
  telephone: ***********

file:
  oss:
    domain: http://swyqqu.natappfree.cc
    call-back-domain: http://**************:6320
    api-end-point: oss-cn-shanghai-internal.aliyuncs.com
    download-end-point: oss-cn-shanghai.aliyuncs.com
    access-key: LTAI5t8TXJdqywcqiBfxNHi1
    access-key-secret: ******************************
    bucket-name: orth-dicom-test
    tmp-dir: /tmp/
    # 生产配置
    # bucket-name: endovas
  sts:
    end-point: sts.cn-shanghai.aliyuncs.com
    access-key: LTAI5tS6KFMgnfsrptoaLHqC
    access-key-secret: ******************************
    role-arn: acs:ram::****************:role/orth-cloud-oss
    role-session-name: orth-cloud-oss

token:
  token-expire-time: 1440

guacamole:
  host: *************
  port: 4822
  screen-save-dir: /mnt

filesync:
  # 文件同步服务端口
  service-port: 8079
  # 文件同步服务接口
  api-url: /endovasFileSync/upload