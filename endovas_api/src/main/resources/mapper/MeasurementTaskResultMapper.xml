<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.endovas.cps.dao.measurement.mapper.MeasurementTaskResultMapper">

    <resultMap id="BaseResultMap" type="com.endovas.cps.entity.measurement.MeasurementTaskResult">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creatorId" column="creator_id" jdbcType="VARCHAR"/>
            <result property="del" column="is_del" jdbcType="BIT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="measurementId" column="measurement_id" jdbcType="VARCHAR"/>
            <result property="sourceType" column="source_type" jdbcType="VARCHAR"/>
            <result property="resultFileName" column="result_file_name" jdbcType="VARCHAR"/>
            <result property="resultFileHash" column="result_file_hash" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,creator_id,
        is_del,update_time,updater_id,
        version,measurement_id,source_type,
        result_file_name,result_file_hash,result
    </sql>

    <select id="findListWithoutResultByMeasurementId" resultType="com.endovas.cps.pojo.dto.measurement.MeasurementTaskResultDTO">
        SELECT
            id, measurement_id, update_time,source_type ,result_file_name, create_time
        FROM
            t_measurement_task_result
        where is_del is false
          and measurement_id = #{measurementId}
        ORDER BY create_time desc
    </select>

</mapper>
