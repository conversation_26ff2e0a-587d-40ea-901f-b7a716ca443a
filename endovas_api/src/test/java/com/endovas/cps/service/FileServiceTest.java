package com.endovas.cps.service;

import com.endovas.cps.Application;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.measurement.impl.MeasurementOperationServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 16:55
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT) //配置启动类
public class FileServiceTest {

    @Autowired
    private  FileService fileService;
    @Autowired
    private MeasurementOperationServiceImpl measurementOperationServiceImpl;

    @Test
    public void aliOssUploadedTest() throws IOException, ExecutionException, InterruptedException {
//        fileService.aliOssUploaded( "DICOM/2024-11-11/4249302653954965557/DICOM_ee0a05df8e8e4bc6be795b1c1aa96b18.zip"); //gbk编码
//        fileService.aliOssUploaded( "DICOM/2024-11-11/4249087287114833938/DICOM_25fa0c16b00c4fa3a3bf12451a65ab29.rar");
        fileService.aliOssUploaded( "DICOM/2024-11-11/4249302653954965553/DICOM_f703b1cac50f40ff993fef1fc020384d.rar");
    }

    @Test
    public void fileSyncTest() {
        measurementOperationServiceImpl.uploadData(null, "*************", "CL_1234565", "brave");
    }

}
