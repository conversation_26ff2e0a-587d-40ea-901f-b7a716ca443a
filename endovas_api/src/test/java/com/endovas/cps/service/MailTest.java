package com.endovas.cps.service;

import com.endovas.cps.Application;
import com.endovas.cps.job.FetchBigFileLinkJob;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: wk
 * @Date: 2025/2/6
 * @Time: 15:19
 */
@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT) //配置启动类
public class MailTest {
    @Autowired
    private FetchBigFileLinkJob fetchBigFileLinkJob;

    @Test
    public void getMailTest() {
    }
}
