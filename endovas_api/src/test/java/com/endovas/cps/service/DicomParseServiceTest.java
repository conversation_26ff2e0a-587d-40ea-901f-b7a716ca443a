package com.endovas.cps.service;

import com.endovas.cps.Application;
import com.endovas.cps.service.measurement.MeasurementResultService;
import com.endovas.cps.service.resource.DicomParseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: wk
 * @Date: 2024/11/8
 * @Time: 17:26
 */
@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//配置启动类
public class DicomParseServiceTest {

    @Autowired
    private DicomParseService dicomParseService;
    @Autowired
    private MeasurementResultService measurementResultService;

    @Test
    public void test() {
        System.out.println("application/pdf".contains("application"));
//        LoginUser loginUser = new LoginUser();
//        loginUser.setId("4207481183002435584");
//        loginUser.setBelong(BelongEnum.PLATFORM_USER);
//        System.out.println(JSONUtil.toJsonStr(measurementResultService.listResultByPatientId("5020968707792330757", loginUser)));


//        try {
//            String descDir = "/Users/<USER>/file/tempfile/";
//            Attachment att = new Attachment();
//            att.setId("123");
//            att.setType("DICOM");
//
//            dicomParseService.process(100L,descDir, att);
//        } catch (ExecutionException e) {
//            e.printStackTrace();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

}
