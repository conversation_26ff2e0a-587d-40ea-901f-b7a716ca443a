<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="clean" type="GradleRunConfiguration" factoryName="Gradle">
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/endovas_api" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="clean" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list />
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <method v="2" />
  </configuration>
</component>