package com.endovas.cps.service.communication.sms;

import io.daige.starter.plugin.sms.service.AliyunSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/23
 * Time: 下午3:28
 */
@Slf4j
@Component
public class SMSApiRequest {
    private final String CAPTCHA_TMPL_CODE = "SMS_284120066";

    @Autowired
    private AliyunSmsService aliyunSmsService;

    public void sendCaptcha(String telephone, String content, String businessScope) {
        Map<String, String> args = new HashMap<>();
        args.put("code", content);
        aliyunSmsService.smsSend(telephone, CAPTCHA_TMPL_CODE, args);
    }



}
