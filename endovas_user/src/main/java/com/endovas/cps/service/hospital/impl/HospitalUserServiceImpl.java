package com.endovas.cps.service.hospital.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.endovas.cps.dao.HospitalUserDAO;
import com.endovas.cps.entity.user.HospitalUser;
import com.endovas.cps.enums.DefaultRoleCodeEnum;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.fo.hospital.HospitalUserAddByPlatformFO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserEditByPlatformFO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserSearchByPlatformFO;
import com.endovas.cps.pojo.vo.hospital.HospitalUserListVO;
import com.endovas.cps.pojo.vo.hospital.HospitalUserSelectVO;
import com.endovas.cps.service.hospital.HospitalUserService;
import com.endovas.cps.service.role.RoleService;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HospitalUserServiceImpl implements HospitalUserService {
    private final HospitalUserDAO hospitalUserDAO;
    private final RedisHelper redisHelper;
    private final RoleService roleService;

    @Override
    public PageVO<HospitalUserListVO> list(HospitalUserSearchByPlatformFO searchFO, List<String> hospitalIds, PageFO pageFO) {
        Specification<HospitalUser> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            if (StrUtil.isNotEmpty(searchFO.getHospitalId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(HospitalUser.HOSPITAL_ID), searchFO.getHospitalId());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getHospitalName())) {
                CriteriaBuilder.In<String> inClause = criteriaBuilder.in(root.get(HospitalUser.HOSPITAL_ID));
                hospitalIds.forEach(inClause::value);
                list.add(inClause);
            }

            if (StrUtil.isNotEmpty(searchFO.getNickName())) {
                Predicate p1 = criteriaBuilder.like(root.get(HospitalUser.NICK_NAME), "%" + searchFO.getNickName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getAccount())) {
                Predicate p1 = criteriaBuilder.equal(root.get(HospitalUser.ACCOUNT), searchFO.getAccount());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getTelephone())) {
                Predicate p1 = criteriaBuilder.like(root.get(HospitalUser.TELEPHONE), "%" + searchFO.getTelephone() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(HospitalUser.STATUS), searchFO.getStatus());
                list.add(p1);
            }

            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };


        Page<HospitalUserListVO> list = hospitalUserDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> new HospitalUserListVO().convertFrom(x));
        return PageUtil.convert(list);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(HospitalUserAddByPlatformFO input) {
        checkUser(StrUtil.EMPTY, input.getAccount(), input.getTelephone(), input.getHospitalId());
        HospitalUser hospitalUser = new HospitalUser();
        input.convertTo(hospitalUser);
        hospitalUser.setLastPasswordModifyTime(LocalDateTime.now());

        hospitalUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(input.getTelephone(), 6);
        hospitalUser.setPassword(SecureUtil.sha256(last6Bit + hospitalUser.getSalt()));
        hospitalUser.setStatus(UserStatusEnum.NORMAL.getCode());
        hospitalUserDAO.save(hospitalUser);
        roleService.bindRoleByPlatform(hospitalUser.getId(), DefaultRoleCodeEnum.ROLE_NAME_HOSPITAL_ADMIN);

    }

    @Override
    public void edit(HospitalUserEditByPlatformFO input) {
        HospitalUser hospitalUser = hospitalUserDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("医院账户信息不存在"));
        checkUser(input.getId(), input.getAccount(), input.getTelephone(), hospitalUser.getHospitalId());
        input.convertTo(hospitalUser);
        hospitalUserDAO.save(hospitalUser);
    }

    private void checkUser(String id, String account, String telephone, String medAgentId) {
        HospitalUser existAccount = hospitalUserDAO.getByAccountAndHospitalId(account, medAgentId);
        if (Objects.nonNull(existAccount) && !existAccount.getId().equals(id)) {
            throw new BusinessAssertException("账户已存在");
        }
        HospitalUser existTelephone = hospitalUserDAO.getByTelephoneAndHospitalId(telephone, medAgentId);
        if (Objects.nonNull(existTelephone) && !existTelephone.getId().equals(id)) {
            throw new BusinessAssertException("手机号已存在");
        }
    }

    @Override
    public void initPass(String id) {
        HospitalUser hospitalUser = hospitalUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("医院账户信息不存在"));
        hospitalUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(hospitalUser.getTelephone(), 6);
        hospitalUser.setPassword(SecureUtil.sha256(last6Bit + hospitalUser.getSalt()));
        hospitalUserDAO.save(hospitalUser);
    }

    @Override
    public void closeOpenAccount(String id) {
        HospitalUser hospitalUser = hospitalUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("医院账户信息不存在"));
        if (UserStatusEnum.NORMAL.getCode().equals(hospitalUser.getStatus())) {
            hospitalUser.setStatus(UserStatusEnum.CLOSE.getCode());
            // 禁用，强制退出
            String token = redisHelper.strGet(RedisCacheKeys.getTokenKey(id));
            redisHelper.delKey(RedisCacheKeys.getTokenKey(id));
            redisHelper.delKey(RedisCacheKeys.getTokenPrefixKey(token));
        } else if (UserStatusEnum.CLOSE.getCode().equals(hospitalUser.getStatus())) {
            hospitalUser.setStatus(UserStatusEnum.NORMAL.getCode());
        }
        hospitalUserDAO.save(hospitalUser);
    }

    @Override
    public void batchDelete(String hospitalId) {
        List<HospitalUser> hospitalUsers = hospitalUserDAO.findByHospitalId(hospitalId);
        hospitalUserDAO.deleteAll(hospitalUsers);
    }

    @Override
    public long count(String hospitalId) {
        return hospitalUserDAO.countByHospitalId(hospitalId);
    }

    @Override
    public List<HospitalUserSelectVO> select(String hospitalId) {
        return hospitalUserDAO.findByHospitalId(hospitalId).stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x->{
            HospitalUserSelectVO one = new HospitalUserSelectVO();
            one.setId(x.getId());
            one.setName(x.getNickName());
            return one;
        }).collect(Collectors.toList());
    }
}
