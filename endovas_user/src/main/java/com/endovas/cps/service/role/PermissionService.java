package com.endovas.cps.service.role;


import com.endovas.cps.entity.Permission;
import com.endovas.cps.pojo.vo.ButtonVO;
import com.endovas.cps.pojo.vo.MenuTreeVO;
import io.daige.starter.common.enums.BelongEnum;

import java.util.List;

/**
 * 权限接口
 */
public interface PermissionService {

    /**
         * 加载权限表中所有操作请求权限
         * <p>
         * 初始化资源 ,提取系统中的所有权限，加载所有url和权限（或角色）的对应关系，  web容器启动就会执行
         * 如果启动@PostConstruct 注解   则web容器启动就会执行
         */
    void loadResourceDefine();

    /**
     * 通过用户id获取
     *
     * @param userId
     * @return
     */
    List<Permission> findByUserId(String userId,BelongEnum belong);

    /**
     * 通过用户id获取菜单
     *
     * @param userId
     * @return
     */
    List<Permission> findMenuByUserId(String userId,BelongEnum belong);

    /**
     * 通过用户id获取按钮
     *
     * @param userId
     * @return
     */
    List<ButtonVO> findBtnByUserId(String userId,BelongEnum belong);

    /**
     * 通过层级查找
     * 默认升序
     *
     * @return
     */
    Permission getRoot();

    /**
     * 通过parendId查找
     *
     * @param parentId
     * @return
     */
    List<Permission> findByParentIdOrderBySortOrder(String parentId);

    /**
     * 通过状态获取
     *
     * @param available
     * @return
     */
    List<Permission> findByAvailableOrderBySortOrder(Boolean available);

    /**
     * 通过类型和状态获取
     *
     * @param type
     * @param available
     * @return
     */
    List<Permission> findByTypeAndAvailableOrderBySortOrder(Integer type, Boolean available);

    /**
     * 通过名称获取
     *
     * @param code
     * @return
     */
    List<Permission> findByCodeAndBelong(String code,String belong);


    /**
     * 通过parendId查找
     *
     * @param parentIds
     * @return
     */
    List<Permission> findByParentIdIn(List<String> parentIds);



    void deleteById(String id);






    /**
     * 获取当前用户需要敏感处理的字段
     */
    List<String> findCurrentUserFields(List<String> permissionCodes,String userId,BelongEnum belong);

    /**
     * 获取当前用户菜单以及按钮
     */
    MenuTreeVO findCurrentUserMenusAndBtns(List<String> permissionCodes, String userId, BelongEnum belong);



}
