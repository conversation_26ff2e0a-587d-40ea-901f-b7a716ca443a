package com.endovas.cps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.SecureUtil;
import com.endovas.cps.dao.*;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.user.*;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.dto.WebLoginUserDTO;
import com.endovas.cps.pojo.fo.UserPassLoginFO;
import com.endovas.cps.pojo.vo.UserSelectByAccountVO;
import com.endovas.cps.pojo.vo.UserSelectByIdVO;
import com.endovas.cps.service.UserService;
import com.endovas.cps.service.role.PermissionService;
import com.endovas.cps.service.role.UserRoleService;
import com.google.common.collect.Lists;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * 包含代理商、器械厂商的登录逻辑
 *
 * @author: bin.yu
 * Date: 2023/12/5
 * Time: 15:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    private final MedAgentUserDAO medAgentUserDAO;
    private final UserRoleService userRoleService;
    private final PermissionService permissionService;
    private final HospitalUserDAO hospitalUserDAO;
    private final ExpertDAO expertDAO;
    private final PlatformUserDAO platformUserDAO;


    @Override
    public WebLoginUserDTO getByAccount(String account, String belong, String enterpriseId) {
        WebLoginUserDTO result = new WebLoginUserDTO();
        result.setBelong(BelongEnum.valueOf(belong));
        switch (BelongEnum.valueOf(belong)) {
            case MED_AGENT_USER: {
                MedAgentUser medAgentUser = medAgentUserDAO.getByAccountAndMedAgentId(account, enterpriseId);
                BeanUtil.copyProperties(medAgentUser, result);
                break;

            }

            case HOSPITAL_USER: {
                HospitalUser hospitalUser = hospitalUserDAO.getByAccountAndHospitalId(account, enterpriseId);
                BeanUtil.copyProperties(hospitalUser, result);
                break;
            }
        }

        // 关联角色
        List<Role> roleList = userRoleService.findByUserId(result.getId());
        result.setRoles(roleList);
        // 关联权限菜单
        List<Permission> permissionList = permissionService.findByUserId(result.getId(), BelongEnum.valueOf(belong));
        result.setPermissions(permissionList);
        return result;
    }

    @Override
    public void resetPassword(String tel, String belong, String enterpriseId, String password) {
        switch (BelongEnum.valueOf(belong)) {
            case MED_AGENT_USER: {
                MedAgentUser medAgentUser = medAgentUserDAO.getByTelephoneAndMedAgentId(tel, enterpriseId);
                BizAssert.notNull(medAgentUser, "用户不存在");
                String defPassword = SecureUtil.sha256(password + medAgentUser.getSalt());
                medAgentUser.setLastPasswordModifyTime(LocalDateTime.now());
                medAgentUser.setPassword(defPassword);
                medAgentUserDAO.save(medAgentUser);
            }

            case HOSPITAL_USER: {
                HospitalUser hospitalUser = hospitalUserDAO.getByTelephoneAndHospitalId(tel, enterpriseId);
                BizAssert.notNull(hospitalUser, "用户不存在");
                String defPassword = SecureUtil.sha256(password + hospitalUser.getSalt());
                hospitalUser.setLastPasswordModifyTime(LocalDateTime.now());
                hospitalUser.setPassword(defPassword);
                hospitalUserDAO.save(hospitalUser);
            }
            case EXPERT: {
                Expert expert = expertDAO.getByTelephone(tel);
                BizAssert.notNull(expert, "用户不存在");
                String defPassword = SecureUtil.sha256(password + expert.getSalt());
                expert.setPassword(defPassword);
                expertDAO.save(expert);
            }
        }
    }

    @Override
    public WebLoginUserDTO getUser(LoginUser loginUser) {
        WebLoginUserDTO result = new WebLoginUserDTO();
        result.setBelong(loginUser.getBelong());
        switch (loginUser.getBelong()) {
            case MED_AGENT_USER: {
                medAgentUserDAO.findById(loginUser.getId()).ifPresent(medAgentUser -> {
                    BeanUtil.copyProperties(medAgentUser, result);
                });
                break;

            }


            case HOSPITAL_USER: {
                hospitalUserDAO.findById(loginUser.getId()).ifPresent(hospitalUser -> {
                    BeanUtil.copyProperties(hospitalUser, result);
                });
                break;
            }
            case EXPERT: {
                expertDAO.findById(loginUser.getId()).ifPresent(expert -> {
                    BeanUtil.copyProperties(expert, result);
                });

                break;
            }
        }

        // 关联角色
        List<Role> roleList = userRoleService.findByUserId(result.getId());
        result.setRoles(roleList);
        // 关联权限菜单
        List<Permission> permissionList = permissionService.findByUserId(result.getId(), loginUser.getBelong());
        result.setPermissions(permissionList);
        return result;
    }

    @Override
    public boolean check(UserPassLoginFO input) {
        switch (BelongEnum.valueOf(input.getBelong())) {
            case MED_AGENT_USER: {
                MedAgentUser medAgentUser = medAgentUserDAO.getByAccountAndMedAgentId(input.getAccount(),
                        input.getEnterpriseId());
                return Objects.nonNull(medAgentUser);
            }
            case HOSPITAL_USER: {
                HospitalUser hospitalUser = hospitalUserDAO.getByAccountAndHospitalId(input.getAccount(),
                        input.getEnterpriseId());
                return Objects.nonNull(hospitalUser);
            }
        }
        return false;
    }

    @Override
    public boolean checkByTel(String tel, String belong, String enterpriseId) {
        switch (BelongEnum.valueOf(belong)) {
            case MED_AGENT_USER: {
                MedAgentUser medAgentUser = medAgentUserDAO.getByTelephoneAndMedAgentId(tel, enterpriseId);
                return Objects.nonNull(medAgentUser);
            }
            case HOSPITAL_USER: {
                HospitalUser hospitalUser = hospitalUserDAO.getByTelephoneAndHospitalId(tel, enterpriseId);
                return Objects.nonNull(hospitalUser);
            }
        }
        return false;
    }

    @Override
    public List<UserSelectByAccountVO> accountSelect(String hospitalId, String medAgentId, String belong) {
        switch (BelongEnum.valueOf(belong)) {
            case HOSPITAL_USER: {
                return hospitalUserDAO.findByHospitalId(hospitalId).stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
                    UserSelectByAccountVO one = new UserSelectByAccountVO();
                    one.setAccount(x.getAccount());
                    one.setName(x.getNickName());
                    return one;

                }).collect(Collectors.toList());
            }
            case MED_AGENT_USER: {
                return medAgentUserDAO.findByMedAgentIdAndOrganizationIdIsNotNull(medAgentId).stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
                    UserSelectByAccountVO one = new UserSelectByAccountVO();
                    one.setAccount(x.getAccount());
                    one.setName(x.getNickName());
                    return one;
                }).collect(Collectors.toList());
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public void updateLoginTime(WebLoginUserDTO webLoginUser) {
        switch (webLoginUser.getBelong()) {
            case MED_AGENT_USER: {
                MedAgentUser medAgentUser =
                        medAgentUserDAO.findById(webLoginUser.getId()).orElseThrow(() -> new BusinessAssertException(
                                "用户不存在"));
                medAgentUser.setLastLoginTime(LocalDateTime.now());
                medAgentUserDAO.save(medAgentUser);
                break;
            }
            case HOSPITAL_USER: {
                HospitalUser hospitalUser =
                        hospitalUserDAO.findById(webLoginUser.getId()).orElseThrow(() -> new BusinessAssertException(
                                "用户不存在"));
                hospitalUser.setLastLoginTime(LocalDateTime.now());
                hospitalUserDAO.save(hospitalUser);
                break;
            }
            case EXPERT: {
                Expert expert =
                        expertDAO.findById(webLoginUser.getId()).orElseThrow(() -> new BusinessAssertException(
                                "用户不存在"));
                expert.setLastLoginTime(LocalDateTime.now());
                expertDAO.save(expert);
                break;

            }

        }
    }

    @Override
    public List<UserSelectByIdVO> select(String belong) {
        switch (BelongEnum.valueOf(belong)) {
            case PLATFORM_USER:
                return platformUserDAO.findAll().stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
                    UserSelectByIdVO one = new UserSelectByIdVO();
                    one.setId(x.getId());
                    one.setNickName(x.getNickName());
                    return one;

                }).collect(Collectors.toList());
            case EXPERT: {
                return expertDAO.findAll().stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
                    UserSelectByIdVO one = new UserSelectByIdVO();
                    one.setId(x.getId());
                    one.setNickName(x.getNickName());
                    return one;

                }).collect(Collectors.toList());
            }
            case HOSPITAL_USER: {
                return hospitalUserDAO.findAll().stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
                    UserSelectByIdVO one = new UserSelectByIdVO();
                    one.setId(x.getId());
                    one.setNickName(x.getNickName());
                    return one;

                }).collect(Collectors.toList());
            }
            case MED_AGENT_USER: {
                return medAgentUserDAO.findAll().stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
                    UserSelectByIdVO one = new UserSelectByIdVO();
                    one.setId(x.getId());
                    one.setNickName(x.getNickName());
                    return one;

                }).collect(Collectors.toList());
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public String getNickName(String userId, String belong) {
        String nickName = null;
        switch (BelongEnum.valueOf(belong)) {
            case PLATFORM_USER:
                nickName = platformUserDAO.findById(userId).map(PlatformUser::getNickName).orElse(null);
                break;
            case EXPERT: {
                nickName = expertDAO.findById(userId).map(Expert::getNickName).orElse(null);
                break;
            }
            case HOSPITAL_USER:
                nickName = hospitalUserDAO.findById(userId).map(HospitalUser::getNickName).orElse(null);
                break;
            case MED_AGENT_USER:
                nickName = medAgentUserDAO.findById(userId).map(MedAgentUser::getNickName).orElse(null);
                break;
        }
        return nickName;
    }

    @Override
    public List<String> getUserIds(String nickName) {
        List<String> expertIds =
                expertDAO.findByNickNameContaining(nickName).stream().map(MysqlBase::getId).collect(Collectors.toList());
        List<String> hospitalUserIds =
                hospitalUserDAO.findByNickNameContaining(nickName).stream().map(MysqlBase::getId).collect(Collectors.toList());
        List<String> medAgentUserIds =
                medAgentUserDAO.findByNickNameContaining(nickName).stream().map(MysqlBase::getId).collect(Collectors.toList());
        return CollUtil.unionAll(expertIds, hospitalUserIds, medAgentUserIds);
    }

    @Override
    public String getNickName(String userId) {
        return platformUserDAO.findById(userId).map(PlatformUser::getNickName).orElse(null);
    }
}
