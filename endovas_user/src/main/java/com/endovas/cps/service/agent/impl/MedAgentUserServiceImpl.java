package com.endovas.cps.service.agent.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.endovas.cps.dao.MedAgentUserDAO;
import com.endovas.cps.dao.OrganizationDAO;
import com.endovas.cps.entity.Organization;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.user.MedAgentUser;
import com.endovas.cps.enums.DefaultRoleCodeEnum;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.fo.agent.*;
import com.endovas.cps.pojo.vo.agent.MedAgentUserListByPlatformVO;
import com.endovas.cps.pojo.vo.agent.MedAgentUserListVO;
import com.endovas.cps.pojo.vo.agent.MedAgentUserSelectVO;
import com.endovas.cps.service.agent.MedAgentUserService;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.role.PermissionService;
import com.endovas.cps.service.role.RoleService;
import com.endovas.cps.service.role.UserRoleService;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MedAgentUserServiceImpl implements MedAgentUserService {
    private final MedAgentUserDAO medAgentUserDAO;
    private final RoleService roleService;
    private final RedisHelper redisHelper;
    private final UserRoleService userRoleService;
    private final OrganizationService organizationService;
    private final OrganizationDAO organizationDAO;
    private final PermissionService permissionService;


    @Override
    public PageVO<MedAgentUserListByPlatformVO> list(MedAgentUserSearchByPlatformFO searchFO, List<String> medAgentIds, PageFO pageFO) {
        Specification<MedAgentUser> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StrUtil.isNotEmpty(searchFO.getMedAgentName())) {
                CriteriaBuilder.In<String> inClause = criteriaBuilder.in(root.get(MedAgentUser.MED_AGENT_ID));
                medAgentIds.forEach(inClause::value);
                list.add(inClause);
            }

            if (StrUtil.isNotEmpty(searchFO.getMedAgentId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MedAgentUser.MED_AGENT_ID), searchFO.getMedAgentId());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getNickName())) {
                Predicate p1 = criteriaBuilder.like(root.get(MedAgentUser.NICK_NAME), "%" + searchFO.getNickName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getAccount())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MedAgentUser.ACCOUNT), searchFO.getAccount());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getTelephone())) {
                Predicate p1 = criteriaBuilder.like(root.get(MedAgentUser.TELEPHONE), "%" + searchFO.getTelephone() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MedAgentUser.STATUS), searchFO.getStatus());
                list.add(p1);
            }

            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };


        Page<MedAgentUserListByPlatformVO> list = medAgentUserDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> new MedAgentUserListByPlatformVO().convertFrom(x));
        return PageUtil.convert(list);
    }

    /**
     * 获取用户当前组织下的所有用户
     *
     * @param orgId
     * @return
     */
    @Override
    public List<MedAgentUserListVO> list(String orgId) {
        // 查询出下级组织
        List<Organization> oList = organizationService.recursiveFindByOrganizationId(orgId);
        List<String> currentAndSubOrgIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(oList)) {
            currentAndSubOrgIdList.addAll(oList.stream().map(Organization::getId).collect(Collectors.toList()));
        }

        // 补充当前组织
        currentAndSubOrgIdList.add(orgId);
        if (StrUtil.isNotEmpty(orgId)) {
            Optional<Organization> organization = organizationDAO.findById(orgId);
            if (organization.isPresent()) {
                oList.add(organization.get());
            }
        }


        Map<String, String> orgNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(oList)) {
            orgNameMap = oList.stream().collect(Collectors.toMap(Organization::getId, Organization::getName, (key1, key2) -> key2));
        }

        // 根据组织id，查询出所有的用户id
        List<MedAgentUserListVO> voList = new ArrayList<>();
        List<MedAgentUser> userDataList = medAgentUserDAO.findByOrganizationIdIn(currentAndSubOrgIdList);
        if (CollectionUtil.isNotEmpty(userDataList)) {
            for (MedAgentUser user : userDataList) {
                MedAgentUserListVO one = new MedAgentUserListVO().convertFrom(user);
                String result = one.getNickName() +
                        "(" +
                        orgNameMap.get(user.getOrganizationId()) +
                        ")";
                one.setNickName(result);
                voList.add(one);
            }
        }

        return voList;
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(MedAgentUserAddByPlatformFO input) {
        checkUser(StrUtil.EMPTY, input.getAccount(), input.getTelephone(), input.getMedAgentId());
        MedAgentUser medAgentUser = new MedAgentUser();
        input.convertTo(medAgentUser);
        medAgentUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(input.getTelephone(), 6);
        medAgentUser.setPassword(SecureUtil.sha256(last6Bit + medAgentUser.getSalt()));
        medAgentUser.setStatus(UserStatusEnum.NORMAL.getCode());
        medAgentUser.setLastLoginTime(LocalDateTime.now());

        List<Organization> parents = organizationDAO.findByParentIdIsNullAndActiveIsTrueAndBelongAndBelongId(BelongEnum.MED_AGENT_USER.getCode(), input.getMedAgentId());
        if(CollectionUtil.isNotEmpty(parents)){
            medAgentUser.setOrganizationId(parents.get(0).getId());
        }

        medAgentUserDAO.save(medAgentUser);
        roleService.bindRoleByPlatform(medAgentUser.getId(), DefaultRoleCodeEnum.ROLE_NAME_MED_AGENT_ADMIN);


    }

    @Override
    public void add(MedAgentUserAddFO input, String medAgentId) {
        checkUser(StrUtil.EMPTY, input.getAccount(), input.getTelephone(), medAgentId);
        MedAgentUser medAgentUser = new MedAgentUser();
        input.convertTo(medAgentUser);


        medAgentUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(input.getTelephone(), 6);
        medAgentUser.setPassword(SecureUtil.sha256(last6Bit + medAgentUser.getSalt()));
        medAgentUser.setStatus(UserStatusEnum.NORMAL.getCode());
        medAgentUser.setLastLoginTime(LocalDateTime.now());
        medAgentUser.setMedAgentId(medAgentId);
        Integer dataType = roleService.roles(input.getRoleId()).stream().mapToInt(Role::getDataType).min().getAsInt();
        medAgentUser.setDataType(dataType);

        // 处理用户角色
        userRoleService.handleUserRole(medAgentUser.getId(), input.getRoleId());

        medAgentUserDAO.save(medAgentUser);


    }

    @Override
    public void edit(MedAgentUserEditByPlatformFO input) {
        MedAgentUser medAgentUser = medAgentUserDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("代理商账户信息不存在"));
        checkUser(input.getId(), input.getAccount(), input.getTelephone(), medAgentUser.getMedAgentId());
        input.convertTo(medAgentUser);
        medAgentUserDAO.save(medAgentUser);
    }

    @Override
    public void edit(MedAgentUserEditFO input, String medAgentId) {
        checkUser(input.getId(), input.getAccount(), input.getTelephone(), medAgentId);
        MedAgentUser medAgentUser = medAgentUserDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("账户信息不存在"));
        input.convertTo(medAgentUser);
        medAgentUserDAO.save(medAgentUser);
        // 处理用户角色
        userRoleService.handleUserRole(medAgentUser.getId(), input.getRoleId());
    }

    private void checkUser(String id, String account, String telephone, String medAgentId) {
        MedAgentUser existAccount = medAgentUserDAO.getByAccountAndMedAgentId(account, medAgentId);
        if (Objects.nonNull(existAccount) && !existAccount.getId().equals(id)) {
            throw new BusinessAssertException("账户已存在");
        }
        MedAgentUser existTelephone = medAgentUserDAO.getByTelephoneAndMedAgentId(telephone, medAgentId);
        if (Objects.nonNull(existTelephone) && !existTelephone.getId().equals(id)) {
            throw new BusinessAssertException("手机号已存在");
        }
    }

    @Override
    public void initPass(String id) {
        MedAgentUser medAgentUser = medAgentUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("代理商账户信息不存在"));
        medAgentUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(medAgentUser.getTelephone(), 6);
        medAgentUser.setPassword(SecureUtil.sha256(last6Bit + medAgentUser.getSalt()));
        medAgentUserDAO.save(medAgentUser);
    }

    @Override
    public void closeOpenAccount(String id) {
        MedAgentUser medAgentUser = medAgentUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("代理商账户信息不存在"));
        if (UserStatusEnum.NORMAL.getCode().equals(medAgentUser.getStatus())) {
            medAgentUser.setStatus(UserStatusEnum.CLOSE.getCode());

            // 禁用，强制退出
            String token = redisHelper.strGet(RedisCacheKeys.getTokenKey(id));
            redisHelper.delKey(RedisCacheKeys.getTokenKey(id));
            redisHelper.delKey(RedisCacheKeys.getTokenPrefixKey(token));
        } else if (UserStatusEnum.CLOSE.getCode().equals(medAgentUser.getStatus())) {
            medAgentUser.setStatus(UserStatusEnum.NORMAL.getCode());
        }
        medAgentUserDAO.save(medAgentUser);
    }

    @Override
    public void batchDelete(String medAgentId) {
        List<MedAgentUser> medAgentUsers = medAgentUserDAO.findByMedAgentId(medAgentId);
        medAgentUserDAO.deleteAll(medAgentUsers);
    }

    @Override
    public String getOrgId(String id) {
        MedAgentUser medAgentUser = medAgentUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("代理商账户信息不存在"));
        return medAgentUser.getOrganizationId();
    }

    @Override
    public MedAgentUser getById(String userId) {
        Optional<MedAgentUser> medAgentUserOpt = medAgentUserDAO.findById(userId);
        if (medAgentUserOpt.isPresent()) {
            MedAgentUser medAgentUser = medAgentUserOpt.get();
            // 关联角色
            List<Role> roleList = userRoleService.findByUserId(medAgentUser.getId());
            medAgentUser.setRoles(roleList);
            // 关联权限菜单
            List<Permission> permissionList = permissionService.findByUserId(medAgentUser.getId(), BelongEnum.MED_AGENT_USER);
            medAgentUser.setPermissions(permissionList);
            return medAgentUser;
        }
        return null;
    }

    @Override
    public List<MedAgentUserSelectVO> select(String medAgentId) {
        return medAgentUserDAO.findByMedAgentIdAndOrganizationIdIsNotNull(medAgentId).stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
            MedAgentUserSelectVO one = new MedAgentUserSelectVO();
            one.setId(x.getId());
            one.setName(x.getNickName());
            return one;
        }).collect(Collectors.toList());
    }
}
