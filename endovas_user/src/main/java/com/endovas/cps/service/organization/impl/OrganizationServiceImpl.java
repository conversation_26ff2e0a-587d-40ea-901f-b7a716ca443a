package com.endovas.cps.service.organization.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.endovas.cps.dao.OrganizationDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.UserRoleDAO;
import com.endovas.cps.dao.mapper.MedAgentUserMapper;
import com.endovas.cps.dao.mapper.PlatformUserMapper;
import com.endovas.cps.entity.Organization;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.UserRole;
import com.endovas.cps.entity.user.MedAgentUser;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.dto.RoleSelectDTO;
import com.endovas.cps.pojo.fo.organization.OrganizationAddFO;
import com.endovas.cps.pojo.fo.organization.OrganizationUpdateFO;
import com.endovas.cps.pojo.fo.organization.UserSearchFO;
import com.endovas.cps.pojo.vo.OrganizationTreeVO;
import com.endovas.cps.pojo.vo.UserListVO;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.role.UserRoleService;
import io.daige.starter.common.database.mybatis.conditions.query.QueryWrap;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: kang.wang
 * Date: 2021/6/25
 * Time: 下午1:09
 */
@Slf4j
@Service
public class OrganizationServiceImpl implements OrganizationService {
    @Autowired
    private OrganizationDAO organizationDAO;
    @Autowired
    private PlatformUserDAO platformUserDAO;
    @Autowired
    private PlatformUserMapper platformUserMapper;
    @Autowired
    private MedAgentUserMapper medAgentUserMapper;


    @Autowired
    private UserRoleDAO userRoleDAO;
    @Autowired
    private UserRoleService userRoleService;


    /**
     * 下级组织查询
     *
     * @param organizationId
     * @return
     */
    @Override
    public List<OrganizationTreeVO> treeByOrganizationId(Boolean showAll, String organizationId, String belongId, BelongEnum belong) {
        List<OrganizationTreeVO> parents;
        if (StrUtil.isEmpty(organizationId)) {
            if (BooleanUtil.isTrue(showAll)) {
                parents = covert(organizationDAO.findByParentIdIsNullAndBelongAndBelongId(belong.getCode(), belongId));
            } else {
                parents = covert(organizationDAO.findByParentIdIsNullAndActiveIsTrueAndBelongAndBelongId(belong.getCode(), belongId));
            }
        } else {
            parents = covert(Arrays.asList(organizationDAO.findById(organizationId).get()));
        }

        List<OrganizationTreeVO> all;
        if (BooleanUtil.isTrue(showAll)) {
            all = covert(organizationDAO.findByParentIdIsNotNullAndBelongAndBelongId(belong.getCode(), belongId));
        } else {
            all = covert(organizationDAO.findByParentIdIsNotNullAndActiveIsTrueAndBelongAndBelongId(belong.getCode(), belongId));
        }
        parents.forEach(x -> getTreeChildren(all, x));
        return parents;
    }

    @Override
    public List<OrganizationTreeVO> treeFromRoot(String belongId, BelongEnum belong) {
        List<OrganizationTreeVO> parents = covert(organizationDAO.findByParentIdIsNullAndActiveIsTrueAndBelongAndBelongId(belong.getCode(), belongId));
        List<OrganizationTreeVO> all = covert(organizationDAO.findByParentIdIsNotNullAndActiveIsTrueAndBelongAndBelongId(belong.getCode(), belongId));
        parents.forEach(x -> getTreeChildren(all, x));
        return parents;
    }

    @Override
    public List<Organization> recursiveFindByOrganizationId(String id) {
        List<Organization> all = new ArrayList<>();
        deepFindByOrganizationId(id, all);
        return all;
    }

    private List<Organization> deepFindByOrganizationId(String organizationId, List<Organization> allList) {
        List<Organization> organizationList = organizationDAO.findByParentIdAndActiveIsTrue(organizationId);
        if (!CollUtil.isEmpty(organizationList)) {
            allList.addAll(organizationList);
            for (Organization organization : organizationList) {
                deepFindByOrganizationId(organization.getId(), allList);
            }
        }
        return allList;
    }


    private List<OrganizationTreeVO> covert(List<Organization> organizations) {
        return organizations.stream().map(x -> {
            OrganizationTreeVO one = new OrganizationTreeVO().convertFrom(x);
            one.setAvailable(x.getActive());
            return one;

        }).collect(Collectors.toList());
    }

    private void getTreeChildren(List<OrganizationTreeVO> all, OrganizationTreeVO parent) {
        for (OrganizationTreeVO vo : all) {
            if (parent.getId().equals(vo.getParentId())) {
                getTreeChildren(all, vo);
                vo.setParentName(parent.getName());
                parent.getChildren().add(vo);
            }
        }
        if (CollUtil.isEmpty(parent.getChildren())) {
            parent.setChildren(null);
        }
    }

    /**
     * 添加组织
     *
     * @param fo
     */
    @Override
    public void add(OrganizationAddFO fo, String belongId, String belong) {
        //没有组织id,就是添加根节点
        if (StringUtils.isBlank(fo.getOrganizationId())) {
            Organization orgPO = new Organization();
            orgPO.setBelong(belong);
            orgPO.setBelongId(belongId);
            orgPO.setActive(true)
                    .setName(fo.getName())
                    .setCode(orgPO.getId() + ":");
            organizationDAO.save(orgPO);
        } else {
            Organization organization = organizationDAO.findById(fo.getOrganizationId()).orElseThrow(() -> new BusinessAssertException("未查询到上级机构，添加失败"));

            Organization orgPO = new Organization();
            orgPO.setBelong(belong);
            orgPO.setBelongId(belongId);
            orgPO.setParentId(organization.getId())
                    .setName(fo.getName())
                    .setCode(organization.getCode() + orgPO.getId() + ":")
                    .setActive(true);
            organizationDAO.save(orgPO);

        }
    }


    /**
     * 更新组织
     *
     * @param fo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(OrganizationUpdateFO fo) {
        Organization orgPO = organizationDAO.findById(fo.getId()).orElseThrow(() -> new BusinessAssertException("未查询到组织机构"));
        orgPO.setName(fo.getName());
        organizationDAO.save(orgPO);
    }


    /**
     * 切换状态
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id) {
        Organization organization = organizationDAO.findById(id).orElseThrow(() -> new BusinessAssertException("未查询到组织机构"));

        if (BooleanUtils.isTrue(organization.getActive())) {

            // 检查是否有下级组织
            List<Organization> organizations = organizationDAO.findByParentId(id);
            for (Organization exist : organizations) {
                if (BooleanUtil.isTrue(exist.getActive())) {
                    throw new BusinessAssertException("存在下级组织且下级组织处于启用状态，不能禁用");
                }
            }


            // 检查是否存在用户
            List<PlatformUser> platformUsers = platformUserDAO.findByOrganizationId(id);
            for (PlatformUser user : platformUsers) {
                if (UserStatusEnum.NORMAL.getCode().equals(user.getStatus())) {
                    throw new BusinessAssertException("存在用户，不能禁用");
                }
            }

            organization.setActive(false);
            organizationDAO.save(organization);
        } else if (BooleanUtils.isFalse(organization.getActive())) {
            organization.setActive(true);
            organizationDAO.save(organization);
        }
    }

    @Override
    public Organization getById(String id) {
        return organizationDAO.findById(id).orElseThrow(() -> new BusinessAssertException("未查询到组织机构"));
    }

    /**
     * 用户列表查询接口
     *
     * @param param
     * @param pageFO
     * @return
     */
    @Override
    public PageVO<UserListVO> listByOrganizationId(UserSearchFO param, PageFO pageFO, BelongEnum belong) {
        Organization loginUserOrg = organizationDAO.findById(param.getOrganizationId()).orElseThrow(() -> new BusinessAssertException("未查询到机构信息，获取用户列表失败"));

        // 查询出当前机构以及下级机构
        List<Organization> orgList = new ArrayList<>();
        orgList.add(loginUserOrg);

        List<String> currentAndSubOrgIdList = orgList.stream().map(Organization::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(currentAndSubOrgIdList)) {
            return PageUtil.empty(pageFO);
        }

        switch (belong) {
            case PLATFORM_USER: {
                IPage<PlatformUser> users = platformUsers(param, pageFO, orgList, currentAndSubOrgIdList, loginUserOrg);
                IPage<UserListVO> result = users.convert(x -> {
                    UserListVO one = new UserListVO();
                    BeanUtil.copyProperties(x, one);
                    convert(one, x.getRoles(), x.getOrganizationId());
                    return one;
                });
                return PageUtil.convert(result);
            }
            case MED_AGENT_USER: {
                IPage<MedAgentUser> users = medAgentUsers(param, pageFO, orgList, currentAndSubOrgIdList, loginUserOrg);
                IPage<UserListVO> result = users.convert(x -> {
                    UserListVO one = new UserListVO();
                    BeanUtil.copyProperties(x, one);
                    convert(one, x.getRoles(), x.getOrganizationId());
                    return one;
                });
                return PageUtil.convert(result);
            }
        }
        return PageUtil.empty(pageFO);

    }

    private void convert(UserListVO one, List<Role> roles, String organizationId) {
        one.setRoles(roles.stream().map(x -> new RoleSelectDTO(x.getId(), x.getName())).collect(Collectors.toList()));
        //处理组织名称
        if (Objects.nonNull(organizationId)) {
            one.setOrganizationId(organizationId);
            Optional<Organization> organization = organizationDAO.findById(organizationId);
            if (organization.isPresent()) {
                one.setOrganizationName(organization.get().getName());
            }
        }
    }

    private IPage<PlatformUser> platformUsers(UserSearchFO param, PageFO pageFO, List<Organization> orgList, List<String> currentAndSubOrgIdList, Organization loginUserOrg) {
        /**
         * 组装条件
         */
        QueryWrap<PlatformUser> q = new QueryWrap<>();
        // 排序
        q.orderByDesc(MysqlBase.COL_CREATE_TIME);
        IPage<PlatformUser> p = new Page<>(pageFO.getPageNumber(), pageFO.getPageSize());
        if (StringUtils.isNotBlank(param.getNickName())) {
            q.like(PlatformUser.COL_NICK_NAME, param.getNickName());
        }
        if (StringUtils.isNotBlank(param.getAccount())) {
            q.eq(PlatformUser.COL_ACCOUNT, param.getAccount());
        }
        if (StringUtils.isNotBlank(param.getTelephone())) {
            q.eq(PlatformUser.COL_TELEPHONE, param.getTelephone());
        }
        if (Objects.nonNull(param.getAvailable())) {
            if (BooleanUtil.isTrue(param.getAvailable())) {
                q.eq(MedAgentUser.COL_STATUS, UserStatusEnum.NORMAL.getCode());
            } else {
                q.eq(MedAgentUser.COL_STATUS, UserStatusEnum.CLOSE.getCode());
            }
        }

        if (StringUtils.isNotBlank(param.getOrgName())) {
            List<String> nameLikeOrgIdList = orgList.stream().filter(x -> x.getName().contains(param.getOrgName())).map(MysqlBase::getId).collect(Collectors.toList());
            q.in(PlatformUser.COL_ORGANIZATION_ID, nameLikeOrgIdList);
        } else {
            // 如果包含下级单位
            if (param.getOrgSearchRange().intValue() == 2) {
                List<Organization> oList = recursiveFindByOrganizationId(loginUserOrg.getId());
                if (CollectionUtil.isNotEmpty(oList)) {
                    currentAndSubOrgIdList.addAll(oList.stream().map(Organization::getId).collect(Collectors.toList()));
                }
            }

            q.in(PlatformUser.COL_ORGANIZATION_ID, currentAndSubOrgIdList);
        }
        if (StringUtils.isNotBlank(param.getRoleId())) {
            List<String> userIds = userRoleDAO.findByRoleId(param.getRoleId()).stream().map(UserRole::getUserId).collect(Collectors.toList());
            q.in(PlatformUser.FIELD_ID, userIds);
        }

        IPage<PlatformUser> users = platformUserMapper.selectPage(p, q);

        List<PlatformUser> userList = users.getRecords();

        /**
         * 补充数据
         */
        //处理角色以及数据权限角色
        userList.forEach(user -> {
            user.setRoles(userRoleService.findActiveRoleByUserId(user.getId()));
        });

        users.setRecords(userList);
        return users;
    }

    private IPage<MedAgentUser> medAgentUsers(UserSearchFO param, PageFO pageFO, List<Organization> orgList, List<String> currentAndSubOrgIdList, Organization loginUserOrg) {
        /**
         * 组装条件
         */
        QueryWrap<MedAgentUser> q = new QueryWrap<>();
        // 排序
        q.orderByDesc(MysqlBase.COL_CREATE_TIME);
        IPage<MedAgentUser> p = new Page<>(pageFO.getPageNumber(), pageFO.getPageSize());
        if (StringUtils.isNotBlank(param.getNickName())) {
            q.like(MedAgentUser.COL_NICK_NAME, param.getNickName());
        }
        if (StringUtils.isNotBlank(param.getAccount())) {
            q.eq(MedAgentUser.COL_ACCOUNT, param.getAccount());
        }
        if (StringUtils.isNotBlank(param.getTelephone())) {
            q.eq(MedAgentUser.COL_TELEPHONE, param.getTelephone());
        }
        if (Objects.nonNull(param.getAvailable())) {
            if (BooleanUtil.isTrue(param.getAvailable())) {
                q.eq(MedAgentUser.COL_STATUS, UserStatusEnum.NORMAL.getCode());
            } else {
                q.eq(MedAgentUser.COL_STATUS, UserStatusEnum.CLOSE.getCode());
            }
        }

        if (StringUtils.isNotBlank(param.getOrgName())) {
            List<String> nameLikeOrgIdList = orgList.stream().filter(x -> x.getName().contains(param.getOrgName())).map(MysqlBase::getId).collect(Collectors.toList());
            q.in(MedAgentUser.COL_ORGANIZATION_ID, nameLikeOrgIdList);
        } else {
            // 如果包含下级单位
            if (param.getOrgSearchRange().intValue() == 2) {
                List<Organization> oList = recursiveFindByOrganizationId(loginUserOrg.getId());
                if (CollectionUtil.isNotEmpty(oList)) {
                    currentAndSubOrgIdList.addAll(oList.stream().map(Organization::getId).collect(Collectors.toList()));
                }
            }

            q.in(MedAgentUser.COL_ORGANIZATION_ID, currentAndSubOrgIdList);
        }
        if (StringUtils.isNotBlank(param.getRoleId())) {
            List<String> userIds = userRoleDAO.findByRoleId(param.getRoleId()).stream().map(UserRole::getUserId).collect(Collectors.toList());
            q.in(MedAgentUser.FIELD_ID, userIds);
        }

        IPage<MedAgentUser> users = medAgentUserMapper.selectPage(p, q);

        List<MedAgentUser> userList = users.getRecords();

        /**
         * 补充数据
         */
        //处理角色以及数据权限角色
        userList.forEach(user -> {
            user.setRoles(userRoleService.findActiveRoleByUserId(user.getId()));
        });

        users.setRecords(userList);
        return users;
    }

    public String getOrgNamePath(String orgId, String orgName) {
        Organization org = organizationDAO.findById(orgId).orElseThrow(() -> new BusinessAssertException("未查询到组织信息"));
        if (StringUtils.isBlank(orgName)) {
            orgName = org.getName();
        } else {
            orgName = org.getName() + "/" + orgName;
        }
        if (StringUtils.isNotBlank(org.getParentId())) {
            getOrgNamePath(org.getParentId(), orgName);
        }
        return orgName;
    }

}
