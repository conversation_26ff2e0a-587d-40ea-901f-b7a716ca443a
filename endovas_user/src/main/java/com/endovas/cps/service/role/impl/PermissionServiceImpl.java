package com.endovas.cps.service.role.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.dao.PermissionDAO;
import com.endovas.cps.dao.mapper.PermissionMapper;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.enums.DefaultRoleCodeEnum;
import com.endovas.cps.pojo.dto.PermissionDTO;
import com.endovas.cps.pojo.vo.ButtonVO;
import com.endovas.cps.pojo.vo.MenuTreeVO;
import com.endovas.cps.service.role.PermissionService;
import com.google.common.collect.Lists;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 权限接口实现
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {
    private final PermissionDAO permissionDAO;
    private final PermissionMapper permissionMapper;
    private final RedisHelper redisHelper;

    private final String PLATFORM_USER_TABLE ="m_platform_user";
    private final String MED_AGENT_TABLE="t_med_agent_user";



    @Override
    public void loadResourceDefine() {
        List<PermissionDTO> permissionDTOList = Lists.newArrayList();
        // 获取启用的权限操作请求
        List<Permission> permissions = findByAvailableOrderBySortOrder(true);
        for (Permission permission : permissions) {
            if (StrUtil.isNotBlank(permission.getCode()) && StrUtil.isNotBlank(permission.getPath())) {
                permissionDTOList.add(new PermissionDTO(permission.getPath(), permission.getCode()));
            }
        }
        redisHelper.strSet(RedisCacheKeys.getPermissionList(), JSONUtil.toJsonStr(permissionDTOList));
    }

    @Override
    public List<Permission> findByUserId(String userId,BelongEnum belong) {
        switch (belong){
            case PLATFORM_USER:
                return permissionMapper.findByUserId(PLATFORM_USER_TABLE,userId);
            case MED_AGENT_USER:
                return permissionMapper.findByUserId(MED_AGENT_TABLE,userId);
        }
        return Lists.newArrayList();

    }


    @Override
    public List<Permission> findMenuByUserId(String userId,BelongEnum belong) {
        List<Integer> types = Lists.newArrayList(BizCommonConst.PERMISSION_DICT, BizCommonConst.PERMISSION_NAV, BizCommonConst.PERMISSION_PAGE);
        return findByUserId(userId,belong).stream().filter(x -> types.contains(x.getType())).collect(Collectors.toList());
    }

    @Override
    public List<ButtonVO> findBtnByUserId(String userId,BelongEnum belong) {
        return findByUserId(userId,belong).stream().filter(x -> BizCommonConst.PERMISSION_BTN.equals(x.getType())).map(x -> new ButtonVO().convertFrom(x)).collect(Collectors.toList());
    }


    @Override
    public Permission getRoot() {
        return permissionDAO.getByParentId(BizCommonConst.PARENT_ID);
    }

    @Override
    public List<Permission> findByParentIdOrderBySortOrder(String parentId) {
        return permissionDAO.findByParentIdOrderBySortOrder(parentId);
    }

    @Override
    public List<Permission> findByAvailableOrderBySortOrder(Boolean available) {
        return permissionDAO.findByAvailableOrderBySortOrder(available);
    }

    @Override
    public List<Permission> findByTypeAndAvailableOrderBySortOrder(Integer type, Boolean available) {
        return permissionDAO.findByTypeAndAvailableOrderBySortOrder(type, available);
    }

    @Override
    public List<Permission> findByCodeAndBelong(String code,String belong) {
        return permissionDAO.findByCodeAndBelong(code,belong);
    }

    @Override
    public List<Permission> findByParentIdIn(List<String> parentIds) {
        return permissionDAO.findByParentIdIn(parentIds);
    }


    @Override
    public void deleteById(String id) {
        permissionDAO.deleteById(id);
    }


    private void getMenuTreeChildren(List<MenuTreeVO> all, MenuTreeVO parent) {
        for (MenuTreeVO vo : all) {
            if (parent.getId().equals(vo.getParentId())) {
                getMenuTreeChildren(all, vo);
                if (Objects.nonNull(parent.getChildren())) {
                    parent.getChildren().add(vo);
                }

            }
        }
        if (CollUtil.isEmpty(parent.getChildren())) {
            parent.setChildren(null);
        }
    }



    @Override
    public List<String> findCurrentUserFields(List<String> permissionCodes, String userId,BelongEnum belong) {
        List<ButtonVO> fields;
        if (permissionCodes.contains(DefaultRoleCodeEnum.ROLE_NAME_PLATFORM_SUPER_ADMIN.getCode())) {
            List<Integer> types = Lists.newArrayList(BizCommonConst.PERMISSION_BTN);
            fields = permissionDAO.findByTypeInOrderBySortOrder(types).stream().filter(x -> "2".equals(x.getButtonType())).map(x -> new ButtonVO().convertFrom(x)).collect(Collectors.toList());
        } else {
            fields = findBtnByUserId(userId,belong).stream().filter(x -> "2".equals(x.getButtonType())).collect(Collectors.toList());
        }
        List<String> result = Lists.newArrayList();
        for (ButtonVO field : fields) {
            List<String> code = StrUtil.split(field.getCode(), "_");
            if (code.size() == 2) {
                if (!result.contains(code.get(1))) {
                    result.add(code.get(1));
                }
            }
        }
        return result;
    }


    @Override
    public MenuTreeVO findCurrentUserMenusAndBtns(List<String> permissionCodes, String userId, BelongEnum belong) {
        MenuTreeVO parentVO;
        List<Permission> userMenuPermissions;
        //权限列表里包含系统默认管理员权限的,显示所属系统的所有菜单
        if (!CollectionUtil.intersection(permissionCodes, DefaultRoleCodeEnum.getSysRoleNameList()).isEmpty()) {
            userMenuPermissions = permissionDAO.findByBelongOrderBySortOrder(belong.getCode());
        } else {
            userMenuPermissions = findByUserId(userId,belong).stream().filter(x -> BooleanUtil.isTrue(x.getShowAlways())).collect(Collectors.toList());
        }

        Permission parent = permissionDAO.getByParentId(BizCommonConst.PARENT_ID);
        parentVO = new MenuTreeVO().convertFrom(parent);
        List<MenuTreeVO> all = userMenuPermissions.stream().sorted(Comparator.comparing(Permission::getType)).map(x -> new MenuTreeVO().convertFrom(x)).collect(Collectors.toList());
        getMenuTreeChildren(all, parentVO);

        return parentVO;
    }


}
