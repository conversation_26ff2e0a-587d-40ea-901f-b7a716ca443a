package com.endovas.cps.service.password.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.endovas.cps.service.password.PasswordStrategyService;
import io.daige.starter.common.constant.PasswordStrategyConst;
import io.daige.starter.common.exception.BusinessAssertException;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/23
 * Time: 下午8:45
 */
@Component(PasswordStrategyConst.HIGH)
public class HighPasswordStrategyService implements PasswordStrategyService {

    @Override
    public void check(String password) {
        if (StringUtils.isBlank(password) || !password.matches("[\\\\a-zA-Z0-9@%+/!#$^?:,(){}\\[\\]~\\-_\\.]{8,16}")) {
            throw new BusinessAssertException("密码必须包含至少 1 个大写字母、 1 个小写字母、 1 个数字和 1 个符号, 最少应使用 8 个字符，最多 16 个字符");
        }
        if (password.equals(password.toUpperCase(Locale.ENGLISH))) {
            throw new BusinessAssertException("密码必须包含小写字母");
        }
        if (password.equals(password.toLowerCase(Locale.ENGLISH))) {
            throw new BusinessAssertException("密码必须包含大写字母");
        }
        if (!password.matches(".*\\d.*")) {
            throw new BusinessAssertException("密码必须包含数字");
        }
        if (!password.matches(".*[\\\\@%+/!#$^?:,(){}\\[\\]~\\-_.].*")) {
            throw new BusinessAssertException("密码必须包含特殊字符");
        }
    }
}
