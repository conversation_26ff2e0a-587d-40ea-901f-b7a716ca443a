package com.endovas.cps.service.organization;


import com.endovas.cps.entity.Organization;
import com.endovas.cps.pojo.fo.organization.OrganizationAddFO;
import com.endovas.cps.pojo.fo.organization.OrganizationUpdateFO;
import com.endovas.cps.pojo.fo.organization.UserSearchFO;
import com.endovas.cps.pojo.vo.OrganizationTreeVO;
import com.endovas.cps.pojo.vo.UserListVO;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * 组织机构服务
 */
public interface OrganizationService {
    /**
     * 添加组织
     *
     * @param fo
     */
    void add(OrganizationAddFO fo,String belongId,String belong);

    /**
     * 更新组织
     *
     * @param fo
     */
    void modify(OrganizationUpdateFO fo);

    /**
     * 切换状态
     *
     * @param id
     */
    void enable(String id);

    /**
     * 根据组织id查询下级组织查询
     *
     * @param organizationId
     * @return
     */
    List<OrganizationTreeVO> treeByOrganizationId(Boolean showAll, String organizationId, String belongId,BelongEnum belong);
    List<OrganizationTreeVO> treeFromRoot(String belongId,BelongEnum belong);

    /**
     * 递归向下查询
     *
     * @param id
     * @return
     */
    List<Organization> recursiveFindByOrganizationId(String id);

    Organization getById(String id);

    PageVO<UserListVO> listByOrganizationId(UserSearchFO param, PageFO pageFO,BelongEnum belong);

    /**
     * 获取组织机构名称路径  心脉医疗/东北大区
     * @param orgId
     * @param orgName
     * @return
     */
    String getOrgNamePath(String orgId, String orgName);
}
