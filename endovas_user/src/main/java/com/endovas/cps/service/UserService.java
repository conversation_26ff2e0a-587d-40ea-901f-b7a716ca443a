package com.endovas.cps.service;

import com.endovas.cps.pojo.dto.WebLoginUserDTO;
import com.endovas.cps.pojo.fo.UserPassLoginFO;
import com.endovas.cps.pojo.vo.UserSelectByAccountVO;
import com.endovas.cps.pojo.vo.UserSelectByIdVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/5
 * Time: 15:32
 */
public interface UserService {


    WebLoginUserDTO getByAccount(String account, String belong, String enterpriseId) ;
    void resetPassword(String tel, String belong, String enterpriseId, String password);
    WebLoginUserDTO getUser(LoginUser loginUser);

    boolean check(UserPassLoginFO userPassLoginFO);

    boolean checkByTel(String tel, String belong,String enterpriseId);

    void updateLoginTime(WebLoginUserDTO webLoginUser);

    List<UserSelectByAccountVO> accountSelect(String hospitalId, String medAgentId, String belong);

    List<UserSelectByIdVO> select(String belong);

    String getNickName(String userId,String belong);

    List<String> getUserIds(String nickName);

    String  getNickName(String userId);

}
