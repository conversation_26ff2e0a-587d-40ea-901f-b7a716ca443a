package com.endovas.cps.service.expert;

import com.endovas.cps.pojo.dto.WebLoginUserDTO;
import com.endovas.cps.pojo.fo.expert.ExpertAddFO;
import com.endovas.cps.pojo.fo.expert.ExpertEditFO;
import com.endovas.cps.pojo.fo.expert.ExpertSearchFO;
import com.endovas.cps.pojo.vo.ExpertSelectVO;
import com.endovas.cps.pojo.vo.ExpertUserListVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:26
 */
public interface ExpertService {
    PageVO<ExpertUserListVO> list(ExpertSearchFO searchFO, PageFO pageFO);

    void add(ExpertAddFO input);

    void edit(ExpertEditFO input);

    void initPass(String id);

    void closeOpenAccount(String id);

    boolean check(String telephone );

    WebLoginUserDTO getByTelephone(String telephone);

    List<ExpertSelectVO> select(String key);

    void logoff(String telephone);
}
