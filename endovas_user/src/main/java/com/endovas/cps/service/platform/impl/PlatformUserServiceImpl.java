package com.endovas.cps.service.platform.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.endovas.cps.dao.OrganizationDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.mapper.PlatformUserMapper;
import com.endovas.cps.entity.Organization;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.fo.platform.PlatformUserAddFO;
import com.endovas.cps.pojo.fo.platform.PlatformUserEditFO;
import com.endovas.cps.pojo.vo.platform.PlatformUserListVO;
import com.endovas.cps.pojo.vo.platform.PlatformUserVO;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.platform.PlatformUserService;
import com.endovas.cps.service.role.PermissionService;
import com.endovas.cps.service.role.UserRoleService;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.component.redis.service.RedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/22
 * Time: 下午3:04
 */
@Service
public class PlatformUserServiceImpl implements PlatformUserService {
    @Autowired
    private PlatformUserDAO platformUserDAO;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PlatformUserMapper platformUserMapper;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationDAO organizationDAO;

    @Autowired
    private RedisHelper redisHelper;


    /**
     * 获取用户当前组织下的所有用户
     *
     * @param orgId
     * @return
     */
    @Override
    public List<PlatformUserListVO> list(String orgId) {


        // 查询出下级组织
        List<Organization> oList = organizationService.recursiveFindByOrganizationId(orgId);
        List<String> currentAndSubOrgIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(oList)) {
            currentAndSubOrgIdList.addAll(oList.stream().map(Organization::getId).collect(Collectors.toList()));
        }

        // 补充当前组织
        currentAndSubOrgIdList.add(orgId);
        if (StrUtil.isNotEmpty(orgId)) {
            Optional<Organization> organization = organizationDAO.findById(orgId);
            if (organization.isPresent()) {
                oList.add(organization.get());
            }
        }


        Map<String, String> orgNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(oList)) {
            orgNameMap = oList.stream().collect(Collectors.toMap(Organization::getId, Organization::getName, (key1, key2) -> key2));
        }

        // 根据组织id，查询出所有的用户id
        List<PlatformUserListVO> voList = new ArrayList<>();
        List<PlatformUser> userDataList = platformUserMapper.selectList(Wrappers.<PlatformUser>lambdaQuery().in(PlatformUser::getOrganizationId, currentAndSubOrgIdList));
        if (CollectionUtil.isNotEmpty(userDataList)) {
            for (PlatformUser user : userDataList) {
                PlatformUserListVO one = new PlatformUserListVO().convertFrom(user);
                String result = one.getNickName() +
                        "(" +
                        orgNameMap.get(user.getOrganizationId()) +
                        ")";
                one.setNickName(result);
                voList.add(one);
            }
        }

        return voList;
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public String updateUser(PlatformUser platformUser) {
        // 不允许修改用户信息时修改密码，请单独调用修改密码接口
        PlatformUser oldPlatformUser = platformUserDAO.findById(platformUser.getId()).get();
        platformUser.setPassword(oldPlatformUser.getPassword());
        platformUser.setSalt(oldPlatformUser.getSalt());
        platformUserDAO.save(platformUser);
        return platformUser.getId();
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(String userId, String password) {
        PlatformUser platformUser = platformUserDAO.findById(userId).get();
        BizAssert.notNull(platformUser, "用户不存在");
        String defPassword = SecureUtil.sha256(password + platformUser.getSalt());
        platformUser.setLastPasswordModifyTime(LocalDateTime.now());
        platformUser.setPassword(defPassword);
        platformUserDAO.save(platformUser);
        return true;
    }

    @Override
    public PlatformUserVO getUser(String userId) {
        PlatformUser platformUser = platformUserDAO.findById(userId).orElseThrow(() -> new BusinessAssertException("员工不存在,请检查数据"));
        return new PlatformUserVO().convertFrom(platformUser);
    }


    @Override
    public PlatformUser getById(String id) {


        Optional<PlatformUser> platformUser = platformUserDAO.findById(id);
        if (platformUser.isPresent()) {
            return getByAccount(platformUser.get().getAccount());
        }
        return null;

    }

    @Override
    public boolean check(String account) {
        return platformUserDAO.countByAccount(account) > 0;
    }

    @Override
    public boolean checkByTel(String telephone) {
        return platformUserDAO.countByTelephone(telephone) > 0;
    }

    @Override
    public PlatformUser getByAccount(String account) {
        PlatformUser platformUser = platformUserDAO.getByAccount(account);
        // 关联角色
        List<Role> roleList = userRoleService.findByUserId(platformUser.getId());
        platformUser.setRoles(roleList);
        // 关联权限菜单
        List<Permission> permissionList = permissionService.findByUserId(platformUser.getId(), BelongEnum.PLATFORM_USER);
        platformUser.setPermissions(permissionList);
        return platformUser;
    }

    @Override
    public PlatformUser getByTel(String telephone) {
        return platformUserDAO.getByTelephone(telephone);
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(PlatformUserAddFO input) {
        checkUser("", input.getAccount(), input.getTelephone());
        PlatformUser platformUser = new PlatformUser();
        input.convertTo(platformUser);

        platformUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(input.getTelephone(), 6);
        platformUser.setPassword(SecureUtil.sha256(last6Bit + platformUser.getSalt()));
        platformUser.setStatus(UserStatusEnum.NORMAL.getCode());
        platformUserDAO.save(platformUser);

        // 处理用户角色
        userRoleService.handleUserRole(platformUser.getId(), input.getRoleId());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void edit(PlatformUserEditFO input) {
        checkUser(input.getId(), input.getAccount(), input.getTelephone());
        PlatformUser platformUser = platformUserDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("账户信息不存在"));
        input.convertTo(platformUser);
        platformUserDAO.save(platformUser);
        // 处理用户角色
        userRoleService.handleUserRole(platformUser.getId(), input.getRoleId());
    }

    @Override
    public void closeOpenAccount(String id) {
        PlatformUser platformUser = platformUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("账户信息不存在"));
        if (UserStatusEnum.NORMAL.getCode().equals(platformUser.getStatus())) {
            platformUser.setStatus(UserStatusEnum.CLOSE.getCode());
            // 禁用，强制退出
            String token = redisHelper.strGet(RedisCacheKeys.getTokenKey(id));
            redisHelper.delKey(RedisCacheKeys.getTokenKey(id));
            redisHelper.delKey(RedisCacheKeys.getTokenPrefixKey(token));
        } else if (UserStatusEnum.CLOSE.getCode().equals(platformUser.getStatus())) {
            platformUser.setStatus(UserStatusEnum.NORMAL.getCode());
        }
        platformUserDAO.save(platformUser);
    }

    @Override
    public void initPass(String id) {
        PlatformUser platformUser = platformUserDAO.findById(id).orElseThrow(() -> new BusinessAssertException("账户信息不存在"));
        platformUser.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(platformUser.getTelephone(), 6);
        platformUser.setPassword(SecureUtil.sha256(last6Bit + platformUser.getSalt()));
        platformUserDAO.save(platformUser);
    }

    private void checkUser(String id, String account, String telephone) {
        PlatformUser existAccount = platformUserDAO.getByAccount(account);
        if (Objects.nonNull(existAccount) && !existAccount.getId().equals(id)) {
            throw new BusinessAssertException("账户已存在");
        }
        PlatformUser existTelephone = platformUserDAO.getByTelephone(telephone);
        if (Objects.nonNull(existTelephone) && !existTelephone.getId().equals(id)) {
            throw new BusinessAssertException("手机号已存在");
        }
    }


}
