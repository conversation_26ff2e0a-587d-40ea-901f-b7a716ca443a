package com.endovas.cps.service.role.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.endovas.cps.dao.UserRoleDAO;
import com.endovas.cps.dao.mapper.PlatformUserRoleMapper;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.UserRole;
import com.endovas.cps.service.role.UserRoleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色接口实现
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class UserRoleServiceImpl implements UserRoleService {

    private final UserRoleDAO userRoleDAO;
    private final PlatformUserRoleMapper platformUserRoleMapper;

    @Override
    public List<Role> findActiveRoleByUserId(String userId) {
        return platformUserRoleMapper.findActiveRoleByUserId(userId);
    }

    @Override
    public List<Role> findByUserId(String userId) {
        return platformUserRoleMapper.findByUserId(userId);
    }

    @Override
    public List<UserRole> findByRoleId(String roleId) {
        return userRoleDAO.findByRoleId(roleId);
    }

    @Override
    public void deleteByUserId(String userId) {
        userRoleDAO.deleteByUserId(userId);
    }

    @Override
    public long countByRoleId(String roleId) {
        return userRoleDAO.countByRoleId(roleId);
    }

    @Override
    public void delete(List<UserRole> userRoles) {
        userRoleDAO.deleteAll(userRoles);
    }


    @Override
    public void handleUserRole(String userId, List<String> roleIdList) {
        if (CollectionUtil.isEmpty(roleIdList)) {
            return;
        }
        // 删除旧的数据
        userRoleDAO.deleteByUserId(userId);

        // 添加新的数据
        List<UserRole> urPoList = roleIdList.stream().map(rid -> {
            UserRole ur = new UserRole();
            ur.setRoleId(rid);
            ur.setUserId(userId);
            return ur;
        }).collect(Collectors.toList());
        userRoleDAO.saveAll(urPoList);
    }
}
