package com.endovas.cps.service.password;

import io.daige.starter.common.exception.BusinessAssertException;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/23
 * Time: 下午8:45
 */
@Component
public class PasswordBuilder {
    private final Map<String, PasswordStrategyService> passwordStrategyPool = new ConcurrentHashMap<>();

    public PasswordBuilder(Map<String, PasswordStrategyService> passwordStrategyPool) {
        passwordStrategyPool.forEach(this.passwordStrategyPool::put);
    }

    /**
     * 获取密码策略
     *
     * @param strategy 密码策略
     * @return PasswordStrategyService
     */
    public PasswordStrategyService getPasswordStrategyService(String strategy) {
        PasswordStrategyService passwordStrategy = passwordStrategyPool.get(strategy);
        if (passwordStrategy == null) {
            throw new BusinessAssertException("密码策略配置 不支持，请传递正确的 passwordStrategy 参数");
        }
        return passwordStrategy;
    }
}
