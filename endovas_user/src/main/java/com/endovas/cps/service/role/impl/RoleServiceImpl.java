package com.endovas.cps.service.role.impl;

import com.endovas.cps.dao.RoleDAO;
import com.endovas.cps.dao.UserRoleDAO;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.UserRole;
import com.endovas.cps.enums.DefaultRoleCodeEnum;
import com.endovas.cps.service.role.RoleService;
import io.daige.starter.common.enums.BelongEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 角色接口实现
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final RoleDAO roleDAO;

    private final UserRoleDAO userRoleDAO;


    @Override
    public void bindRoleByPlatform(String userId, DefaultRoleCodeEnum roleCode) {
        Role role = roleDAO.getByCodeAndBelongAndBelongId(roleCode.getCode(), BelongEnum.PLATFORM_USER.getCode(), null);
        UserRole userRole = new UserRole();
        userRole.setRoleId(role.getId());
        userRole.setUserId(userId);
        userRoleDAO.save(userRole);
    }

    @Override
    public List<Role> roles(List<String> roleIds) {
        return roleDAO.findByIdIn(roleIds);
    }
}
