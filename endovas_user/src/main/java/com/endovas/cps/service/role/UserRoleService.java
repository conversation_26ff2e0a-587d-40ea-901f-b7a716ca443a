package com.endovas.cps.service.role;


import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.UserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色接口
 */
public interface UserRoleService {
    /**
     * 通过用户id获取
     *
     * @param userId
     * @return
     */
    List<Role> findActiveRoleByUserId(@Param("userId") String userId);

    /**
     * 通过用户id获取
     *
     * @param userId
     * @return
     */
    List<Role> findByUserId(@Param("userId") String userId);

    /**
     * 通过roleId查找
     *
     * @param roleId
     * @return
     */
    List<UserRole> findByRoleId(String roleId);

    /**
     * 删除用户角色
     *
     * @param userId
     */
    void deleteByUserId(String userId);

    long countByRoleId(String roleId);

    void delete(List<UserRole> userRoles);

    void handleUserRole(String userId, List<String> roleIdList);
}
