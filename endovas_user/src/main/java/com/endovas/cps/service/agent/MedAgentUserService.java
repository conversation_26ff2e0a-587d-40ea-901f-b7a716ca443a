package com.endovas.cps.service.agent;

import com.endovas.cps.entity.user.MedAgentUser;
import com.endovas.cps.pojo.fo.agent.*;
import com.endovas.cps.pojo.vo.agent.MedAgentUserListByPlatformVO;
import com.endovas.cps.pojo.vo.agent.MedAgentUserListVO;
import com.endovas.cps.pojo.vo.agent.MedAgentUserSelectVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:26
 */
public interface MedAgentUserService {
    PageVO<MedAgentUserListByPlatformVO> list(MedAgentUserSearchByPlatformFO searchFO, List<String> medAgentIds, PageFO pageFO);
    List<MedAgentUserListVO> list(String orgId);
    void add(MedAgentUserAddByPlatformFO input);

    void add(MedAgentUserAddFO input, String medAgentId);

    void edit(MedAgentUserEditByPlatformFO input);

    void edit(MedAgentUserEditFO input, String medAgentId);

    void initPass(String id);

    void closeOpenAccount(String id);

    void batchDelete(String medAgentId);

    String getOrgId(String id);


    MedAgentUser getById(String userId);

    List<MedAgentUserSelectVO> select(String medAgentId);

}
