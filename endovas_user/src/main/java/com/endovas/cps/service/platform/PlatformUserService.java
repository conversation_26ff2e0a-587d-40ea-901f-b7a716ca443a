package com.endovas.cps.service.platform;

import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.pojo.fo.platform.PlatformUserAddFO;
import com.endovas.cps.pojo.fo.platform.PlatformUserEditFO;
import com.endovas.cps.pojo.vo.platform.PlatformUserListVO;
import com.endovas.cps.pojo.vo.platform.PlatformUserVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/22
 * Time: 下午3:01
 */
public interface PlatformUserService {

    PlatformUser getById(String id);

    PlatformUserVO getUser(String userId);

    /**
     * 检测账号是否存在
     *
     * @param account 账号
     * @return true 表示存在
     */
    boolean check(String account);

    /**
     * 检测账号是否存在
     *
     * @param telephone 账号
     * @return true 表示存在
     */
    boolean checkByTel(String telephone);

    /**
     * 根据账号查询用户
     *
     * @param account 账号
     * @return 用户
     */
    PlatformUser getByAccount(String account);

    /**
     * 根据手机号查询用户
     *
     * @param telephone 账号
     * @return 用户
     */
    PlatformUser getByTel(String telephone);

    void add(PlatformUserAddFO param);

    void edit(PlatformUserEditFO param);

    void closeOpenAccount(String id);

    void initPass(String id);

    List<PlatformUserListVO> list(String orgId);

    /**
     * 修改用户信息
     *
     * @param platformUser 用户
     * @return 用户
     */
    String updateUser(PlatformUser platformUser);

    /**
     * 重置密码
     *
     * @param userId 用户ID
     * @param userId 重置的新密码
     * @return 是否成功
     */
    Boolean resetPassword(String userId, String password);

}
