package com.endovas.cps.service.password.impl;

import com.endovas.cps.service.password.PasswordStrategyService;
import io.daige.starter.common.constant.PasswordStrategyConst;
import io.daige.starter.common.exception.BusinessAssertException;
import org.springframework.stereotype.Component;

import java.util.Locale;


/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/23
 * Time: 下午8:45
 */
@Component(PasswordStrategyConst.NORMAL)
public class NormalPasswordStrategyService implements PasswordStrategyService {
    @Override
    public void check(String password) {
        if (password.length() <= 15) {
            throw new BusinessAssertException("密码必须最少应使用 15 个字符");
        }
        if (password.equals(password.toUpperCase(Locale.ENGLISH))) {
            throw new BusinessAssertException("密码必须包含小写字母");
        }
        if (password.equals(password.toLowerCase(Locale.ENGLISH))) {
            throw new BusinessAssertException("密码必须包含大写字母");
        }
        if (!password.matches(".*\\d.*")) {
            throw new BusinessAssertException("密码必须包含数字");
        }
    }
}
