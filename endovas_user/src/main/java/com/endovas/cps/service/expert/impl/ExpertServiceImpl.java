package com.endovas.cps.service.expert.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.endovas.cps.dao.ExpertDAO;
import com.endovas.cps.entity.user.Expert;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.dto.WebLoginUserDTO;
import com.endovas.cps.pojo.fo.expert.ExpertAddFO;
import com.endovas.cps.pojo.fo.expert.ExpertEditFO;
import com.endovas.cps.pojo.fo.expert.ExpertSearchFO;
import com.endovas.cps.pojo.vo.ExpertSelectVO;
import com.endovas.cps.pojo.vo.ExpertUserListVO;
import com.endovas.cps.service.expert.ExpertService;
import com.google.common.collect.Lists;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpertServiceImpl implements ExpertService {
    private final ExpertDAO expertDAO;

    @Override
    public PageVO<ExpertUserListVO> list(ExpertSearchFO searchFO, PageFO pageFO) {

        Specification<Expert> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            if (StrUtil.isNotEmpty(searchFO.getNickName())) {
                Predicate p1 = criteriaBuilder.like(root.get(Expert.NICK_NAME), "%" + searchFO.getNickName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getEnterpriseName())) {
                Predicate p1 = criteriaBuilder.like(root.get(Expert.ENTERPRISE_NAME), "%" + searchFO.getEnterpriseName() + "%");
                list.add(p1);
            }
            if (StrUtil.isNotEmpty(searchFO.getTelephone())) {
                Predicate p1 = criteriaBuilder.like(root.get(Expert.TELEPHONE), "%" + searchFO.getTelephone() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Expert.STATUS), searchFO.getStatus());
                list.add(p1);
            }

            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };


        Page<ExpertUserListVO> list = expertDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> new ExpertUserListVO().convertFrom(x));
        return PageUtil.convert(list);
    }

    @Override
    public void add(ExpertAddFO input) {
        checkUser(StrUtil.EMPTY, input.getAccount(), input.getTelephone());
        Expert expert = new Expert();
        input.convertTo(expert);
        expert.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(input.getTelephone(), 6);
        expert.setPassword(SecureUtil.sha256(last6Bit + expert.getSalt()));
        expert.setStatus(UserStatusEnum.NORMAL.getCode());
        expertDAO.save(expert);
    }

    @Override
    public void edit(ExpertEditFO input) {
        checkUser(input.getId(), input.getAccount(), input.getTelephone());
        Expert expert = expertDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("专家账户信息不存在"));
        input.convertTo(expert);
        expertDAO.save(expert);
    }

    private void checkUser(String id, String account, String telephone) {
        Expert existAccount = expertDAO.getByAccount(account);
        if (Objects.nonNull(existAccount) && !existAccount.getId().equals(id)) {
            throw new BusinessAssertException("账户已存在");
        }
        Expert existTelephone = expertDAO.getByTelephone(telephone);
        if (Objects.nonNull(existTelephone) && !existAccount.getId().equals(id)) {
            throw new BusinessAssertException("手机号已存在");
        }
    }

    @Override
    public void initPass(String id) {
        Expert expert = expertDAO.findById(id).orElseThrow(() -> new BusinessAssertException("专家账户信息不存在"));
        expert.setSalt(RandomUtil.randomString(20));
        String last6Bit = StrUtil.subSufByLength(expert.getTelephone(), 6);
        expert.setPassword(SecureUtil.sha256(last6Bit + expert.getSalt()));
        expertDAO.save(expert);
    }

    @Override
    public void closeOpenAccount(String id) {
        Expert expert = expertDAO.findById(id).orElseThrow(() -> new BusinessAssertException("专家账户信息不存在"));
        if (UserStatusEnum.NORMAL.getCode().equals(expert.getStatus())) {
            expert.setStatus(UserStatusEnum.CLOSE.getCode());
        } else if (UserStatusEnum.CLOSE.getCode().equals(expert.getStatus())) {
            expert.setStatus(UserStatusEnum.NORMAL.getCode());
        }
        expertDAO.save(expert);
    }

    @Override
    public boolean check(String telephone) {
        Expert expert = expertDAO.getByTelephone(telephone);
        return Objects.nonNull(expert);
    }

    @Override
    public WebLoginUserDTO getByTelephone(String telephone) {
        WebLoginUserDTO result = new WebLoginUserDTO();
        Expert expert = expertDAO.getByTelephone(telephone);
        BeanUtil.copyProperties(expert, result);
        result.setPermissions(Lists.newArrayList());
        result.setRoles(Lists.newArrayList());
        result.setBelong(BelongEnum.EXPERT);
        return result;
    }

    @Override
    public List<ExpertSelectVO> select(String key) {
        Specification<Expert> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            Predicate p1 = criteriaBuilder.like(root.get(Expert.NICK_NAME), "%" + key + "%");
            list.add(p1);

            Predicate p2 = criteriaBuilder.equal(root.get(Expert.ACCOUNT), key);
            list.add(p2);
            if (Validator.isMobile(key)) {
                Predicate p3 = criteriaBuilder.equal(root.get(Expert.TELEPHONE), key);
                list.add(p3);
            }
            return criteriaBuilder.or(list.toArray(new Predicate[list.size()]));
        };

        return expertDAO.findAll(specification).stream().filter(x -> UserStatusEnum.NORMAL.getCode().equals(x.getStatus())).map(x -> {
            ExpertSelectVO one = new ExpertSelectVO();
            one.setId(x.getId());
            one.setName(x.getNickName());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public void logoff(String telephone) {
        Expert expert = expertDAO.getByTelephone(telephone);
        expert.setStatus(UserStatusEnum.LOGOFF.getCode());
        expertDAO.save(expert);
    }
}
