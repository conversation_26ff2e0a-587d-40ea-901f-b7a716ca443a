package com.endovas.cps.service.role.impl;

import com.endovas.cps.dao.RolePermissionDAO;
import com.endovas.cps.entity.RolePermission;
import com.endovas.cps.service.role.RolePermissionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 角色权限接口实现
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class RolePermissionServiceImpl implements RolePermissionService {

    private final RolePermissionDAO rolePermissionDAO;


    @Override
    public List<RolePermission> findByPermissionId(String permissionId) {
        return rolePermissionDAO.findByPermissionId(permissionId);
    }

    @Override
    public List<RolePermission> findByRoleId(String roleId) {
        return rolePermissionDAO.findByRoleId(roleId);
    }

    @Override
    public void deleteByRoleId(String roleId) {
        rolePermissionDAO.deleteByRoleId(roleId);
    }
}
