package com.endovas.cps.service.hospital;

import com.endovas.cps.pojo.fo.hospital.HospitalUserAddByPlatformFO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserEditByPlatformFO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserSearchByPlatformFO;
import com.endovas.cps.pojo.vo.hospital.HospitalUserListVO;
import com.endovas.cps.pojo.vo.hospital.HospitalUserSelectVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:26
 */
public interface HospitalUserService {
    PageVO<HospitalUserListVO> list(HospitalUserSearchByPlatformFO searchFO, List<String> hospitalIds, PageFO pageFO);

    void add(HospitalUserAddByPlatformFO input);

    void edit(HospitalUserEditByPlatformFO input);

    void initPass(String id);

    void closeOpenAccount(String id);

    void batchDelete(String hospitalId);

    long count(String hospitalId);

    List<HospitalUserSelectVO> select(String hospitalId);
}
