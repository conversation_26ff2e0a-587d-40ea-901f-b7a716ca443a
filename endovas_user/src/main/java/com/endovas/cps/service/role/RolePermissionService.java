package com.endovas.cps.service.role;


import com.endovas.cps.entity.RolePermission;

import java.util.List;

/**
 * 角色权限接口
 *
 */
public interface RolePermissionService  {

    /**
     * 通过permissionId获取
     * @param permissionId
     * @return
     */
    List<RolePermission> findByPermissionId(String permissionId);

    /**
     * 通过roleId获取
     * @param roleId
     */
    List<RolePermission> findByRoleId(String roleId);

    /**
     * 通过roleId删除
     * @param roleId
     */
    void deleteByRoleId(String roleId);
}
