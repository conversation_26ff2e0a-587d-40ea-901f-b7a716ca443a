package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 用户
 * </p>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum GenderEnum implements BaseEnum {

    /**
     * W="女"
     */
    F("女"),
    /**
     * M="男"
     */
    M("男"),
    /**
     * N="未知"
     */
    N("未知"),
    ;

    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static GenderEnum match(String val, GenderEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static GenderEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(GenderEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
