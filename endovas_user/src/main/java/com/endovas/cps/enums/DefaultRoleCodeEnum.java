package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/4
 * Time: 下午8:12
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DefaultRoleCodeEnum implements BaseEnum {

    //平台超级管理员
    ROLE_NAME_PLATFORM_SUPER_ADMIN("ROLE_NAME_PLATFORM_SUPER_ADMIN"),
    //代理商管理员
    ROLE_NAME_MED_AGENT_ADMIN("ROLE_NAME_MED_AGENT_ADMIN"),

    //医院管理员
    ROLE_NAME_HOSPITAL_ADMIN("ROLE_NAME_HOSPITAL_ADMIN"),



    ;

    private String desc;

    //返回系统内置角色
    public static List<String> getSysRoleNameList() {
        return Arrays.asList(ROLE_NAME_PLATFORM_SUPER_ADMIN.desc,  ROLE_NAME_MED_AGENT_ADMIN.desc, ROLE_NAME_HOSPITAL_ADMIN.desc);
    }

}

