package com.endovas.cps.dao;


import com.endovas.cps.entity.Role;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色数据处理层
 *
 */
@Repository
public interface RoleDAO extends MysqlBaseRepo<Role> {

    /**
     * 获取角色ByCode
     * @param code
     * @return
     */
    Role getByCodeAndBelongAndBelongId(String code,String belong,String belongId);


    /**
     * 获取角色ByCategory
     * @param category
     * @return
     */
    List<Role> findByIdIn(List<String> ids);
    List<Role> findByBelongAndBelongId(String belong,String belongId);
    Page<Role> findByBelongAndBelongId(String belong,String belongId, Pageable page);
    Long countByBelongAndBelongId(String belong,String belongId);

}
