package com.endovas.cps.dao.mapper;

import com.endovas.cps.entity.Permission;
import io.daige.starter.common.database.mysql.MysqlBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
@Mapper
public interface PermissionMapper extends MysqlBaseMapper<Permission> {

    /**
     * 通过用户id获取
     * @param userId
     * @return
     */
    List<Permission> findByUserId(@Param("table")String table,@Param("userId") String userId);
}
