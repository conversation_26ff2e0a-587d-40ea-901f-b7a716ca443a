package com.endovas.cps.dao;


import com.endovas.cps.entity.Organization;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: gxh
 * Date: 2021/6/18
 * Time: 下午3:04
 */
public interface OrganizationDAO extends MysqlBaseRepo<Organization> {

    List<Organization> findByNameContainsAndActiveIsTrue(String name);

    List<Organization> findByCodeStartsWithAndActiveIsTrue(String code);

    List<Organization> findByParentId(String parentId);

    List<Organization> findByIdIn(List<String> ids);

    List<Organization> findByParentIdAndActiveIsTrue(String parentId);

    List<Organization> findByParentIdIsNullAndActiveIsTrueAndBelongAndBelongId(String belong,String belongId);

    List<Organization> findByParentIdIsNullAndBelongAndBelongId(String belong,String belongId);

    List<Organization> findByParentIdIsNotNullAndActiveIsTrueAndBelongAndBelongId(String belong,String belongId);

    List<Organization> findByParentIdIsNotNullAndBelongAndBelongId(String belong,String belongId);

    List<Organization> findByIdAndActiveIsTrue(String id);
}
