package com.endovas.cps.dao;

import com.endovas.cps.entity.user.Expert;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
public interface ExpertDAO extends MysqlBaseRepo<Expert> {
    Expert getByAccount(String account);

    Expert getByTelephone(String telephone);

    List<Expert> findByNickNameContaining(String nickName);

}
