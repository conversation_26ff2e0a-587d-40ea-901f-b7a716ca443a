package com.endovas.cps.dao.mapper;

import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.UserRole;
import io.daige.starter.common.database.mysql.MysqlBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
@Mapper
public interface PlatformUserRoleMapper extends MysqlBaseMapper<UserRole> {

    /**
     * 通过用户id获取
     *
     * @param userId
     * @return
     */
    List<Role> findByUserId(@Param("userId") String userId);

    List<Role> findActiveRoleByUserId(@Param("userId") String userId);
}
