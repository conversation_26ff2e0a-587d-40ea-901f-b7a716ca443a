package com.endovas.cps.dao;

import com.endovas.cps.entity.user.HospitalUser;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
public interface HospitalUserDAO extends MysqlBaseRepo<HospitalUser> {
    HospitalUser getByAccountAndHospitalId(String account, String hospitalId);

    HospitalUser getByTelephoneAndHospitalId(String telephone, String hospitalId);
    List<HospitalUser> findByHospitalId(String hospitalId);
    Long countByHospitalId(String hospitalId);

    List<HospitalUser> findByNickNameContaining(String nickName);
}
