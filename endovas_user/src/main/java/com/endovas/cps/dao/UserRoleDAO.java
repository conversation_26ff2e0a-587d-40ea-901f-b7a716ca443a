package com.endovas.cps.dao;


import com.endovas.cps.entity.UserRole;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色数据处理层
 */
@Repository
public interface UserRoleDAO extends MysqlBaseRepo<UserRole> {

    /**
     * 通过roleId查找
     *
     * @param roleId
     * @return
     */
    List<UserRole> findByRoleId(String roleId);


    /**
     * 通过roleIds查找
     *
     * @param roleIds
     * @return
     */
    List<UserRole> findByRoleIdIn(List<String> roleIds);

    /**
     * 删除用户角色
     *
     * @param userId
     */
    void deleteByUserId(String userId);

    long countByRoleId(String roleId);
    UserRole getByUserIdAndRoleId(String userId, String roleId);
}
