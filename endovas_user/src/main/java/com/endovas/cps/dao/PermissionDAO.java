package com.endovas.cps.dao;


import com.endovas.cps.entity.Permission;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 权限数据处理层
 */
@Repository
public interface PermissionDAO extends MysqlBaseRepo<Permission> {


    /**
     * 通过parentId查找
     *
     * @param parentId
     * @return
     */
    List<Permission> findByParentIdOrderBySortOrder(String parentId);

    /**
     * 通过parentId和类型查找
     *
     * @param type
     * @param parentId
     * @return
     */
    List<Permission> findByTypeAndParentIdOrderBySortOrder(Integer type, String parentId);

    /**
     * 通过parentId查找
     *
     * @param parentIds
     * @return
     */
    List<Permission> findByParentIdIn(List<String> parentIds);

    /**
     * 通过状态获取
     *
     * @param available
     * @return
     */
    List<Permission> findByAvailableOrderBySortOrder(Boolean available);

    /**
     * 通过类型和状态获取
     *
     * @param type
     * @param available
     * @return
     */
    List<Permission> findByTypeAndAvailableOrderBySortOrder(Integer type, Boolean available);

    /**
     * 通过标识名称获取
     *
     * @param code
     * @return
     */
    List<Permission> findByCodeAndBelong(String code,String belong);

    /**
     * 模糊搜索
     *
     * @param title
     * @return
     */
    List<Permission> findByTitleLikeOrderBySortOrder(String title);

    Permission getPermissionById(String id);

    Permission getByParentId(String parentId);

    List<Permission> findByTypeInOrderBySortOrder(List<Integer> type);
    List<Permission> findByBelongOrderBySortOrder(String belong);
}
