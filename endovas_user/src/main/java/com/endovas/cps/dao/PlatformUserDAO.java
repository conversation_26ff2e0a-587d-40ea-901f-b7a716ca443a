package com.endovas.cps.dao;

import com.endovas.cps.entity.user.PlatformUser;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/22
 * Time: 下午2:53
 */
@Repository
public interface PlatformUserDAO extends MysqlBaseRepo<PlatformUser> {
    Long countByAccount(String account);
    Long countByTelephone(String telephone);
    PlatformUser getByAccount(String account);
    PlatformUser getByTelephone(String telephone);
    List<PlatformUser> findByOrganizationId(String organizationId);
    List<PlatformUser> findByOrganizationIdIn(List<String> organizationIds);
    List<PlatformUser> findByNickNameContaining(String nickName);
}
