package com.endovas.cps.dao;

import com.endovas.cps.entity.user.MedAgentUser;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
public interface MedAgentUserDAO extends MysqlBaseRepo<MedAgentUser> {
    MedAgentUser getByAccountAndMedAgentId(String account, String medAgentId);

    MedAgentUser getByTelephoneAndMedAgentId(String telephone, String medAgentId);
    List<MedAgentUser> findByMedAgentIdAndOrganizationIdIsNotNull(String medAgentId);
    List<MedAgentUser> findByMedAgentId(String medAgentId);
    List<MedAgentUser> findByOrganizationIdIn(List<String> orgIds);

    List<MedAgentUser> findByNickNameContaining(String nickName);

}
