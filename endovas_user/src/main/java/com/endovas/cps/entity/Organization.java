package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * Created by IntelliJ IDEA.
 *
 * @author: kang.wang
 * Date: 2021/6/25
 * Time: 下午12:24
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_organization")
@TableName("t_organization")
public class Organization extends MysqlBase {

    @Column(unique = true, columnDefinition = "VARCHAR(500) COMMENT '层级编号'")
    private String code; //编号
    @Column(columnDefinition = "VARCHAR(32) COMMENT '父级别ID'")
    private String parentId; //父级别
    @Column(columnDefinition = "VARCHAR(200) COMMENT '名称'")
    private String name;
    @Column(columnDefinition = "BIT(1) COMMENT '是否可用'")
    private Boolean active;
    public static final String COL_BELONG_ID = "belong_id";

    private String belongId;
    /**
     * @see BelongEnum
     */
    private String belong;
}
