package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 角色和用户关联表
 */
@Getter
@Setter
@Entity
@Table(name="m_user_role")
@TableName("m_user_role")
public class UserRole extends MysqlBase {
    //用户唯一id
    private String userId;
    //角色唯一id
    private String roleId;

}
