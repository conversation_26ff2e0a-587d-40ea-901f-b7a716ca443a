package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 角色和权限的关联表
 */
@Getter
@Setter
@Entity
@TableName("m_role_permission")
@Table(name="m_role_permission")
public class RolePermission extends MysqlBase {
    //角色id
    private String roleId;
    //权限id
    private String permissionId;
}
