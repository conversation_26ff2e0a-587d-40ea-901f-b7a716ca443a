package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


@Getter
@Setter
@Entity
@Table(name = "m_role")
@TableName("m_role")
public class Role extends MysqlBase {

    public static final String BELONG = "belong";
    public static final String BELONG_ID = "belongId";
    public static final String NAME = "name";
    public static final String ACTIVE_STATUS="activeStatus";
    public static final String DATA_TYPE="dataType";


    //角色code,系统添加 以ROLE_开头
    @Column(unique = true, nullable = false)
    private String code;
    //展示的名称
    private String name;

    //数据权限类型，用于用户搜索时
    private Integer dataType = BizCommonConst.ROLE_DATA_TYPE_SAME;

    //数据是否脱敏
    private Boolean desensitize;
    //备注
    private String description;

    private Boolean activeStatus;

    private String belongId;
    private String belong;



}
