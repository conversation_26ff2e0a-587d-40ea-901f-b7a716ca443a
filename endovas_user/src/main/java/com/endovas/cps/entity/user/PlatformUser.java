package com.endovas.cps.entity.user;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/22
 * Time: 下午2:37
 */

@Setter
@Getter
@Entity
@Table(name = "m_platform_user")
@TableName("m_platform_user")
public class PlatformUser extends MysqlBase {
    public static final String COL_ACCOUNT = "account";
    public static final String COL_TELEPHONE = "telephone";
    public static final String COL_NICK_NAME = "nick_name";
    public static final String COL_STATUS = "status";
    public static final String COL_ORGANIZATION_ID = "organization_id";


    //登录账号（手机号或账号）
    private String account;
    //密码
    private String password;
    private String salt;
    //邮箱
    private String email;
    //手机号
    private String telephone;
    //密码最后更新时间
    private LocalDateTime lastPasswordModifyTime;
    //最后登录时间
    private LocalDateTime lastLoginTime;
    //昵称
    private String nickName;

    private String organizationId;

    //拥有的角色
    @TableField(exist = false)
    @Transient
    private List<Role> roles;
    //拥有的权限
    @TableField(exist = false)
    @Transient
    private List<Permission> permissions;

    private String status;



    public static String genSalt() {
        return RandomUtil.randomString(20);
    }
}
