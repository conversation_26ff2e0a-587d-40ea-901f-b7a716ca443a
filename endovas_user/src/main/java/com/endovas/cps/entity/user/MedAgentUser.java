package com.endovas.cps.entity.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/11/30
 * Time: 18:57
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_med_agent_user")
@TableName("t_med_agent_user")
public class MedAgentUser extends MysqlBase {
    public static final String NICK_NAME = "nickName";
    public static final String ENTERPRISE_NAME = "enterpriseName";
    public static final String ACCOUNT = "account";
    public static final String TELEPHONE = "telephone";
    public static final String STATUS = "status";
    public static final String MED_AGENT_ID = "medAgentId";

    public static final String COL_ACCOUNT = "account";
    public static final String COL_TELEPHONE = "telephone";
    public static final String COL_NICK_NAME = "nick_name";
    public static final String COL_STATUS = "status";
    public static final String COL_ORGANIZATION_ID = "organization_id";


    private String nickName;
    private String account;
    private String telephone;
    //密码
    private String password;
    private String salt;

    private Integer dataType;
    private String status;

    private String organizationId;

    //拥有的角色
    @TableField(exist = false)
    @Transient
    private List<Role> roles;
    //拥有的权限
    @TableField(exist = false)
    @Transient
    private List<Permission> permissions;

    //密码最后更新时间
    private LocalDateTime lastPasswordModifyTime;
    //最后登录时间
    private LocalDateTime lastLoginTime;

    private String medAgentId;
}
