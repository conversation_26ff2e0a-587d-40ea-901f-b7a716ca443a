package com.endovas.cps.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/11/30
 * Time: 18:57
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_hospital_user")
@TableName("t_hospital_user")
public class HospitalUser extends MysqlBase {
    public static final String NICK_NAME = "nickName";
    public static final String ENTERPRISE_NAME = "enterpriseName";
    public static final String ACCOUNT = "account";
    public static final String TELEPHONE = "telephone";
    public static final String STATUS = "status";
    public static final String HOSPITAL_ID = "hospitalId";


    private String nickName;
    private String account;
    private String telephone;
    //密码
    private String password;
    private String salt;

    private Integer dataType;
    private String status;
    //密码最后更新时间
    private LocalDateTime lastPasswordModifyTime;
    //最后登录时间
    private LocalDateTime lastLoginTime;

    private String hospitalId;
}
