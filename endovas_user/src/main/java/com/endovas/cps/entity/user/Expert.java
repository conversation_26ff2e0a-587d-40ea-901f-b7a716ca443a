package com.endovas.cps.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:17
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_expert")
@TableName("t_expert")
public class Expert extends MysqlBase {
    public static final String NICK_NAME="nickName";
    public static final String ENTERPRISE_NAME="enterpriseName";
    public static final String ACCOUNT="account";
    public static final String TELEPHONE="telephone";
    public static final String STATUS="status";



    private String enterpriseName;
    private String nickName;
    private String account;
    private String telephone;
    //密码
    private String password;
    private String salt;

    private Integer dataType;

    //最后登录时间
    private LocalDateTime lastLoginTime;

    private String status;

}
