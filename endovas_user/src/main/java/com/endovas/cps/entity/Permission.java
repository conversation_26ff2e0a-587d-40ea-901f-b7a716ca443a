package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 菜单/权限
 */
@Getter
@Setter
@Entity
@TableName("m_permission")
@Table(name="m_permission")
public class Permission extends MysqlBase {
    //默认是始终显示
    private Boolean showAlways = true;


    // 类型-1菜单目录 0页面类型 1操作类型
    private Integer type;

    //菜单/权限标识
    private String code;

    //菜单标题/权限名称
    private String title;

    //后端接口路径
    private String path;

    //前端组件
    private String component;

    //前端图标
    private String icon;

    //前端按钮权限类型
    private String buttonType;

    //父id
    private String parentId;

    //说明备注
    private String description;

    //排序值
    private BigDecimal sortOrder;

    //是否可用
    @TableField("is_available")
    @Column(name = "is_available")
    private Boolean available = true;
    //外部网页链接
    private String url;

    private String belong ;

}
