package com.endovas.cps.config.properties;

import cn.hutool.core.collection.CollUtil;
import io.daige.starter.common.constant.PasswordStrategyConst;
import io.daige.starter.common.enums.LoginMode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

import static com.endovas.cps.config.properties.TokenProperties.PREFIX;


/**
 * 认证服务端 属性
 */
@Configuration
@ConfigurationProperties(prefix = PREFIX)
@Getter
@Setter
public class TokenProperties {
    public static final String PREFIX = "token";
    //验证方式，账户模式(account)或手机号模式(tel)
    private String method = LoginMode.Account.getCode();
    //出错几次出现图形验证码（默认超过三次）
    private Integer showCaptchaTimes = 3;

    /**
     * 密码安全策略BasicPasswordStrategyServiceBasicPasswordStrategyService
     */
    private String passwordStrategy = PasswordStrategyConst.BASIC;
    /**
     * 密码过期时间
     * 单位:天
     */
    private Integer passwordExpireDay = 90;


    /**
     * token默认过期时间
     * 单位：分钟
     */
    private Integer tokenExpireTime = 120;


    /**
     * 用户选择保存登录状态对应token过期时间（天）
     */
    private Integer saveLoginTime = 7;
    /**
     * 限制用户登陆错误次数（次）显示验证码
     */
    private Integer loginTimeLimit = 3;

    //指定不拦截的uri
    private Ignore ignore = new Ignore();

    //登录互踢
    private Boolean loginKick = false;

    @Setter
    @Getter
    public static class Ignore {
        private List<String> baseUri = CollUtil.newArrayList(
                "/**/api-docs/**",
                "/**/api-docs-ext/**",
                "/**/swagger-resources/**",
                "/**/webjars/**",
                "/error",
                "/**/anon/**",
                "/**/static/**",
                "/**/ws/**",
                "/remote/plt",
                "/doc.html",
                "/**/*.json",
                "/DICOM/**"

        );

        private List<String> token = CollUtil.newArrayList("/**/noToken/**", "/**/attachment/view/**");

        public List<String> ignoreUrl() {
            List<String> all = new ArrayList<>();
            all.addAll(getBaseUri());
            all.addAll(getToken());
            return all;
        }
    }


}
