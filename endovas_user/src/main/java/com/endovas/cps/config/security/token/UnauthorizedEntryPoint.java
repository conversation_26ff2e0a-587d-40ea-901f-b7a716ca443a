package com.endovas.cps.config.security.token;

import io.daige.starter.common.constant.CustomResultCodeConst;
import io.daige.starter.common.render.InterceptorErrorRender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/19
 * Time: 下午6:15
 */
@Component
@Slf4j
public class UnauthorizedEntryPoint implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) {
        InterceptorErrorRender.fail(response, "请登录后访问", CustomResultCodeConst.NOT_LOGIN);
    }
}
