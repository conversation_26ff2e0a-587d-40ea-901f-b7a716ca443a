package com.endovas.cps.config.security.token;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.config.properties.TokenProperties;
import com.endovas.cps.config.security.UserDetailsServiceImpl;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.constant.CustomResultCodeConst;
import io.daige.starter.common.constant.SecurityConst;
import io.daige.starter.common.render.InterceptorErrorRender;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.ContextUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static io.daige.starter.common.constant.BizCommonConst.CURRENT_USER;

/**
 *
 */
@Slf4j
public class TokenFilter extends BasicAuthenticationFilter {

    private TokenProperties tokenProperties;

    private RedisHelper redisHelper;

    private UserDetailsServiceImpl userDetailsService;

    public TokenFilter(AuthenticationManager authenticationManager,
                       TokenProperties tokenProperties,
                       RedisHelper redisHelper, UserDetailsServiceImpl userDetailsService) {
        super(authenticationManager);
        this.tokenProperties = tokenProperties;
        this.redisHelper = redisHelper;
        this.userDetailsService = userDetailsService;
    }

    public TokenFilter(AuthenticationManager authenticationManager, AuthenticationEntryPoint authenticationEntryPoint) {
        super(authenticationManager, authenticationEntryPoint);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        String userToken = request.getHeader(SecurityConst.JWT_HEADER_KEY_TOKEN);

        if (StrUtil.isEmpty(userToken)) {
            userToken = request.getParameter(SecurityConst.JWT_HEADER_KEY_TOKEN);
        }
        boolean notValid = StrUtil.isBlank(userToken);

        if (notValid) {
            ContextUtil.setUserId(null);
            chain.doFilter(request, response);
            return;
        }
        UsernamePasswordAuthenticationToken authentication = null;
        if (StrUtil.isNotEmpty(userToken)) {
            authentication = getAuthentication(userToken, request, response);
        }

        if (Objects.isNull(authentication)) {
            InterceptorErrorRender.fail(response, "登录已失效，请重新登录", CustomResultCodeConst.NOT_LOGIN);
        } else {
            SecurityContextHolder.getContext().setAuthentication(authentication);
            chain.doFilter(request, response);
        }
    }

    private UsernamePasswordAuthenticationToken getAuthentication(String token, HttpServletRequest request, HttpServletResponse response) {
        String currentUser = redisHelper.strGet(RedisCacheKeys.getTokenPrefixKey(token));
        if (StrUtil.isBlank(currentUser)) {
            return null;
        }
        LoginUser loginUser = JSONUtil.toBean(currentUser, LoginUser.class);

        String account = loginUser.getAccount();
        List<GrantedAuthority> authorities = userDetailsService.getCurrUserPerms(loginUser.getId());

        request.setAttribute(CURRENT_USER, loginUser);
        ContextUtil.setUserId(loginUser.getId());

        if (StrUtil.isNotBlank(account)) {
            //踩坑提醒 此处password不能为null
            User principal = new User(account, "", authorities);
            return new UsernamePasswordAuthenticationToken(principal, null, authorities);
        }
        return null;
    }


}

