package com.endovas.cps.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "init")
public class InitAccountProperties {

    /**
     * 超级管理员账号
     */
    private String account;
    private String password;
    private String telephone;


}
