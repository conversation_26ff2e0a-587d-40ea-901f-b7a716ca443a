package com.endovas.cps.config.security;

import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.user.MedAgentUser;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.service.agent.MedAgentUserService;
import com.endovas.cps.service.hospital.HospitalUserService;
import com.endovas.cps.service.platform.PlatformUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Slf4j
@Component
public class UserDetailsServiceImpl {


    @Autowired
    private PlatformUserService platformUserService;
    @Autowired
    private MedAgentUserService medAgentUserService;
    @Autowired
    private HospitalUserService hospitalUserService;


    public List<GrantedAuthority> getCurrUserPerms(String userId) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        PlatformUser platformUser = platformUserService.getById(userId);
        if (Objects.nonNull(platformUser)) {
            for (Permission p : platformUser.getPermissions()) {
                authorities.add(new SimpleGrantedAuthority(p.getCode()));
            }
        }

        MedAgentUser medAgentUser = medAgentUserService.getById(userId);
        if (Objects.nonNull(medAgentUser)) {
            for (Permission p : medAgentUser.getPermissions()) {
                authorities.add(new SimpleGrantedAuthority(p.getCode()));
            }
        }

        return authorities;
    }
}
