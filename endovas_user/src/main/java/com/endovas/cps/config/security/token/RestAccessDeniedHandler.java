package com.endovas.cps.config.security.token;


import io.daige.starter.common.constant.CustomResultCodeConst;
import io.daige.starter.common.render.InterceptorErrorRender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 */
@Component
@Slf4j
public class RestAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) {
        InterceptorErrorRender.fail(response, accessDeniedException.getMessage(), CustomResultCodeConst.NO_ACCESS);
    }

}
