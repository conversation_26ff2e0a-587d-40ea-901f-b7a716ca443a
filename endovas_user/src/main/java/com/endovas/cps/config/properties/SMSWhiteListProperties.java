package com.endovas.cps.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;


/**
 * 短信验证码白名单,添加之后不需要验证短信
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/24
 * Time: 下午6:16
 */
@Configuration
@ConfigurationProperties(prefix = "sms.white-list")
@Getter
@Setter
public class SMSWhiteListProperties {
    //是否启用,默认不启用
    private Boolean enable = false;
    private List<String> telephone;

}
