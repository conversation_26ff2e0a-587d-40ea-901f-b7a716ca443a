package com.endovas.cps.config;

import io.daige.starter.plugin.sms.core.SmsProperties;
import io.daige.starter.plugin.sms.service.AliyunSmsService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/12/3
 * Time: 下午4:20
 */
@Configuration
public class SmsConfig {
    @Bean
    public SmsProperties smsProperties() {
        return new SmsProperties();
    }

    @Bean
    public AliyunSmsService aliyunSmsService(SmsProperties smsProperties) {
        return new AliyunSmsService(smsProperties);
    }


}
