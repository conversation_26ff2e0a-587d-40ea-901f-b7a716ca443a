package com.endovas.cps.config.security;


import com.endovas.cps.config.properties.TokenProperties;
import com.endovas.cps.config.security.permission.MyFilterSecurityInterceptor;
import com.endovas.cps.config.security.token.RestAccessDeniedHandler;
import com.endovas.cps.config.security.token.TokenFilter;
import com.endovas.cps.config.security.token.UnauthorizedEntryPoint;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;

/**
 * Security 核心配置类
 * 开启控制权限至Controller
 */
@Slf4j
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private TokenProperties tokenProperties;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;


    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private RestAccessDeniedHandler accessDeniedHandler;

    @Autowired
    private MyFilterSecurityInterceptor myFilterSecurityInterceptor;

    @Autowired
      private UnauthorizedEntryPoint entryPoint;



    @Override
    protected void configure(HttpSecurity http) throws Exception {
        ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry registry = http
                .authorizeRequests();
        // 除配置文件忽略路径,其它所有请求都需经过认证和授权
        for (String url : tokenProperties.getIgnore().ignoreUrl()) {
            registry.antMatchers(url).permitAll();
        }

        registry
            .and()
                // 允许网页iframe
                .headers().frameOptions().disable()
            .and()
                .logout()
                .permitAll()
            .and()
                .authorizeRequests()
                // 任何请求
                .anyRequest()
                // 需要身份认证
                .authenticated()
            .and()
                // 允许跨域
                .cors()
            .and()
                // 关闭跨站请求防护
                .csrf().disable()
                // 前后端分离采用JWT 不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
                // 自定义权限拒绝处理类
                .exceptionHandling()
                .accessDeniedHandler(accessDeniedHandler)
                .authenticationEntryPoint(entryPoint)
            .and()
                // 添加自定义权限过滤器
                .addFilterBefore(myFilterSecurityInterceptor, FilterSecurityInterceptor.class)
                // 添加JWT过滤器 除已配置的其它请求都需经过此过滤器
                .addFilter(new TokenFilter(authenticationManager(), tokenProperties, redisHelper, userDetailsService));
    }
}
