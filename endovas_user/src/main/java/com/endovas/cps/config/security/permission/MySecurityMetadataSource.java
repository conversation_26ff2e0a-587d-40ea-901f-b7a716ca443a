package com.endovas.cps.config.security.permission;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.pojo.dto.PermissionDTO;
import com.endovas.cps.service.role.PermissionService;
import com.google.common.collect.Lists;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.Collection;
import java.util.List;

/**
 * 权限资源管理器
 * 为权限决断器提供支持
 * 加载资源与权限的对应关系
 */
@Slf4j
@Component
public class MySecurityMetadataSource implements FilterInvocationSecurityMetadataSource {
    /***
     *
     *  MySecurityMetadataSource
     *   加载资源与权限的对应关系
     *   实现FilterInvocationSecurityMetadataSource接口也是必须的。 首先，这里从数据库中获取信息。 其中loadResourceDefine方法不是必须的，
     *           这个只是加载所有的资源与权限的对应关系并缓存起来，避免每次获取权限都访问数据库（提高性能），然后getAttributes根据参数（被拦截url）返回权限集合。
     *           这种缓存的实现其实有一个缺点，因为loadResourceDefine方法是放在构造器上调用的，而这个类的实例化只在web服务器启动时调用一次，那就是说loadResourceDefine方法只会调用一次，
     *           如果资源和权限的对应关系在启动后发生了改变，那么缓存起来的权限数据就和实际授权数据不一致，那就会授权错误了。但如果资源和权限对应关系是不会改变的，这种方法性能会好很多。
     *           要想解决 权限数据的一致性 可以直接在getAttributes方法里面调用数据库操作获取权限数据，通过被拦截url获取数据库中的所有权限，封装成Collection<ConfigAttribute>返回就行了。（灵活、简单)
     *
     *           器启动加载顺序：1：调用loadResourceDefine()方法  2：调用supports()方法   3：调用getAllConfigAttributes()方法
     *
     *
     */

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private RedisHelper redisHelper;





    /**
     * 参数是要访问的url，返回这个url对于的所有权限（或角色）
     * 每次请求后台就会调用 得到请求所拥有的权限
     * 这个方法在url请求时才会调用，服务器启动时不会执行这个方法
     * getAttributes这个方法会根据你的请求路径去获取这个路径应该是有哪些权限才可以去访问。
     *
     */

    /**
     * 判定用户请求的url是否在权限表中
     * 如果在权限表中，则返回给decide方法，用来判定用户是否有此权限
     * 如果不在权限表中则放行
     *
     * @param o
     * @return
     * @throws IllegalArgumentException
     */
    @Override
    public Collection<ConfigAttribute> getAttributes(Object o) throws IllegalArgumentException {

        String permissionList = redisHelper.strGet(RedisCacheKeys.getPermissionList());
        if (StrUtil.isEmpty(permissionList)) {
            permissionService.loadResourceDefine();
        }
        List<PermissionDTO> permissionDTOList = JSONUtil.toList(permissionList, PermissionDTO.class);
        //Object中包含用户请求request
        String url = ((FilterInvocation) o).getRequestUrl();
        PathMatcher pathMatcher = new AntPathMatcher();
        for (PermissionDTO permissionDTO : permissionDTOList) {
            String resURL = permissionDTO.getPath();
            if (StrUtil.isNotBlank(resURL) && pathMatcher.match(resURL, url)) {
                return Lists.newArrayList(new SecurityConfig(permissionDTO.getCode()));
            }
        }
        return null;
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        //要返回true  不然要报异常　　 SecurityMetadataSource does not support secure object class: class

        return true;
    }
}
