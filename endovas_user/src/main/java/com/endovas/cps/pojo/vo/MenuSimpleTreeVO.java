package com.endovas.cps.pojo.vo;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.Permission;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *
 */
@Setter
@Getter
public class MenuSimpleTreeVO extends BaseVO implements BeanConvert<MenuSimpleTreeVO, Permission> {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "父级id")
    private String parentId;
    @ApiModelProperty(value = "菜单标题")
    private String title;

    @ApiModelProperty(value = "层级 0级顶级菜单 1级菜单 2级菜单 3级菜单 ")
    private Integer level;

    @ApiModelProperty(value = "子菜单/权限")
    private List<MenuSimpleTreeVO> children;

    @Override
    public MenuSimpleTreeVO convertFrom(Permission input) {
        BeanUtil.copyProperties(input, this);
        return this;
    }
}
