package com.endovas.cps.pojo.fo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;


@Data
@ApiModel(value = "权限添加")
@AllArgsConstructor
public class RoleEditFO {

    private String id;

    @ApiModelProperty(value = "角色名称")
    private String name;
    @ApiModelProperty(value = "数据是否脱敏")
    private Boolean desensitize;
    @ApiModelProperty(value = "数据范围")
    private Integer dataType;
    @ApiModelProperty(value = "备注")
    private String description;

}
