package com.endovas.cps.pojo.vo;

import com.endovas.cps.entity.Organization;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel
public class OrganizationTreeVO extends BaseVO implements BeanConvert<OrganizationTreeVO, Organization> {

    private String id;
    private String code;
    private String parentId;
    private String parentName;
    private String name;
    private Boolean available;
    private List<OrganizationTreeVO> children = new ArrayList<>();
}
