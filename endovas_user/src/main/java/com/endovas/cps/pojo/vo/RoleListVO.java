package com.endovas.cps.pojo.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.endovas.cps.entity.Role;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/2
 * Time: 下午7:52
 */
@Setter
@Getter
@ApiModel(value = "角色")
public class RoleListVO extends BaseVO implements BeanConvert<RoleListVO, Role> {
    private String id;
    @ApiModelProperty(value = "角色code,系统添加 以ROLE_开头")
    private String code;
    @ApiModelProperty(value = "角色名称")
    private String name;
    @ApiModelProperty(value = "备注")
    private String description;
    @ApiModelProperty(value = "数据权限类型 0全部默认 1本公司及以下 2本公司")
    private Integer dataType;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    private List<RolePermissionVO> permissions;
    @ApiModelProperty(value = "账号数量")
    private long accountAmount;
    @ApiModelProperty(value = "启用状态 ")
    private Boolean activeStatus;
    private String belong;

    @Override
    public RoleListVO convertFrom(Role input) {
        String[] skip = {MysqlBase.CREATE_TIME};
        BeanUtil.copyProperties(input, this, skip);
        this.createTime = DateUtil.format(input.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN);
        return this;
    }
}
