package com.endovas.cps.pojo.fo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/16
 * Time: 16:21
 */
@Setter
@Getter
@ApiModel(value = "短信发送验证", description = "短信验证")
public class UserSmsCheckFO {
    @ApiModelProperty(value = "验证码ID")
    @NotEmpty(message = "captchaKey不能为空")
    private String captchaKey;
    @ApiModelProperty(value = "验证码")
    @NotEmpty(message = "短信验证码不能为空")
    private String captcha;
}
