package com.endovas.cps.pojo.vo.agent;

import com.endovas.cps.entity.user.MedAgentUser;
import com.endovas.cps.pojo.dto.RoleSelectDTO;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/4
 * Time: 11:13
 */
@Setter
@Getter
@ApiModel
public class MedAgentUserListVO implements BeanConvert<MedAgentUserListVO, MedAgentUser> {
    private String id;

    @ApiModelProperty(value = "工号")
    private String account;

    @ApiModelProperty(value = "名称")
    private String nickName;

    @ApiModelProperty(value = "手机号")
    private String telephone;


    @ApiModelProperty(value = "账号状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "机构id")
    private String organizationId;

    @ApiModelProperty(value = "机构名称")
    private String organizationName;

    @ApiModelProperty(value = "角色权限")
    private List<RoleSelectDTO> roles;

    private String email;
}
