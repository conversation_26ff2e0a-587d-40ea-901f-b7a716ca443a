package com.endovas.cps.pojo.vo;

import com.endovas.cps.pojo.dto.RoleSelectDTO;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@ApiModel
public class UserListVO extends BaseVO  {

    private String id;

    @ApiModelProperty(value = "工号")
    private String account;

    @ApiModelProperty(value = "名称")
    private String nickName;

    @ApiModelProperty(value = "手机号")
    private String telephone;


    @ApiModelProperty(value = "账号状态（是否启用）")
    private String  status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "机构id")
    private String organizationId;

    @ApiModelProperty(value = "机构名称")
    private String organizationName;



    //邮箱
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "角色权限")
    private List<RoleSelectDTO> roles;
}
