package com.endovas.cps.pojo.fo.expert;

import com.endovas.cps.entity.user.Expert;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.validator.annotation.Telephone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:44
 */
@Data
@ApiModel(value = "添加专家账户")
public class ExpertAddFO implements BeanConvert<ExpertAddFO, Expert> {
    @ApiModelProperty(value = "单位名称")
    private String enterpriseName;
    @ApiModelProperty(value = "姓名", required = true)
    @NotEmpty(message = "姓名不能为空")
    private String nickName;
    @ApiModelProperty(value = "账号", required = true)
    @NotEmpty(message = "账号不能为空")
    private String account;

    @ApiModelProperty(value = "手机号码", required = true)
    @Telephone(require = true)
    private String telephone;
    @ApiModelProperty(value = "数据范围权限", required = true)
    @NotNull(message = "数据范围权限不能为空")
    private Integer dataType;
}
