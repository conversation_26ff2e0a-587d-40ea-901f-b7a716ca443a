package com.endovas.cps.pojo.vo;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.RolePermission;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/12
 * Time: 上午11:56
 */
@Setter
@Getter
@ApiModel(value = "角色所拥有的权限")
public class RolePermissionVO extends BaseVO implements BeanConvert<RolePermissionVO, RolePermission> {
    //角色id
    private String roleId;
    //权限id
    private String permissionId;

    @Override
    public RolePermissionVO convertFrom(RolePermission input) {
        BeanUtil.copyProperties(input, this);
        return this;
    }
}
