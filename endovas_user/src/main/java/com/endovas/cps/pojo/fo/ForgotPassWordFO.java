package com.endovas.cps.pojo.fo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/8/27
 * Time: 下午5:09
 */
@Setter
@Getter
@ApiModel
public class ForgotPassWordFO {
    @ApiModelProperty(value = "登录手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String telephone;
    @ApiModelProperty(value = "新密码", required = true)
    private String password;
    @ApiModelProperty(value = "captchaKey", required = true)
    @NotEmpty(message = "captchaKey不能为空")
    private String captchaKey;
    @ApiModelProperty(value = "短信验证码", required = true)
    @NotEmpty(message = "短信验证码不能为空")
    private String captcha;


    @ApiModelProperty(value = "归属系统")
    private String belong;

    @ApiModelProperty(value = "公司名称")
    private String enterpriseId;
}
