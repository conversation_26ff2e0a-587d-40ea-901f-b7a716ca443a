package com.endovas.cps.pojo.fo;

import io.daige.starter.common.validator.annotation.Telephone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/8/29
 * Time: 上午4:16
 */
@Setter
@Getter
@ApiModel
public class SendCaptchaSmsFO {
    @ApiModelProperty(value = "登录手机号",required = true)
    @Telephone(require = true,message = "请正确填写手机号")
    private String telephone;
    @ApiModelProperty(value = "captchaKey",required = true)
    @NotEmpty(message = "captchaKey不能为空")
    private String captchaKey;
    @ApiModelProperty(value = "图形验证码",required = true)
    @NotEmpty(message = "图形验证码不能为空")
    private String captcha;

    @ApiModelProperty(value = "业务范围",required = true)
    private String businessScope;
}
