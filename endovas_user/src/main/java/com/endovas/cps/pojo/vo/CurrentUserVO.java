package com.endovas.cps.pojo.vo;

import io.daige.starter.common.pojo.vo.BaseVO;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/3
 * Time: 下午4:01
 */
@Setter
@Getter
@ApiModel(value = "当前用户信息")
public class CurrentUserVO extends BaseVO {
    @ApiModelProperty(value = "用户信息")
    private LoginUser loginUser;
    @ApiModelProperty(value = "菜单和按钮信息")
    private MenuTreeVO menuAndBtn;
}
