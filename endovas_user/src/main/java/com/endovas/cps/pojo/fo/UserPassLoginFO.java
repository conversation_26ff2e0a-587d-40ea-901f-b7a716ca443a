package com.endovas.cps.pojo.fo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/11/17
 * Time: 下午5:18
 */
@Setter
@Getter
@ApiModel(value = "UserPassLoginFO", description = "登录参数")
public class UserPassLoginFO {
    @ApiModelProperty(value = "账号")
    @NotEmpty(message = "账户不能为空")
    private String account;
    @ApiModelProperty(value = "密码")
    @NotEmpty(message = "密码不能为空")
    private String password;
    @ApiModelProperty(value = "归属系统")
    @NotEmpty(message = "归属系统不能为空")
    private String belong;

    @ApiModelProperty(value = "公司名称")
    @NotEmpty(message = "公司名称不能为空")
    private String enterpriseId;

    @ApiModelProperty(value = "是否记住登录状态")
    private Boolean autoLogin;


    @ApiModelProperty(value = "验证码ID")
    private String captchaKey;
    @ApiModelProperty(value = "验证码")
    private String captcha;
}
