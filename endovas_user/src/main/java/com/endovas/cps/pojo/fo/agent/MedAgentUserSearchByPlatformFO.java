package com.endovas.cps.pojo.fo.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:32
 */

@Setter
@Getter
@ApiModel
public class MedAgentUserSearchByPlatformFO {

    private String medAgentId;

    @ApiModelProperty(value = "代理商名称")
    private String medAgentName;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "姓名")
    private String nickName;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "手机号码")
    private String telephone;
}
