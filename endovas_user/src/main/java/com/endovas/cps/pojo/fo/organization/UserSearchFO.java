package com.endovas.cps.pojo.fo.organization;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class UserSearchFO {

    @ApiModelProperty(value = "成员账号")
    private String account;
    @ApiModelProperty(value = "成员姓名")
    private String nickName;

    @ApiModelProperty(value = "手机号码")
    private String telephone;


    @ApiModelProperty(value = "账号状态")
    private Boolean available;

    @ApiModelProperty(value = "所属组织")
    private String orgName;

    @ApiModelProperty(value = "1 仅展示部门的直属成员 2 展示直属和下属部门成员")
    private Integer orgSearchRange;

    @ApiModelProperty(value = "角色ID")
    private String roleId;
    @ApiModelProperty(value = "数据权限ID")
    private String accessId;

    private String organizationId;
    private String loginUserOrgId;


}
