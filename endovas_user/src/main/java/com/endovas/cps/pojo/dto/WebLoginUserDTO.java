package com.endovas.cps.pojo.dto;

import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import io.daige.starter.common.enums.BelongEnum;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/5
 * Time: 15:21
 */
@Setter
@Getter
public class WebLoginUserDTO {
    private String id;
    private BelongEnum belong;
    private String account;
    private String telephone;
    private String salt;
    private String password;
    private String nickName;
    private String organizationId;
    private Integer dataType;

    private String status;

    private List<Permission> permissions;
    private List<Role> roles;

    private String medAgentId;
    private String hospitalId;

    private LocalDateTime lastPasswordModifyTime;

}
