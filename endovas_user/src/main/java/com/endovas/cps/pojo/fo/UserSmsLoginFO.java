package com.endovas.cps.pojo.fo;

import io.daige.starter.common.validator.annotation.Telephone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 * User: bin.yu
 * Date: 2020/11/17
 * Time: 下午5:18
 */
@Setter
@Getter
@ApiModel(value = "UserSmsLoginFO", description = "登录参数")
public class UserSmsLoginFO {
    @ApiModelProperty(value = "手机号")
    @Telephone(message = "请正确填写手机号码", require = true)
    private String telephone;
    @ApiModelProperty(value = "是否记住登录状态")
    private Boolean autoLogin;


    @ApiModelProperty(value = "验证码ID")
    private String captchaKey;
    @ApiModelProperty(value = "验证码")
    private String captcha;
}
