package com.endovas.cps.pojo.fo.platform;

import com.endovas.cps.entity.user.PlatformUser;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.validator.annotation.Email;
import io.daige.starter.common.validator.annotation.Telephone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/4
 * Time: 10:38
 */
@Setter
@Getter
@ApiModel(value = "添加用户参数")
@AllArgsConstructor
@RequiredArgsConstructor
public class PlatformUserAddFO implements BeanConvert<PlatformUserAddFO, PlatformUser> {

    @ApiModelProperty(value = "员工姓名", required = true)
    @NotEmpty(message = "员工姓名不能为空")
    private String nickName;

    @ApiModelProperty(value = "用户名", required = true)
    @NotEmpty(message = "用户名不能为空")
    private String account;

    @ApiModelProperty(value = "所属组织id", required = true)
    @NotEmpty(message = "所属组织id不能为空")
    private String organizationId;

    @ApiModelProperty(value = "菜单权限角色", required = true)
    @NotNull(message = "菜单权限角色不能为空")
    private List<String> roleId;

    @ApiModelProperty(value = "手机号码", required = true)
    @NotEmpty(message = "手机号码不能为空")
    @Telephone(message = "手机号码无效")
    private String telephone;

    @ApiModelProperty(value = "邮箱", required = true)
    @Email(message = "邮箱无效",require = true)
    private String email;


}
