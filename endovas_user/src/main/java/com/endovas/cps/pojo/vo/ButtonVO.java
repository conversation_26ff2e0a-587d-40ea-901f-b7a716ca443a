package com.endovas.cps.pojo.vo;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.Permission;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 *
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ButtonVO extends BaseVO implements BeanConvert<ButtonVO, Permission> {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "父id")
    private String parentId;

    @ApiModelProperty(value = "菜单/权限名称")
    private String code;

    @ApiModelProperty(value = "始终显示")
    private Boolean showAlways;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "类型 -1:目录  0:菜单 1:按钮 2:页面")
    private Integer type;

    @ApiModelProperty(value = "菜单标题")
    private String title;

    @ApiModelProperty(value = "调用的后端接口地址,支持模糊匹配")
    private String path;

    @ApiModelProperty(value = "前端组件")
    private String component;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "网页链接")
    private String url;

    @ApiModelProperty(value = "按钮权限类型")
    private String buttonType;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;

    @Override
    public ButtonVO convertFrom(Permission input) {
        BeanUtil.copyProperties(input, this);
        return this;
    }
}
