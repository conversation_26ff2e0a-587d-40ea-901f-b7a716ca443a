package com.endovas.cps.pojo.vo.hospital;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.user.HospitalUser;
import com.endovas.cps.enums.UserStatusEnum;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:28
 */
@Getter
@Setter
@ApiModel
public class HospitalUserListVO extends BaseVO implements BeanConvert<HospitalUserListVO, HospitalUser> {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String id;
    private String hospitalId;
    private String hospitalName;

    @ApiModelProperty(value = "姓名", required = true)
    private String nickName;
    @ApiModelProperty(value = "账号", required = true)
    private String account;
    @ApiModelProperty(value = "手机号", required = true)
    private String telephone;
    @ApiModelProperty(value = "数据范围权限", notes = "全部:0,自己:2", required = true)
    private Integer dataType;
    @ApiModelProperty(value = "账号状态", notes = "正常:NORMAL、待审核:AUDIT、账户暂停:CLOSE", required = true)
    private String status;

    @Override
    public HospitalUserListVO convertFrom(HospitalUser input) {
        BeanUtil.copyProperties(input, this);
        input.setStatus(UserStatusEnum.get(input.getStatus()).getDesc());
        return this;
    }
}
