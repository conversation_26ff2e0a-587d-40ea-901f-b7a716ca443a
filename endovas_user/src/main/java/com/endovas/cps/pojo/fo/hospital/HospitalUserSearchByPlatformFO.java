package com.endovas.cps.pojo.fo.hospital;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:32
 */

@Setter
@Getter
@ApiModel
public class HospitalUserSearchByPlatformFO {
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    private String hospitalId;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "姓名")
    private String nickName;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "手机号码")
    private String telephone;
}
