package com.endovas.cps.pojo.fo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Data
@ApiModel(value = "菜单资源添加")
@AllArgsConstructor
public class PermissionFO {

    private String id;

    @ApiModelProperty(value = "始终显示 默认是")
    private Boolean showAlways;

    @ApiModelProperty(value = "层级 0级顶级菜单 1级菜单 2级菜单 3级菜单 ")
    private Integer level;

    @NotNull(message = "类型不能为空")
    @ApiModelProperty(value = "类型-1菜单目录 0页面类型 1操作类型")
    private Integer type;

    @NotNull(message = "权限标志不能为空")
    @ApiModelProperty(value = "菜单/权限标志")
    private String code;

    @NotNull(message = "菜单标题/权限名称不能为空")
    @ApiModelProperty(value = "菜单标题/权限名称")
    private String title;

    @ApiModelProperty(value = "页面路径/资源链接url")
    private String path;

    @ApiModelProperty(value = "前端组件")
    private String component;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "按钮权限类型")
    private String buttonType;

    @ApiModelProperty(value = "父id")
    private String parentId;

    @ApiModelProperty(value = "说明备注")
    private String description;

    @NotNull(message = "排序值不能为空")
    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;

    @ApiModelProperty(value = "是否启用")
    private Boolean available;

    @ApiModelProperty(value = "网页链接")
    private String url;

    @ApiModelProperty(value = "归属于",notes = "PLATFORM_USER、MED_FACTORY_USER、MED_AGENT_USER")
    private String belong;
}
