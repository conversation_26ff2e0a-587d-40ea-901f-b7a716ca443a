<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.endovas.cps.dao.mapper.PermissionMapper">

    <select id="findByUserId" resultType="com.endovas.cps.entity.Permission">
      SELECT DISTINCT p.id, p.show_always, p.code,p.title, p.path, p.icon, p.type, p.component,
        p.button_type, p.parent_id, p.sort_order, p.description, p.is_available, p.url
      FROM ${table} u
      LEFT JOIN m_user_role ur ON u.id = ur.user_id
      LEFT JOIN m_role_permission rp ON ur.role_id = rp.role_id
      LEFT JOIN m_permission p ON p.id = rp.permission_id
      WHERE  p.is_available = true and u.id = #{userId}

      ORDER BY p.sort_order ASC
    </select>
</mapper>
