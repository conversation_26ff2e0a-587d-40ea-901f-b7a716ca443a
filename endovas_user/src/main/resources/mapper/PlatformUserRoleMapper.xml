<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.endovas.cps.dao.mapper.PlatformUserRoleMapper">

    <select id="findByUserId" resultType="com.endovas.cps.entity.Role">
        SELECT r.id id, code,  r.name, r.data_type, r.desensitize
        FROM m_user_role ur
                 LEFT JOIN m_role r
                           ON ur.role_id = r.id
        WHERE user_Id = #{userId}
    </select>

    <select id="findActiveRoleByUserId" resultType="com.endovas.cps.entity.Role">
        SELECT r.id id, code,  r.name, r.data_type
        FROM m_user_role ur
                 LEFT JOIN m_role r
                           ON ur.role_id = r.id
        WHERE user_id = #{userId}
          and r.active_status = 1
    </select>
</mapper>
