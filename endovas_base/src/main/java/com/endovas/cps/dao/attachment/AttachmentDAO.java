package com.endovas.cps.dao.attachment;

import com.endovas.cps.entity.attachment.Attachment;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午2:02
 */
@Repository
public interface AttachmentDAO extends MysqlBaseRepo<Attachment> {
    Attachment getByTargetIdAndType(String targetId, String type);

    Attachment getAttachmentById(String id);
    Attachment getByUrl(String url);

    Attachment getByAttachmentTempId(String attachmentTempId);

    List<Attachment> findByTargetIdAndType(String targetId, String type);

    List<Attachment> findByTargetIdAndTypeAndDelIsTrue(String targetId, String type);

    List<Attachment> findByIdIn(List<String> id);
}
