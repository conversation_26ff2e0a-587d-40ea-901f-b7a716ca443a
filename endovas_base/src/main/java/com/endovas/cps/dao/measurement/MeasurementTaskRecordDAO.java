package com.endovas.cps.dao.measurement;

import com.endovas.cps.entity.measurement.MeasurementTaskRecord;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:30
 */
@Repository
public interface MeasurementTaskRecordDAO extends MysqlBaseRepo<MeasurementTaskRecord> {

    List<MeasurementTaskRecord> findByMeasurementIdOrderByCreateTimeDesc(String measurementId);
    List<MeasurementTaskRecord> findByOperaterIdAndContent(String operaterId, String content);
}
