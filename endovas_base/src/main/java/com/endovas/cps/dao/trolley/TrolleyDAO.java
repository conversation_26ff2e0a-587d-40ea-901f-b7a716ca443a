package com.endovas.cps.dao.trolley;

import com.endovas.cps.entity.trolley.Trolley;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:26
 */
@Repository
public interface TrolleyDAO extends MysqlBaseRepo<Trolley> {
    Trolley getByUdi(String udi);
    List<Trolley> findByUdiContaining(String udi);
    List<Trolley> findBySysVer(String sysVer);
    List<Trolley> findBySoftVer(String softVer);

    Page<Trolley> findByMedAgentId(String medAgentId, Pageable pageable);

    List<Trolley> findByHospitalId(String hospitalId);

    List<Trolley> findByMedAgentId(String medAgentId);

    List<Trolley> findByMedAgentIdAndStatus(String medAgentId, String status);

    Long countByHospitalId(String hospitalId);

    Long countByMedAgentId(String medAgentId);

    List<Trolley> findByIdIn(List<String> trolleyIds);
}
