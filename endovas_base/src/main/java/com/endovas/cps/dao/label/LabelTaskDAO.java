package com.endovas.cps.dao.label;

import com.endovas.cps.entity.label.LabelTask;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:30
 */
@Repository
public interface LabelTaskDAO extends MysqlBaseRepo<LabelTask> {
    Long countByCreatorId(String creatorId);
    List<LabelTask> findByTaskStatus(String taskStatus);

    Long countByCreatorIdAndCreateTimeBetween(String creatorId, LocalDateTime start, LocalDateTime end);
    Long countByLabelerIdAndTaskStatusAndCreateTimeBetween(String labelerId, String taskStatus,LocalDateTime start, LocalDateTime end);

    Long countByLabelerIdAndTaskStatus(String labelerId,String taskStatus);
    Long countByTaskStatus(String taskStatus);
    Long countByLabelerIdAndSurgeryTypeIdInAndTaskStatus(String labelerId, List<String> surgeryTypeIds, String taskStatus);

}
