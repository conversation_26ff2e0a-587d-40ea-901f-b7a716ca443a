package com.endovas.cps.dao.measurement;

import com.endovas.cps.entity.measurement.MeasurementTask;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:30
 */
@Repository
public interface MeasurementTaskDAO extends MysqlBaseRepo<MeasurementTask> {

    MeasurementTask findByMeasurementTaskId(String measurerTaskId);
    MeasurementTask findFirstByMeasurerIdAndEnvPrepareStatusOrderByCreateTimeDesc(String measurerId, String envPrepareStatus);

    List<MeasurementTask> findByTaskStatus(String taskStatus);
    List<MeasurementTask> findByPatientIdAndTaskStatus(String patientId,String taskStatus);
    List<MeasurementTask> findByMeasurerIdAndTaskStatus(String measurerId,String taskStatus);

    Long countByCreatorIdAndCreateTimeBetween(String creatorId, LocalDateTime start, LocalDateTime end);
    Long countByMeasurerIdAndTaskStatusAndCreateTimeBetween(String measurerId, String taskStatus,LocalDateTime start, LocalDateTime end);

    Long countByMeasurerIdAndTaskStatus(String measurerId,String taskStatus);
    Long countByMeasurerIdAndTaskStatusIn(String measurerId,List<String> taskStatus);
    Long countByTaskStatus(String taskStatus);
    Long countByMeasurerIdAndSurgeryTypeIdInAndTaskStatus(String measurerId,List<String> surgeryTypeIds,String taskStatus);

}
