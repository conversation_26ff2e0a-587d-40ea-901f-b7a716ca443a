package com.endovas.cps.dao.measurement;

import com.endovas.cps.entity.measurement.MeasurementTaskResult;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:30
 */
@Repository
public interface MeasurementTaskResultDAO extends MysqlBaseRepo<MeasurementTaskResult> {
    MeasurementTaskResult findByMeasurementIdAndSourceTypeAndDelIsFalse(String measurementId, String sourceType);
    List<MeasurementTaskResult> findByMeasurementIdAndDelIsFalse(String measurementId);

    int countByMeasurementIdAndResultFileHashAndSourceType(String measurementId, String resultFileName, String sourceType);
}
