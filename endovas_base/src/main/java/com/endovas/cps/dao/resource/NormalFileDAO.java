package com.endovas.cps.dao.resource;

import com.endovas.cps.entity.resource.NormalFile;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:26
 */
@Repository
public interface NormalFileDAO extends MysqlBaseRepo<NormalFile> {
    List<NormalFile>  findByPatientId(String patientId);

}
