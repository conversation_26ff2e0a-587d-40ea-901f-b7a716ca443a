package com.endovas.cps.dao.label;

import com.endovas.cps.entity.label.LabelTaskRecord;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:30
 */
@Repository
public interface LabelTaskRecordDAO extends MysqlBaseRepo<LabelTaskRecord> {

    List<LabelTaskRecord> findByLabelTaskIdOrderByCreateTimeDesc(String labelTaskId);
    List<LabelTaskRecord> findByOperaterIdAndContent(String operaterId, String content);
}
