package com.endovas.cps.dao.measurement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.endovas.cps.entity.measurement.MeasurementTaskResult;
import com.endovas.cps.pojo.dto.measurement.MeasurementTaskResultDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_measurement_task_result】的数据库操作Mapper
* @createDate 2024-11-29 19:25:28
* @Entity generator.domain.MeasurementTaskResult
*/
@Mapper
public interface MeasurementTaskResultMapper extends BaseMapper<MeasurementTaskResult> {
    List<MeasurementTaskResultDTO> findListWithoutResultByMeasurementId(@Param("measurementId")String measurementId);
}




