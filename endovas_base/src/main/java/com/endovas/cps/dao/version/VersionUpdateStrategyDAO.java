package com.endovas.cps.dao.version;

import com.endovas.cps.entity.version.VersionUpdateStrategy;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:13
 */
@Repository
public interface VersionUpdateStrategyDAO extends MysqlBaseRepo<VersionUpdateStrategy> {
    List<VersionUpdateStrategy> findByVersionId(String versionId);

}
