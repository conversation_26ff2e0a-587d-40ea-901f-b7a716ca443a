package com.endovas.cps.dao.surgery;

import com.endovas.cps.entity.surgery.SurgeryType;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:10
 */
@Repository
public interface SurgeryTypeDAO extends MysqlBaseRepo<SurgeryType> {

    List<SurgeryType> findByParentIdIsNullOrderBySerialNumberAsc();
    List<SurgeryType> findByParentIdIsNotNullOrderBySerialNumberAsc();
    List<SurgeryType> findByParentIdIsNotNullAndActiveIsTrueOrderBySerialNumberAsc();
    List<SurgeryType> findByParentIdIsNullAndActiveIsTrueOrderBySerialNumberAsc();
    List<SurgeryType> findByParentIdAndActiveIsTrue(String parentId);


}
