package com.endovas.cps.dao.hospital;

import com.endovas.cps.entity.hospital.CathRoomTrolley;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@Repository
public interface CathRoomTrolleyDAO extends MysqlBaseRepo<CathRoomTrolley> {
    CathRoomTrolley getByCathRoomId(String cathRoomId);

    CathRoomTrolley getByTrolleyIdAndHospitalId(String trolleyId,String hospitalId);
    CathRoomTrolley getByTrolleyIdAndCathRoomId(String trolleyId, String cathRoomId);

    List<CathRoomTrolley> findByCathRoomIdNotInAndHospitalId(List<String> cathRoomIds,String hospitalId);
}
