package com.endovas.cps.dao.agent;

import com.endovas.cps.entity.agent.MedAgent;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@SoftDelete
@Repository
public interface MedAgentDAO extends MysqlBaseRepo<MedAgent> {

    List<MedAgent> findByNameContaining(String name);
    MedAgent getByName(String name);

}
