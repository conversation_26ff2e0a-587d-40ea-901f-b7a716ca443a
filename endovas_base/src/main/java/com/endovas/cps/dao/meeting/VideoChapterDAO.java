package com.endovas.cps.dao.meeting;

import com.endovas.cps.entity.meeting.VideoChapter;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2022/10/26
 * Time: 18:58
 */
@Repository
public interface VideoChapterDAO extends MysqlBaseRepo<VideoChapter> {
    List<VideoChapter> findByDeviceIdOrderByCreateTimeAsc(String deviceId);
}
