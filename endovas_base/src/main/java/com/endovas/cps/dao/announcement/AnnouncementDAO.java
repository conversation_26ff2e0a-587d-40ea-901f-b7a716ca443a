package com.endovas.cps.dao.announcement;

import com.endovas.cps.entity.announcement.Announcement;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:25
 */
@Repository
public interface AnnouncementDAO extends MysqlBaseRepo<Announcement> {

    Page<Announcement> findByOrderByCreateTimeDesc(Pageable pageable);
}
