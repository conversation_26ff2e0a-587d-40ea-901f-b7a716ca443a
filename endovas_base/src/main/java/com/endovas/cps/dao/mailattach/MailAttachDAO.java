package com.endovas.cps.dao.mailattach;

import com.endovas.cps.entity.mailattach.MailAttach;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

/**
 * @author: wk
 * @Date: 2025/2/14
 * @Time: 18:38
 */
@Repository
public interface MailAttachDAO extends MysqlBaseRepo<MailAttach> {
    long countByMailId(String mailId);
    MailAttach getByMailId(String mailId);
}
