package com.endovas.cps.dao.platform;

import com.endovas.cps.entity.platform.Equipment;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:26
 */
@Repository
public interface EquipmentDAO extends MysqlBaseRepo<Equipment> {
    Equipment findFirstByStatusAndSoftNameAndDelIsFalse(String status,String softName);

    Equipment findFirstByStatusAndSoftNameAndDelIsFalseOrderByUpdateTimeAsc(String status,String softName);
    long countByStatus(String status);

    @Query(value = "update t_equipment set status=?1", nativeQuery = true)
    @Modifying
    @Transactional
    int updateEquipStatus(String equipStatus);

    List<Equipment> findByHostnameAndProtocolAndStatus(String hostname,String protocol,String status);
}
