package com.endovas.cps.dao.patient;

import com.endovas.cps.entity.patient.PatientTrack;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@SoftDelete
@Repository
public interface PatientTrackDAO extends MysqlBaseRepo<PatientTrack> {
    List<PatientTrack> findByPatientId(String patientId);

    Long countByTrackMethod(String trackMethod);
    Long countByPatientId(String patientId);
}
