package com.endovas.cps.dao.hospital;

import com.endovas.cps.entity.hospital.Hospital;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@SoftDelete
@Repository
public interface HospitalDAO extends MysqlBaseRepo<Hospital> {
    List<Hospital> findAllByDelIsFalseOrderByCreateTimeDesc();
    List<Hospital> findByNameContaining(String name);
    List<Hospital> findByIdIn(List<String> hospitalIds);
    Hospital getByName(String name);
}
