package com.endovas.cps.dao.version;

import com.endovas.cps.entity.version.Version;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:10
 */
@Repository
public interface VersionDAO extends MysqlBaseRepo<Version> {


    Version findFirstByTerminalTypeAndOsAndApplyTimeLessThanAndAvailableIsTrueOrderByApplyTimeDesc(String terminalType, String os, LocalDateTime applyTime);

    List<Version> findFirst10ByTerminalTypeAndOsAndAvailableIsTrueOrderByCreateTimeDesc(String terminalType, String os);

    Version getByTerminalTypeAndOsAndSoftVersionAndAvailableIsTrue(String terminalType, String os, String softVersion);
}
