package com.endovas.cps.dao.trolley;

import com.endovas.cps.entity.trolley.TrolleyTransfer;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:26
 */
@Repository
public interface TrolleyTransferDAO extends MysqlBaseRepo<TrolleyTransfer> {
    TrolleyTransfer getByTrolleyId(String trolleyId);
    TrolleyTransfer getByTrolleyIdAndTransferFromHospitalId(String trolleyId, String transferFromHospitalId);
    TrolleyTransfer getByTransferFromHospitalId(String transferFromHospitalId);
    TrolleyTransfer getByTransferToHospitalId(String transferToHospitalId);
}
