package com.endovas.cps.dao.mailattach;

import com.endovas.cps.entity.mailattach.MailAttachLink;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MailAttachLinkDAO extends MysqlBaseRepo<MailAttachLink> {
    List<MailAttachLink> findByStatus(Integer status);
}