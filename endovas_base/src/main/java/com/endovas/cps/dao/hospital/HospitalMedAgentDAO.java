package com.endovas.cps.dao.hospital;

import com.endovas.cps.entity.hospital.HospitalMedAgent;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@SoftDelete
@Repository
public interface HospitalMedAgentDAO extends MysqlBaseRepo<HospitalMedAgent> {

    HospitalMedAgent getByHospitalId(String hospitalId);

    List<HospitalMedAgent> findByMedAgentId(String medAgentId);

    List<HospitalMedAgent> findByMedAgentIdIn(List<String> medAgentIds);
}
