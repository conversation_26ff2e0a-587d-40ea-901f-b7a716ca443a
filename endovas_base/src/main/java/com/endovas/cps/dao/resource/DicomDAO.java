package com.endovas.cps.dao.resource;

import com.endovas.cps.entity.resource.Dicom;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:26
 */
@Repository
public interface DicomDAO extends MysqlBaseRepo<Dicom> {
    List<Dicom> findBySurgeryTypeIdIn(List<String> surgeryTypeIds);

    List<Dicom> findBySurgeryStage(String surgeryStage);

    Long countByPatientIdAndSurgeryStage(String patientId, String surgeryStage);
    Long countByCreatorIdAndPatientIdIsNull(String creatorId);
    Long countByCreatorIdAndCreateTimeBetween(String creatorId, LocalDateTime startTime,LocalDateTime endTime);
    Long countByCreatorIdAndSurgeryTypeIdInAndCreateTimeBetween(String creatorId, List<String> surgeryTypeIds, LocalDateTime startTime,LocalDateTime endTime);

    List<Dicom> findByNameContainingAndPatientIdIsNotNull(String name);
    List<Dicom> findByRemarkContainingAndPatientIdIsNotNull(String name);

    List<Dicom> findByCreatorIdAndStatusIn(String creatorId,List<String> status);
    Long countBySurgeryTypeIdInAndCreatorId(List<String> surgeryTypeIds,String creatorId);
    Long countBySurgeryTypeIdIn(List<String> surgeryTypeIds);
    Long countBySurgeryTypeIdInAndCreateTimeBetween(List<String> surgeryTypeIds,LocalDateTime startTime,LocalDateTime endTime);
}
