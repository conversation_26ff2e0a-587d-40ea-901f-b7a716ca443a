package com.endovas.cps.dao.measurement;

import com.endovas.cps.entity.measurement.MeasurementOperationRecord;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:30
 */
@Repository
public interface MeasurementOperationDAO extends MysqlBaseRepo<MeasurementOperationRecord> {
    // 已完成的业务列表
    List<MeasurementOperationRecord> findByMeasurementIdAndIsFinishedIsTrueOrderByCreateTimeDesc(String measurementId);

    // 查询最新的业务记录
    MeasurementOperationRecord findFirstByMeasurementIdOrderByCreateTimeDesc(String measurementId);

    // 根据设备id查询一个未完成的记录   找到机器当前被使用的操作记录
    MeasurementOperationRecord findFirstByEquipmentIdAndIsUsingEquipIsTrueOrderByCreateTimeDesc(String equipmentId);

    @Query(value="update t_measurement_operation_record set is_using_equip=?1", nativeQuery = true)
    @Modifying
    @Transactional
    int updateIsUsingEquip(Boolean isUsingEquip);
}
