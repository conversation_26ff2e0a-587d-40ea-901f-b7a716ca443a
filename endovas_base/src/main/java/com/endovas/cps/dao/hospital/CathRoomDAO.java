package com.endovas.cps.dao.hospital;

import com.endovas.cps.entity.hospital.CathRoom;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@Repository
public interface CathRoomDAO extends MysqlBaseRepo<CathRoom> {
    List<CathRoom> findByHospitalId(String hospitalId);

    List<CathRoom> findByIdIn(List<String> ids);
}
