package com.endovas.cps.dao.patient;

import com.endovas.cps.entity.patient.Patient;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import io.daige.starter.component.jpa.annotation.SoftDelete;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:24
 */
@SoftDelete
@Repository
public interface PatientDAO extends MysqlBaseRepo<Patient> {
    List<Patient> findByNameContaining(String name);
    List<Patient> findByIdIn(List<String> ids);

    List<Patient> findBySurgeryTypeIdIn(List<String> surgeryTypeId);

    Long countByIdInAndAgeBetween(List<String> ids,Integer start, Integer end);

    List<Patient> findByCreatorId(String creatorId);
}
