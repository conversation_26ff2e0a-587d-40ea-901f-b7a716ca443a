package com.endovas.cps.dao.attachment;

import com.endovas.cps.entity.attachment.AttachmentTemp;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午2:02
 */
@Repository
public interface AttachmentTempDAO extends MysqlBaseRepo<AttachmentTemp> {
    AttachmentTemp getAttachmentTempById(String id);
    AttachmentTemp getAttachmentTempByTmpUrl(String tmpUrl);
    List<AttachmentTemp> findByIdIn(List<String> id);

}
