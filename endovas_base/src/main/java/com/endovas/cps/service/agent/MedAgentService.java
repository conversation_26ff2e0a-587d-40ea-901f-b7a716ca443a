package com.endovas.cps.service.agent;

import com.endovas.cps.pojo.fo.agent.MedAgentAddFO;
import com.endovas.cps.pojo.fo.agent.MedAgentEditFO;
import com.endovas.cps.pojo.fo.agent.MedAgentSearchFO;
import com.endovas.cps.pojo.vo.EnterpriseSelectVO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentListVO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentSelectVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 17:18
 */
public interface MedAgentService {
    PageVO<MedAgentListVO> list(MedAgentSearchFO searchFO, PageFO pageFO);

    List<MedAgentSelectVO> select();

    void add(MedAgentAddFO input);

    void edit(MedAgentEditFO input);

    void del(String id);

    List<EnterpriseSelectVO> select(String name);

}
