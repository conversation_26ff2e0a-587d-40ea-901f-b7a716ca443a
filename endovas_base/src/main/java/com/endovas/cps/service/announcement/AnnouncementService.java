package com.endovas.cps.service.announcement;

import com.endovas.cps.pojo.fo.announcement.AnnouncementAddFO;
import com.endovas.cps.pojo.vo.AnnouncementVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:27
 */
public interface AnnouncementService {
    void add(AnnouncementAddFO input);
    AnnouncementVO detail(String id);

    PageVO<AnnouncementVO> list(PageFO pageFO);
}
