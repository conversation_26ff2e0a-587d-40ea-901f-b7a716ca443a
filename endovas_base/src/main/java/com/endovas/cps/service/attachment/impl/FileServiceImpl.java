package com.endovas.cps.service.attachment.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.config.properties.AliStsProperties;
import com.endovas.cps.dao.attachment.AttachmentDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.CompressExtEnum;
import com.endovas.cps.enums.DicomStatusEnum;
import com.endovas.cps.enums.WebSocketMsgTypeEnum;
import com.endovas.cps.pojo.dto.ali.PolicyDTO;
import com.endovas.cps.pojo.dto.ws.msg.DicomMsg;
import com.endovas.cps.pojo.vo.AliStsTokenVO;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.resource.DicomParseService;
import com.endovas.cps.service.resource.DicomService;
import com.endovas.cps.service.wsmsg.WebSocketMsgService;
import com.endovas.cps.util.FileExtractUtils;
import io.daige.starter.common.render.ToClientMsg;
import io.daige.starter.common.render.WebSocketMsgRender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 11:39
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {
    private final AliOSSProperties aliOSSProperties;
    private final AliStsProperties aliStsProperties;
    private final WebSocketMsgService webSocketMsgService;

    private final DefaultAcsClient acsClient;
    private final OSSClient ossClient;

    private final AttachmentDAO attachmentDAO;


    private final DicomDAO dicomDAO;
    private final DicomService dicomService;
    private final DicomParseService dicomParseService;

    @Override
    public Boolean dicomCanView(String dicomId) {
        Attachment attachment = attachmentDAO.getByTargetIdAndType(dicomId, AttachmentTypeEnum.DICOM.getCode());
        Optional<Dicom> dicomOpt = dicomDAO.findById(dicomId);
        if (dicomOpt.isPresent()) {
            Dicom dicom = dicomOpt.get();
            if (DicomStatusEnum.TEMPFILE_READY.getCode().equals(dicom.getTempFileStatus())) {
                return true;
            }
            if (DicomStatusEnum.TEMPFILE_CLEANEDUP.getCode().equals(dicom.getTempFileStatus())
                    || DicomStatusEnum.TEMPFILE_FAIL.getCode().equals(dicom.getTempFileStatus())
                    || StrUtil.isEmpty(dicom.getTempFileStatus())
            ) {
                // 如果不存在或者处理失败时，更新缓存处理状态
                dicom.setTempFileStatus(DicomStatusEnum.TEMPFILE_DOWNLOADING.getCode());
                dicomDAO.save(dicom);

                //解压并解析dicom文件
                final String finalOssFilePath = attachment.getUrl();
                ThreadUtil.execAsync(() -> {
                    try {
                        process(finalOssFilePath, attachment);
                    } catch (IOException | ExecutionException | InterruptedException ignored) {
                    }
                });
                return false;
            }
        }
        return false;
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void aliOssUploaded(String ossFilePath) throws IOException, ExecutionException, InterruptedException {
        Attachment attachment = attachmentDAO.getByUrl(ossFilePath);
        process(ossFilePath, attachment);
    }

    private void process(String ossFilePath, Attachment attachment) throws IOException, ExecutionException, InterruptedException {
        // 更新dicom状态为处理中
        dicomService.updateStatusToProcess(attachment.getTargetId());
        ToClientMsg processMsg = WebSocketMsgRender.toClient(WebSocketMsgTypeEnum.DICOM_STATUS.getCode(), new DicomMsg(attachment.getTargetId(), DicomStatusEnum.PROCESSING.getCode()));
        webSocketMsgService.sendMessage(attachment.getCreatorId(), processMsg);

        String downloadTmpDir = genLocalTmpDownLoadFilePath(aliOSSProperties.getTmpDir(), attachment.getId(), attachment.getType());
        String downloadPath = downloadTmpDir + attachment.getName();

        log.info("-------------下载文件 开始-------------:{}", attachment.getTargetId());
        // 下载文件
        ObjectMetadata objectMetadata = ossClient.getObject(new GetObjectRequest(aliOSSProperties.getBucketName(), ossFilePath), new File(downloadPath));
        // 更新dicom临时文件状态为准备完成
        dicomService.updateTempFileStatusToFinish(attachment.getTargetId());

        log.info("-------------下载文件 结束-------------:{}", attachment.getTargetId());

        // 解压文件
        String fileExtractPath = genLocalTmpExtractPath(downloadTmpDir);

        log.info("-------------解压文件 开始-------------:{}", attachment.getTargetId());
        if (CompressExtEnum.ZIP.getCode().equalsIgnoreCase(FileUtil.extName(attachment.getName()))) {
            FileExtractUtils.unzipFile(downloadPath, fileExtractPath, attachment.getType());
        }
        if (CompressExtEnum.RAR.getCode().equalsIgnoreCase(FileUtil.extName(attachment.getName()))) {
            FileExtractUtils.unRarFileWith7z(downloadPath, fileExtractPath, attachment.getType());
        }
        log.info("-------------解压文件 结束-------------:{}", attachment.getTargetId());
        Dicom dicom = dicomParseService.process(objectMetadata.getContentLength(), fileExtractPath, attachment);
        Boolean hasDicom = false;
        if (Objects.nonNull(dicom)) {
            hasDicom = dicom.getHasDicomFile();
        }
        ToClientMsg finishMsg = WebSocketMsgRender.toClient(WebSocketMsgTypeEnum.DICOM_STATUS.getCode(), new DicomMsg(attachment.getTargetId(), DicomStatusEnum.FINISH.getCode(), hasDicom));
        webSocketMsgService.sendMessage(attachment.getCreatorId(), finishMsg);
    }


    @Override
    public void processMailAttach(String ossFilePath) throws IOException, ExecutionException, InterruptedException {

        Attachment attachment = attachmentDAO.getByUrl(ossFilePath);

        // 更新dicom状态为处理中
        dicomService.updateStatusToProcess(attachment.getTargetId());
        ToClientMsg processMsg = WebSocketMsgRender.toClient(WebSocketMsgTypeEnum.DICOM_STATUS.getCode(), new DicomMsg(attachment.getTargetId(), DicomStatusEnum.PROCESSING.getCode()));
        webSocketMsgService.sendMessage(attachment.getCreatorId(), processMsg);

        String downloadTmpDir = genLocalTmpDownLoadFilePath(aliOSSProperties.getTmpDir(), attachment.getId(), attachment.getType());
        String downloadPath = downloadTmpDir + attachment.getName();

        log.info("-------------读取文件 开始-------------:{}", attachment.getTargetId());
        File file = new File(downloadPath);
        // 下载文件 文件已经存在
        dicomService.updateTempFileStatusToFinish(attachment.getTargetId());
        log.info("-------------读取文件 结束-------------:{}", attachment.getTargetId());

        // 解压文件
        String fileExtractPath = genLocalTmpExtractPath(downloadTmpDir);

        log.info("-------------解压文件 开始-------------:{}", attachment.getTargetId());
        if (CompressExtEnum.ZIP.getCode().equalsIgnoreCase(FileUtil.extName(attachment.getName()))) {
            FileExtractUtils.unzipFile(downloadPath, fileExtractPath, attachment.getType());
        } else if (CompressExtEnum.RAR.getCode().equalsIgnoreCase(FileUtil.extName(attachment.getName()))) {
            FileExtractUtils.unRarFileWith7z(downloadPath, fileExtractPath, attachment.getType());
        } else {
            log.info("-------------解压文件失败，文件不是zip或者rar-------------:{}", attachment.getTargetId());
            return;
        }
        log.info("-------------解压文件 结束-------------:{}", attachment.getTargetId());
        Dicom dicom = dicomParseService.process(file.length(), fileExtractPath, attachment);
        Boolean hasDicom = false;
        if (Objects.nonNull(dicom)) {
            hasDicom = dicom.getHasDicomFile();
        }
        ToClientMsg finishMsg = WebSocketMsgRender.toClient(WebSocketMsgTypeEnum.DICOM_STATUS.getCode(), new DicomMsg(attachment.getTargetId(), DicomStatusEnum.FINISH.getCode(), hasDicom));
        webSocketMsgService.sendMessage(attachment.getCreatorId(), finishMsg);

        // 上传文件至oss
        ossClient.putObject(aliOSSProperties.getBucketName(), ossFilePath, file);

        // 删除临时文件
        FileUtil.del(downloadPath); // 删除临时文件
    }


    @Override
    // /tmp/dicom/231s212121/
    public String genLocalTmpDownLoadFilePath(String temDir, String fileId, String type) {
        StringBuilder rootTmpPathStr = new StringBuilder(temDir);
        rootTmpPathStr.append(type);
        rootTmpPathStr.append(StrUtil.SLASH);
        rootTmpPathStr.append(fileId);
        rootTmpPathStr.append(StrUtil.SLASH);
        String rootTmpPath = rootTmpPathStr.toString();
        // 判断目录是否存在，不存在则创建目录
        File savePath = new File(rootTmpPath);
        FileUtil.mkdir(savePath);
        return savePath.getAbsolutePath() + StrUtil.SLASH;
    }

    // /tmp/dicom/231s212121/extract/
    private String genLocalTmpExtractPath(String tmpPath) {
        StringBuilder builder = new StringBuilder(tmpPath);
        builder.append("extract");
        builder.append(File.separator);
        return builder.toString();
    }


    @Override
    public AliStsTokenVO getAliStsToken() throws ClientException {

        StringBuilder policyStr = new StringBuilder(aliOSSProperties.getBucketName());
        policyStr.append(StrUtil.SLASH);
        policyStr.append("*");
        PolicyDTO policyDTO = PolicyDTO.createPolicyDTO(aliOSSProperties.getBucketName(), policyStr.toString());


        final AssumeRoleRequest request = new AssumeRoleRequest();
        request.setMethod(MethodType.POST);
        request.setRoleArn(aliStsProperties.getRoleArn());
        request.setRoleSessionName(aliStsProperties.getRoleSessionName());
        request.setPolicy(JSONUtil.toJsonStr(policyDTO));
        request.setDurationSeconds(3600L);

        final AssumeRoleResponse response = acsClient.getAcsResponse(request);
        AssumeRoleResponse.Credentials credentials = response.getCredentials();
        AliStsTokenVO tokenVO = new AliStsTokenVO();
        tokenVO.setAccessKeyId(credentials.getAccessKeyId());
        tokenVO.setAccessKeySecret(credentials.getAccessKeySecret());
        tokenVO.setSecurityToken(credentials.getSecurityToken());
        tokenVO.setBucket(aliOSSProperties.getBucketName());
        return tokenVO;
    }
}
