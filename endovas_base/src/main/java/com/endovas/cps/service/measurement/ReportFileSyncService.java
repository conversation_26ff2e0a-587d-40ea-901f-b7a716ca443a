package com.endovas.cps.service.measurement;

import com.endovas.cps.pojo.fo.measurement.filesync.QueryReportFileExistFO;
import com.endovas.cps.pojo.vo.measurement.filesync.QueryReportFileExistVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/26
 * @Time: 15:39
 */
public interface ReportFileSyncService {

    List<String> getEquipUserNameList(String hostname,String protocol);
    QueryReportFileExistVO reportFileExist(QueryReportFileExistFO param);
    void uploadReportFile(MultipartFile file, String measurementTaskId, String fileHash);
}
