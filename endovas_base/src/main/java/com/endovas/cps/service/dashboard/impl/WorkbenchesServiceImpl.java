package com.endovas.cps.service.dashboard.impl;

import cn.hutool.core.date.DateUtil;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientTrackDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.patient.Patient;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.surgery.SurgeryType;
import com.endovas.cps.enums.DicomStatusEnum;
import com.endovas.cps.enums.MeasurementStatusEnum;
import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.dto.dashboard.DicomMonthStats;
import com.endovas.cps.pojo.vo.dashboard.DiseaseTypeVO;
import com.endovas.cps.pojo.vo.dashboard.MedImageMonthStatVO;
import com.endovas.cps.pojo.vo.dashboard.TodoListStatVO;
import com.endovas.cps.pojo.vo.dashboard.WorkbenchStat;
import com.endovas.cps.service.dashboard.WorkbenchesService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.google.common.collect.Lists;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/12/30
 * @Time: 14:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkbenchesServiceImpl implements WorkbenchesService {

    private final DicomDAO dicomDAO;
    private final PatientDAO patientDAO;
    private final PatientTrackDAO patientTrackDAO;
    private final MeasurementTaskDAO measurementTaskDAO;
    private final SurgeryTypeDAO surgeryTypeDAO;
    private final SurgeryTypeService surgeryTypeService;

    private final String THORACIC_AORTA = "胸主动脉疾病";
    private final String ABDOMINAL_AORTA = "腹主动脉疾病";

    @Override
    public WorkbenchStat stat(LoginUser loginUser) {

        // 术前影像上传数量
        WorkbenchStat result = new WorkbenchStat();
        List<Dicom> dicoms = dicomDAO.findByCreatorIdAndStatusIn(loginUser.getId(), Lists.newArrayList(DicomStatusEnum.UPLOADING.getCode(), DicomStatusEnum.FINISH.getCode(), DicomStatusEnum.PROCESSING.getCode()));
        List<Dicom> preopDicoms = dicoms.stream().filter(dicom -> dicom.getSurgeryStage().equals(SurgeryStageEnum.PREOP.getCode())).collect(Collectors.toList());
        result.setDicomAmt(Long.valueOf(preopDicoms.size()));
        // 影像总大小
        Long totalSize = preopDicoms.stream().map(Dicom::getFileSize).filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .sum();
        result.setDicomTotalSize(totalSize);

        // 测量影像任务数
        Long measurementCompletedAmt = measurementTaskDAO.countByMeasurerIdAndTaskStatus(loginUser.getId(), MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode());
        result.setMeasurementCompletedAmt(measurementCompletedAmt);

        // 创建患者档案数量
        List<Patient> patients = patientDAO.findByCreatorId(loginUser.getId());
        result.setPatientAmt(Long.valueOf(patients.size()));

        // 疾病类型占比
        List<SurgeryType> surgeryTypes = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc();
        List<DiseaseTypeVO> diseaseTypeVO = Lists.newArrayList();
        for (SurgeryType surgeryType : surgeryTypes) {
            DiseaseTypeVO one = new DiseaseTypeVO();
            one.setName(surgeryType.getName());
            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
            one.setAmt(dicomDAO.countBySurgeryTypeIdInAndCreatorId(allSurgeryTypeIds, loginUser.getId()));
            diseaseTypeVO.add(one);
        }
        result.setDiseaseTypeRatio(diseaseTypeVO);

        return result;
    }

    @Override
    public MedImageMonthStatVO medImageMonthStat(String year, LoginUser loginUser) {

        // 查询出所有的疾病类型
        List<String> thoracicAortaTypeIds = null;
        List<String> abdominalAortaTypeIds = null;
        List<SurgeryType> surgeryTypes = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc();
        for (SurgeryType surgeryType : surgeryTypes) {
            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
            if (surgeryType.getName().equals(THORACIC_AORTA)) {
                thoracicAortaTypeIds = allSurgeryTypeIds;
            } else {
                abdominalAortaTypeIds = allSurgeryTypeIds;
            }
        }

        // 根据月份统计疾病对应的影像数量
        List<DicomMonthStats> dicomMonthStatsList = Lists.newLinkedList();
        for (int i = 1; i < 13; i++) {
            LocalDateTime firstDayOfMonth = DateUtil.beginOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            LocalDateTime lastDayOfMonth = DateUtil.endOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();

            DicomMonthStats dicomMonthStats = new DicomMonthStats();
            dicomMonthStats.setThoracicAortaAmt(dicomDAO.countByCreatorIdAndSurgeryTypeIdInAndCreateTimeBetween(loginUser.getId(), thoracicAortaTypeIds, firstDayOfMonth, lastDayOfMonth));
            dicomMonthStats.setAbdominalAortaAmt(dicomDAO.countByCreatorIdAndSurgeryTypeIdInAndCreateTimeBetween(loginUser.getId(), abdominalAortaTypeIds, firstDayOfMonth, lastDayOfMonth));
            dicomMonthStats.setMonth(new Long(i));
            dicomMonthStatsList.add(dicomMonthStats);
        }

        MedImageMonthStatVO result = new MedImageMonthStatVO();
        result.setMonths(dicomMonthStatsList);

        return result;
    }

    @Override
    public TodoListStatVO todoListStat(LoginUser loginUser) {

        TodoListStatVO result = new TodoListStatVO();

        // 未完结的测量任务
        Long unCompletedMeasurementAmt = measurementTaskDAO.countByMeasurerIdAndTaskStatus(loginUser.getId(), MeasurementStatusEnum.TASKSTATUS_ALREADYCLAIMED.getCode());
        result.setUnCompletedMeasurementAmt(unCompletedMeasurementAmt);

        // 可领取的测量任务
        Long unclaimedMeasurementAmt = measurementTaskDAO.countByTaskStatus(MeasurementStatusEnum.TASKSTATUS_UNCLAIMED.getCode());
        result.setUnclaimedMeasurementAmt(unclaimedMeasurementAmt);

        // 未创建患者档案
        Long unCreatedPatientAmt = dicomDAO.countByCreatorIdAndPatientIdIsNull(loginUser.getId());
        result.setUnCreatedPatientAmt(unCreatedPatientAmt);

        // 未上传术后影像
        List<Patient> patients = patientDAO.findByCreatorId(loginUser.getId());
        Long unUploadedPostOpDicomAmt = 0l;
        for (Patient patient : patients) {
            Long postopImgCount = dicomDAO.countByPatientIdAndSurgeryStage(patient.getId(), SurgeryStageEnum.POSTOP.getCode());
            if (postopImgCount.intValue() <= 0) {
                unUploadedPostOpDicomAmt += 1;
            }
        }
        result.setUnUploadedPostOpDicomAmt(unUploadedPostOpDicomAmt);

        // 未随访患者病情
        Long unCreatedPatientTrackAmt = 0l;
        for (Patient patient : patients) {
            Long trackCount = patientTrackDAO.countByPatientId(patient.getId());
            if (trackCount.intValue() <= 0) {
                unCreatedPatientTrackAmt += 1;
            }
        }
        result.setUnCreatedPatientTrackAmt(unCreatedPatientTrackAmt);

        return result;
    }

}
