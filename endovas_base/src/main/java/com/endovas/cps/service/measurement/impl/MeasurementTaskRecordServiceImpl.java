package com.endovas.cps.service.measurement.impl;

import com.endovas.cps.dao.measurement.MeasurementTaskRecordDAO;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.measurement.MeasurementTaskRecord;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskRecordSearchFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskRecordListVO;
import com.endovas.cps.service.measurement.MeasurementTaskRecordService;
import com.endovas.cps.service.user.CreatorTypeService;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeasurementTaskRecordServiceImpl implements MeasurementTaskRecordService {

    private final MeasurementTaskRecordDAO measurementTaskRecordDAO;
    private final CreatorTypeService creatorTypeService;

    @Override
    public List<MeasurementTaskRecordListVO> list(MeasurementTaskRecordSearchFO searchFO, LoginUser loginUser) {
        List<MeasurementTaskRecord> poList = measurementTaskRecordDAO.findByMeasurementIdOrderByCreateTimeDesc(searchFO.getMeasurementTaskId());

        List<MeasurementTaskRecordListVO> resultList = poList.stream().map(po -> {
            MeasurementTaskRecordListVO vo = new MeasurementTaskRecordListVO();
            vo.convertFrom(po);

            // 发起人
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(po.getOperaterBelong()), po.getOperaterId());
            vo.setOperater(creatorTypeDTO.getCreator());
            vo.setOperaterType(creatorTypeDTO.getCreatorType());

            return vo;
        }).collect(Collectors.toList());

        return resultList;
    }

    @Override
    public void add(MeasurementTask beforePO, MeasurementTask afterPO, String opContent, String opUserId, String opUserBelong) {
        MeasurementTaskRecord po = new MeasurementTaskRecord();
        po.setMeasurementId(afterPO.getId());

        if (Objects.nonNull(beforePO)) {
            po.setBeforeTaskStatus(beforePO.getTaskStatus());
            po.setBeforeResultSyncStatus(beforePO.getResultSyncStatus());
            po.setBeforeEnvPrepareStatus(beforePO.getEnvPrepareStatus());
        }

        po.setAfterTaskStatus(afterPO.getTaskStatus());
        po.setAfterResultSyncStatus(afterPO.getResultSyncStatus());
        po.setAfterEnvPrepareStatus(afterPO.getEnvPrepareStatus());

        po.setContent(opContent);
        po.setOperaterId(opUserId);
        po.setOperaterBelong(opUserBelong);

        measurementTaskRecordDAO.save(po);
    }
}
