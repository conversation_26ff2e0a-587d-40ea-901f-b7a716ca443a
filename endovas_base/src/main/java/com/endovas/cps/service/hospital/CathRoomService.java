package com.endovas.cps.service.hospital;


import com.endovas.cps.pojo.fo.hospital.CathRoomAddFO;
import com.endovas.cps.pojo.fo.hospital.CathRoomEditFO;
import com.endovas.cps.pojo.vo.platform.hospital.CathRoomListVO;
import com.endovas.cps.pojo.vo.platform.hospital.CathRoomSelectVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/8/14
 * Time: 14:17
 */
public interface CathRoomService {
    List<CathRoomSelectVO> canBindSelect(String hospitalId);
    void add(CathRoomAddFO input);
    void edit(Cath<PERSON>oomEditFO input);
    void switchAvailable(String id);
    List<CathRoomListVO> list(String hospitalId);


}
