package com.endovas.cps.service.resource;

import com.endovas.cps.pojo.fo.resource.NormalFileAddFO;
import com.endovas.cps.pojo.fo.resource.NormalFileEditFO;
import com.endovas.cps.pojo.vo.resource.NormalFileListVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 13:57
 */
public interface NormalFileService {
    void add(NormalFileAddFO input, LoginUser loginUser);

    List<NormalFileListVO> list(String patientId, LoginUser loginUser);
    void edit(NormalFileEditFO param);

    void del(String id);
}
