package com.endovas.cps.service.mailattach.impl;

import com.endovas.cps.dao.mailattach.MailAttachLinkDAO;
import com.endovas.cps.entity.mailattach.MailAttachLink;
import com.endovas.cps.enums.MailAttachLinkStatusEnum;
import com.endovas.cps.pojo.dto.mailattach.MailAttachDownloadDTO;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.mailattach.MailAttachDownloadService;
import com.endovas.cps.service.mailattach.NeteaseMailService;
import com.endovas.cps.service.mailattach.QQMailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MailAttachDownloadServiceImpl implements MailAttachDownloadService {

    private final MailAttachLinkDAO mailAttachLinkDAO;
    private final QQMailService qqMailService;
    private final NeteaseMailService neteaseMailService;
    private final FileService fileService;

    // 注入自己，避免 this. 调用无效 @Async
    @Autowired
    @Lazy
    private MailAttachDownloadService self;

    @Override
    public void processDownloadLinks() {
        List<MailAttachLink> initLinks = mailAttachLinkDAO.findByStatus(MailAttachLinkStatusEnum.INIT.getCode());
        for (MailAttachLink initLink : initLinks) {
            log.info("开始处理附件链接: {}", initLink.getId());

            initLink.setStatus(MailAttachLinkStatusEnum.PROCESSING.getCode());
            mailAttachLinkDAO.save(initLink);

            // 提取字段，避免在异步方法中访问数据库
            String id = initLink.getId();
            String mailType = initLink.getMailType();
            String downloadLink = initLink.getDownloadLink();
            String fileName = initLink.getFileName();
            String receiverAccount = initLink.getReceiverAccount();

            // 异步处理（用 self 调用）
            self.processLink(id, mailType, downloadLink, fileName, receiverAccount);
        }
    }

    @Async("mailAttachExecutor")
    @Override
    public void processLink(String id, String mailType, String downloadLink, String fileName, String receiverAccount) {
        MailAttachDownloadDTO downloadDTO = null;
        try {
            if ("QQ".equals(mailType)) {
                downloadDTO = qqMailService.downloadAndProcess(downloadLink, fileName, receiverAccount);
            } else if (mailType != null && mailType.startsWith("NETEASE")) {
                downloadDTO = neteaseMailService.downloadAndProcess(downloadLink, fileName, receiverAccount);
            }
        } catch (Exception e) {
            log.error("下载文件报错: {}", id, e);
        }


        MailAttachLink link = mailAttachLinkDAO.findById(id).orElse(null);
        if (link == null) {
            log.warn("未找到附件链接: {}", id);
            return;
        }
        try {
            if (downloadDTO != null) {
                link.setStatus(MailAttachLinkStatusEnum.SUCCESS.getCode());
                link.setOssFilePath(downloadDTO.getOssFilePath());
                fileService.processMailAttach(downloadDTO.getOssFilePath());
                log.info("附件链接处理成功: {}", id);
            } else {
                link.setStatus(MailAttachLinkStatusEnum.FAILED.getCode());
                link.setErrorMsg("下载或处理失败");
                log.warn("附件链接处理失败: {}", id);
            }

        } catch (Exception e) {
            log.error("处理附件链接异常: {}", id, e);
            link.setStatus(MailAttachLinkStatusEnum.FAILED.getCode());
            link.setErrorMsg(e.getMessage());
        } finally {
            mailAttachLinkDAO.save(link);
        }
    }
}
