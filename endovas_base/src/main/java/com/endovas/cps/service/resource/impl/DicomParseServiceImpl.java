package com.endovas.cps.service.resource.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.DicomStatusEnum;
import com.endovas.cps.pojo.dto.AttachmentLocalFileDTO;
import com.endovas.cps.pojo.dto.dicom.DicomDTO;
import com.endovas.cps.pojo.dto.dicom.InstancesDTO;
import com.endovas.cps.pojo.dto.dicom.MetadataADTO;
import com.endovas.cps.pojo.dto.dicom.MetadataBDTO;
import com.endovas.cps.pojo.dto.dicom.MetadataBase;
import com.endovas.cps.pojo.dto.dicom.SeriesDTO;
import com.endovas.cps.pojo.dto.dicom.StudiesDTO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.resource.DicomParseService;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.io.DicomInputStream;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.SimpleFileVisitor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 15:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DicomParseServiceImpl implements DicomParseService {
    private final DicomDAO dicomDAO;
    private final AttachmentService attachmentService;

    private final AliOSSProperties aliOSSProperties;
    private final ServerProperties serverProperties;

    public final String DICOM_FILE_EXTENSION = "dcm";


    @Override
    public Dicom process(Long zipFileSize, String dicomPath, Attachment attachment) throws ExecutionException, InterruptedException, IOException {
        log.info("-------------解析dicom文件 开始-------------:{}", attachment.getTargetId());
        List<File> dicomFiles = getSpecificFileList(dicomPath, DICOM_FILE_EXTENSION);
        if (CollectionUtils.isEmpty(dicomFiles)) {
            Optional<Dicom> dicomOptional = dicomDAO.findById(attachment.getTargetId());
            if (dicomOptional.isPresent()) {
                Dicom dicom = dicomOptional.get();
                dicom.setStatus(DicomStatusEnum.FINISH.getCode());
                dicom.setHasDicomFile(false);
                dicomDAO.save(dicom);
                return dicom;
            }
            return null;
        }

        // 读取所有文件  key:dcm文件名   value：dcm文件解析数据
        Map<String, Future<MetadataBase>> dataMap = Maps.newConcurrentMap();
        long fileSize = 0L;

        CompletionService<MetadataBase> cs = ThreadUtil.newCompletionService(ThreadUtil.newExecutor(10));
        for (File dicomFile : dicomFiles) {
            fileSize = fileSize + dicomFile.length();
            Future<MetadataBase> data = cs.submit(() -> readDicomFile(dicomFile));
            dataMap.put(dicomFile.getPath().replace(dicomPath, ""), data);
        }

        // 拼接instances
        List<InstancesDTO> instancesDTOS = new ArrayList<>();
        for (Map.Entry<String, Future<MetadataBase>> me : dataMap.entrySet()) {
            if (Objects.isNull(me.getValue().get())) {
                continue;
            }
            InstancesDTO d = new InstancesDTO();
            d.setMetadata(me.getValue().get());
            String url = "dicomweb:" + aliOSSProperties.getDomain() + serverProperties.getServlet().getContextPath() + StrUtil.SLASH + attachment.getType() + StrUtil.SLASH + attachment.getId() + "/extract/" + me.getKey();
            d.setUrl(url);
            instancesDTOS.add(d);
        }


        // 分组并过滤instances对象
        Map<String, List<InstancesDTO>> groupedSeries = instancesDTOS.stream()
                .filter(i -> (Objects.nonNull(i.getMetadata())
                        && Objects.nonNull(i.getMetadata().getModality())
                        && !i.getMetadata().getModality().contains("SR")))
                .collect(Collectors.groupingBy(i -> i.getMetadata().getSeriesInstanceUID()));

        // 组装成series列表
        Map<String, SeriesDTO> seriesDTOMap = new HashMap<>();
        for (Map.Entry<String, List<InstancesDTO>> gseries : groupedSeries.entrySet()) {
            List<InstancesDTO> instancesList = gseries.getValue();

            // 拼接seriesDTO
            SeriesDTO seriesDTO = new SeriesDTO();
            InstancesDTO instancesDTO = instancesList.get(0);
            seriesDTO.setSeriesInstanceUID(instancesDTO.getMetadata().getSeriesInstanceUID());
            seriesDTO.setSeriesDescription(instancesDTO.getMetadata().getSeriesDescription());
            seriesDTO.setSeriesNumber(instancesList.size());
            seriesDTO.setSeriesTime(instancesDTO.getMetadata().getSeriesTime());
            seriesDTO.setModality(instancesDTO.getMetadata().getModality());
            seriesDTO.setSliceThickness(instancesDTO.getMetadata().getSliceThickness());
            seriesDTO.setInstances(instancesList);
            seriesDTOMap.put(seriesDTO.getSeriesInstanceUID(), seriesDTO);
        }

        // 分组series对象
        List<SeriesDTO> seriesList = new ArrayList<>(seriesDTOMap.values());
        Map<String, List<SeriesDTO>> groupedStudies = seriesList.stream().collect(Collectors.groupingBy(s -> s.getInstances().get(0).getMetadata().getStudyInstanceUID()));

        // 组装成studio列表
        List<StudiesDTO> studiesDTOList = new ArrayList<>();
        for (Map.Entry<String, List<SeriesDTO>> gstudy : groupedStudies.entrySet()) {
            List<SeriesDTO> serList = gstudy.getValue();

            // 拼接StudiesDTO
            StudiesDTO studiesDTO = new StudiesDTO();
            studiesDTO.setStudyInstanceUID(serList.get(0).getInstances().get(0).getMetadata().getStudyInstanceUID());
            studiesDTO.setStudyDate(serList.get(0).getInstances().get(0).getMetadata().getStudyDate());
            studiesDTO.setStudyTime(serList.get(0).getInstances().get(0).getMetadata().getStudyTime());
            studiesDTO.setPatientName(serList.get(0).getInstances().get(0).getMetadata().getPatientName());
            studiesDTO.setPatientID(serList.get(0).getInstances().get(0).getMetadata().getPatientID());
            studiesDTO.setAccessionNumber(serList.get(0).getInstances().get(0).getMetadata().getAccessionNumber());
            studiesDTO.setPatientAge(serList.get(0).getInstances().get(0).getMetadata().getPatientAge());
            studiesDTO.setPatientSex(serList.get(0).getInstances().get(0).getMetadata().getPatientSex());
            studiesDTO.setSeries(serList);
            int instancesCount = serList.stream().mapToInt(s -> s.getInstances().size()).sum();
            studiesDTO.setNumInstances(instancesCount);
            studiesDTO.setModalities(serList.get(0).getInstances().get(0).getMetadata().getModality()); // FIXME wk 可能有SR报告 需要拼接成 CT/SR
            studiesDTOList.add(studiesDTO);
        }

        DicomDTO dicomDTO = new DicomDTO();
        dicomDTO.setStudies(studiesDTOList);

        Optional<Dicom> dicomOptional = dicomDAO.findById(attachment.getTargetId());
        if (dicomOptional.isPresent()) {
            Dicom dicom = dicomOptional.get();
            dicom.setFileSize(zipFileSize);
            if (!CollectionUtils.isEmpty(studiesDTOList)) {
                dicom.setHasDicomFile(true);
            }
            dicom.setStatus(DicomStatusEnum.FINISH.getCode());
            dicom.setTempFileStatus(DicomStatusEnum.TEMPFILE_READY.getCode());
            dicom.setPatientName(studiesDTOList.get(0).getPatientName());
            dicom.setPatientNo(studiesDTOList.get(0).getPatientID());
            dicom.setPatientGender(studiesDTOList.get(0).getPatientSex());
            dicom.setPatientAge(extractNumber(studiesDTOList.get(0).getPatientAge()));
            dicom.setManufacture(studiesDTOList.get(0).getSeries().get(0).getInstances().get(0).getMetadata().getManufacture());

            dicom.setModality(studiesDTOList.get(0).getModalities());
            dicom.setContentDate(studiesDTOList.get(0).getStudyDate());
            dicom.setRowsColumns(studiesDTOList.get(0).getSeries().get(0).getInstances().get(0).getMetadata().getRows()
                    + " & " + studiesDTOList.get(0).getSeries().get(0).getInstances().get(0).getMetadata().getColumns());
            dicom.setFileCount(new Long(studiesDTOList.get(0).getNumInstances()));

            String jsonFileFullPath = dicomPath + attachment.getId() + ".json";
            File jsonFile = new File(jsonFileFullPath);
            File existFile = FileUtil.touch(jsonFile);
            FileUtil.writeString(JSONUtil.toJsonStr(dicomDTO), existFile, Charset.defaultCharset());
            String url = aliOSSProperties.getDomain() + serverProperties.getServlet().getContextPath() + StrUtil.SLASH + jsonFileFullPath.replace(aliOSSProperties.getTmpDir(), "");
            dicom.setViewJsonUrl(url);

            dicomDAO.save(dicom);
            log.info("-------------解析dicom文件 结束-------------:{}", attachment.getTargetId());

            // 生成DICOM预览图
            try {
                generatePreviewImage(studiesDTOList, dicomPath, attachment);
                log.info("DICOM预览图生成成功:{}", attachment.getTargetId());
            } catch (Exception e) {
                log.error("生成DICOM预览图失败:{}", attachment.getTargetId(), e);
            }

            return dicom;
        }
        return null;
    }

    public static String extractNumber(String input) {
        String numberPart = ReUtil.get("[0-9]+", input, 0);
        return numberPart != null ? numberPart.replaceFirst("^0+", "") : "0";
    }

    /**
     * 获取目录下（包括子目录）所有的特定文件
     *
     * @param path
     * @param fileExtension
     * @return
     */
    private List<File> getSpecificFileList(String path, String fileExtension) {
        File baseDir = new File(path); // 替换为你的目录路径
        final String extension = fileExtension; // 指定文件扩展名
        final List<File> files = new ArrayList<>();

        try {
            // 遍历文件树
            Files.walkFileTree(baseDir.toPath(), new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path path, java.nio.file.attribute.BasicFileAttributes attrs) {
                    File file = path.toFile();
                    if (StrUtil.equalsIgnoreCase(extension, FileUtil.extName(file))) {
                        files.add(file);
                    }
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            log.error("获取特定文件列表异常：{}，{}", path, fileExtension);
            log.error("获取特定文件列表异常", e);
        }

        return files;
    }

    private Long doubleStringToLong(String data) {
        if (Objects.isNull(data) || !NumberUtil.isNumber(data)) {
            return 0L;
        }

        BigDecimal d = new BigDecimal(data).setScale(0, RoundingMode.DOWN);

        return d.longValue();
    }

    private MetadataBase readDicomFile(File dicomFile) {

        try {
            // 如果是文件夹，跳过
            if (dicomFile.isDirectory()) {
                return null;
            }

            MetadataBase metadataDTO = null;
            try (InputStream inputStream = FileUtil.getInputStream(dicomFile)) {
                try (DicomInputStream dis = new DicomInputStream(inputStream)) {
                    Attributes attr = dis.readDataset(-1, -1);
                    if (Objects.nonNull(attr.getValue(Tag.WindowCenter))) {
                        String data = new String((byte[]) attr.getValue(Tag.WindowCenter));
                        if (data.contains("\\")) {
                            metadataDTO = new MetadataBDTO();
                            if (Objects.isNull(attr.getStrings(Tag.WindowCenter))) {
                                ((MetadataBDTO) metadataDTO).setWindowCenter(new ArrayList<>());
                            } else {
                                List<Long> windowCenterList = Arrays.stream(attr.getStrings(Tag.WindowCenter)).map(w -> Long.parseLong(w)).collect(Collectors.toList());
                                ((MetadataBDTO) metadataDTO).setWindowCenter(windowCenterList);
                            }
                            if (Objects.isNull(attr.getStrings(Tag.WindowWidth))) {
                                ((MetadataBDTO) metadataDTO).setWindowWidth(new ArrayList<>());
                            } else {
                                List<Long> windowWidthList = Arrays.stream(attr.getStrings(Tag.WindowWidth)).map(w -> Long.parseLong(w)).collect(Collectors.toList());
                                ((MetadataBDTO) metadataDTO).setWindowWidth(windowWidthList);
                            }
                        } else {
                            metadataDTO = new MetadataADTO();
                            if (Objects.isNull(attr.getString(Tag.WindowCenter))) {
                                ((MetadataADTO) metadataDTO).setWindowCenter(0l);
                            } else {
                                ((MetadataADTO) metadataDTO).setWindowCenter(doubleStringToLong(attr.getString(Tag.WindowCenter)));
                            }
                            if (Objects.isNull(attr.getString(Tag.WindowWidth))) {
                                ((MetadataADTO) metadataDTO).setWindowWidth(0l);
                            } else {
                                ((MetadataADTO) metadataDTO).setWindowWidth(doubleStringToLong(attr.getString(Tag.WindowWidth)));
                            }
                        }
                    } else {
                        metadataDTO = new MetadataADTO();
                    }

                    if (Objects.nonNull(attr.getInt(Tag.Columns, 0))) {
                        metadataDTO.setColumns(attr.getInt(Tag.Columns, 0));
                    }
                    if (Objects.nonNull(attr.getInt(Tag.Rows, 0))) {
                        metadataDTO.setRows(attr.getInt(Tag.Rows, 0));
                    }
                    metadataDTO.setAcquisitionNumber(attr.getInt(Tag.AcquisitionNumber, 0));
                    metadataDTO.setInstanceNumber(attr.getInt(Tag.InstanceNumber, 0));
                    metadataDTO.setSOPClassUID(attr.getString(Tag.SOPClassUID));
                    metadataDTO.setPhotometricInterpretation(attr.getString(Tag.PhotometricInterpretation));
                    metadataDTO.setBitsAllocated(attr.getInt(Tag.BitsAllocated, 0));
                    metadataDTO.setBitsStored(attr.getInt(Tag.BitsStored, 0));
                    metadataDTO.setPixelRepresentation(attr.getInt(Tag.PixelRepresentation, 0));
                    metadataDTO.setSamplesPerPixel(attr.getInt(Tag.SamplesPerPixel, 0));
                    if (Objects.isNull(attr.getDoubles(Tag.PixelSpacing))) {
                        metadataDTO.setPixelSpacing(new ArrayList<>());
                    } else {
                        List<Double> d = Arrays.stream(attr.getDoubles(Tag.PixelSpacing)).boxed().collect(Collectors.toList());
                        metadataDTO.setPixelSpacing(d);
                    }
                    metadataDTO.setHighBit(attr.getInt(Tag.HighBit, 0));
                    if (Objects.isNull(attr.getDoubles(Tag.ImageOrientationPatient))) {
                        metadataDTO.setImageOrientationPatient(new ArrayList<>());
                    } else {
                        List<Double> i = Arrays.stream(attr.getDoubles(Tag.ImageOrientationPatient)).boxed().collect(Collectors.toList());
                        metadataDTO.setImageOrientationPatient(i);
                    }
                    if (Objects.isNull(attr.getDoubles(Tag.ImagePositionPatient))) {
                        metadataDTO.setImagePositionPatient(new ArrayList<>());
                    } else {
                        List<Double> d = Arrays.stream(attr.getDoubles(Tag.ImagePositionPatient)).boxed().collect(Collectors.toList());
                        metadataDTO.setImagePositionPatient(d);
                    }
                    metadataDTO.setFrameOfReferenceUID(attr.getString(Tag.FrameOfReferenceUID));
                    if (Objects.nonNull(attr.getStrings(Tag.ImageType))) {
                        metadataDTO.setImageType(Arrays.asList(attr.getStrings(Tag.ImageType)));
                    } else {
                        metadataDTO.setImageType(null);
                    }
                    metadataDTO.setManufacture(attr.getString(Tag.Manufacturer));
                    metadataDTO.setModality(attr.getString(Tag.Modality));
                    metadataDTO.setSOPInstanceUID(attr.getString(Tag.SOPInstanceUID));
                    metadataDTO.setSeriesInstanceUID(attr.getString(Tag.SeriesInstanceUID));
                    metadataDTO.setStudyInstanceUID(attr.getString(Tag.StudyInstanceUID));
                    metadataDTO.setRescaleIntercept(attr.getInt(Tag.RescaleIntercept, -1024));
                    metadataDTO.setRescaleSlope(attr.getInt(Tag.RescaleSlope, 1));
                    metadataDTO.setSeriesDate(attr.getString(Tag.SeriesDate));
                    metadataDTO.setSeriesTime(attr.getString(Tag.SeriesTime));
                    metadataDTO.setAcquisitionDate(attr.getString(Tag.AcquisitionDate));
                    metadataDTO.setAcquisitionTime(attr.getString(Tag.AcquisitionTime));

                    metadataDTO.setStudyDate(attr.getString(Tag.StudyDate));
                    metadataDTO.setStudyTime(attr.getString(Tag.StudyTime));
                    metadataDTO.setSliceThickness(attr.getString(Tag.SliceThickness));
                    metadataDTO.setPatientName(attr.getString(Tag.PatientName));
                    metadataDTO.setPatientID(attr.getString(Tag.PatientID));
                    metadataDTO.setPatientAge(attr.getString(Tag.PatientAge));
                    metadataDTO.setPatientSex(attr.getString(Tag.PatientSex));
                    metadataDTO.setAccessionNumber(attr.getString(Tag.AccessionNumber));
                    metadataDTO.setSeriesDescription(attr.getString(Tag.SeriesDescription));
                }
            }
            return metadataDTO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("解析dicom文件异常", e);
            log.info("解析dicom文件异常----->:{}", dicomFile.getPath());
        }
        return null;
    }

    /**
     * 生成DICOM预览图
     *
     * @param studiesDTOList 研究列表
     * @param dicomPath      DICOM文件路径
     * @param attachment     附件信息
     */
    private void generatePreviewImage(List<StudiesDTO> studiesDTOList, String dicomPath, Attachment attachment) {
        if (CollectionUtils.isEmpty(studiesDTOList)) {
            log.warn("没有找到有效的DICOM研究数据，无法生成预览图:{}", attachment.getTargetId());
            return;
        }

        // 选择第一个非SR的series的第一个instance
        InstancesDTO selectedInstance = null;
        for (StudiesDTO study : studiesDTOList) {
            if (!CollectionUtils.isEmpty(study.getSeries())) {
                for (SeriesDTO series : study.getSeries()) {
                    if (!CollectionUtils.isEmpty(series.getInstances())) {
                        for (InstancesDTO instance : series.getInstances()) {
                            MetadataBase metadata = instance.getMetadata();
                            if (metadata != null && metadata.getModality() != null
                                && !metadata.getModality().contains("SR")) {
                                selectedInstance = instance;
                                break;
                            }
                        }
                        if (selectedInstance != null) break;
                    }
                }
                if (selectedInstance != null) break;
            }
        }

        if (selectedInstance == null) {
            log.warn("没有找到合适的DICOM实例用于生成预览图:{}", attachment.getTargetId());
            return;
        }

        try {
            // 从URL中提取文件路径
            String instanceUrl = selectedInstance.getUrl();
            String relativePath = instanceUrl.substring(instanceUrl.indexOf("/extract/") + 9);
            String dicomFilePath = dicomPath + relativePath;
            File dicomFile = new File(dicomFilePath);

            if (!dicomFile.exists()) {
                log.warn("DICOM文件不存在:{}", dicomFilePath);
                return;
            }

            // 转换DICOM为BufferedImage
            BufferedImage previewImage = convertDicomToBufferedImage(dicomFile, selectedInstance.getMetadata());
            if (previewImage == null) {
                log.warn("无法从DICOM文件生成图像:{}", dicomFilePath);
                return;
            }

            // 保存预览图
            savePreviewImage(previewImage, attachment);

        } catch (Exception e) {
            log.error("生成DICOM预览图异常:{}", attachment.getTargetId(), e);
        }
    }

    /**
     * 将DICOM文件转换为BufferedImage
     *
     * @param dicomFile DICOM文件
     * @param metadata  元数据
     * @return BufferedImage
     */
    private BufferedImage convertDicomToBufferedImage(File dicomFile, MetadataBase metadata) {
        try (InputStream inputStream = FileUtil.getInputStream(dicomFile);
             DicomInputStream dis = new DicomInputStream(inputStream)) {

            Attributes attr = dis.readDataset(-1, -1);

            // 获取图像参数
            int rows = attr.getInt(Tag.Rows, 0);
            int columns = attr.getInt(Tag.Columns, 0);
            int bitsAllocated = attr.getInt(Tag.BitsAllocated, 16);
            int bitsStored = attr.getInt(Tag.BitsStored, 12);
            int pixelRepresentation = attr.getInt(Tag.PixelRepresentation, 0);
            String photometricInterpretation = attr.getString(Tag.PhotometricInterpretation, "MONOCHROME2");

            if (rows == 0 || columns == 0) {
                log.warn("DICOM文件缺少图像尺寸信息");
                return null;
            }

            // 获取像素数据
            Object pixelDataObj = attr.getValue(Tag.PixelData);
            if (pixelDataObj == null) {
                log.warn("DICOM文件没有像素数据");
                return null;
            }

            int[] pixelData;
            if (pixelDataObj instanceof byte[]) {
                byte[] byteData = (byte[]) pixelDataObj;
                pixelData = new int[byteData.length / 2];
                for (int i = 0; i < pixelData.length; i++) {
                    int low = byteData[i * 2] & 0xFF;
                    int high = byteData[i * 2 + 1] & 0xFF;
                    pixelData[i] = (high << 8) | low;
                    if (pixelRepresentation == 1 && pixelData[i] > (1 << (bitsStored - 1))) {
                        pixelData[i] -= (1 << bitsStored);
                    }
                }
            } else if (pixelDataObj instanceof short[]) {
                short[] shortData = (short[]) pixelDataObj;
                pixelData = new int[shortData.length];
                for (int i = 0; i < shortData.length; i++) {
                    pixelData[i] = shortData[i];
                }
            } else {
                log.warn("不支持的像素数据类型:{}", pixelDataObj.getClass());
                return null;
            }

            // 应用窗口调整
            long windowCenter = 0;
            long windowWidth = 0;
            if (metadata instanceof MetadataADTO) {
                windowCenter = ((MetadataADTO) metadata).getWindowCenter();
                windowWidth = ((MetadataADTO) metadata).getWindowWidth();
            } else if (metadata instanceof MetadataBDTO) {
                List<Long> centerList = ((MetadataBDTO) metadata).getWindowCenter();
                List<Long> widthList = ((MetadataBDTO) metadata).getWindowWidth();
                if (!CollectionUtils.isEmpty(centerList) && !CollectionUtils.isEmpty(widthList)) {
                    windowCenter = centerList.get(0);
                    windowWidth = widthList.get(0);
                }
            }

            if (windowCenter == 0 && windowWidth == 0) {
                // 使用默认窗口值
                windowCenter = (1 << (bitsStored - 1));
                windowWidth = (1 << bitsStored);
            }

            // 转换为8位图像数据
            byte[] imageData = applyWindowLevel(pixelData, windowCenter, windowWidth, rows, columns);

            // 创建BufferedImage
            BufferedImage image = new BufferedImage(columns, rows, BufferedImage.TYPE_BYTE_GRAY);
            DataBufferByte dataBuffer = (DataBufferByte) image.getRaster().getDataBuffer();
            System.arraycopy(imageData, 0, dataBuffer.getData(), 0, imageData.length);

            // 处理MONOCHROME1（反转）
            if ("MONOCHROME1".equals(photometricInterpretation)) {
                for (int i = 0; i < imageData.length; i++) {
                    dataBuffer.getData()[i] = (byte) (255 - (dataBuffer.getData()[i] & 0xFF));
                }
            }

            return image;

        } catch (Exception e) {
            log.error("转换DICOM文件为图像失败", e);
            return null;
        }
    }

    /**
     * 应用窗口调整
     *
     * @param pixelData    像素数据
     * @param windowCenter 窗口中心
     * @param windowWidth  窗口宽度
     * @param rows         行数
     * @param columns      列数
     * @return 8位图像数据
     */
    private byte[] applyWindowLevel(int[] pixelData, long windowCenter, long windowWidth, int rows, int columns) {
        byte[] imageData = new byte[rows * columns];

        double windowMin = windowCenter - windowWidth / 2.0;
        double windowMax = windowCenter + windowWidth / 2.0;

        for (int i = 0; i < pixelData.length; i++) {
            double pixelValue = pixelData[i];

            // 应用窗口调整
            if (pixelValue <= windowMin) {
                imageData[i] = 0;
            } else if (pixelValue >= windowMax) {
                imageData[i] = (byte) 255;
            } else {
                double normalizedValue = (pixelValue - windowMin) / (windowMax - windowMin);
                imageData[i] = (byte) (normalizedValue * 255);
            }
        }

        return imageData;
    }

    /**
     * 保存预览图
     *
     * @param previewImage 预览图像
     * @param attachment   附件信息
     */
    private void savePreviewImage(BufferedImage previewImage, Attachment attachment) {
        try {
            // 生成预览图文件名
            String previewFileName = attachment.getTargetId() + "_preview.png";

            // 创建临时文件
            String tempDir = System.getProperty("java.io.tmpdir");
            File tempFile = new File(tempDir, previewFileName);

            // 保存图像为PNG格式
            ImageIO.write(previewImage, "PNG", tempFile);

            // 读取文件数据
            byte[] imageData = FileUtil.readBytes(tempFile);

            // 创建附件DTO
            AttachmentLocalFileDTO attachmentDTO = new AttachmentLocalFileDTO();
            attachmentDTO.setFileName(previewFileName);
            attachmentDTO.setFileData(imageData);
            attachmentDTO.setAttachmentType(AttachmentTypeEnum.DICOM_PICTURE.getCode());
            attachmentDTO.setContentType("image/png");
            attachmentDTO.setTargetId(attachment.getTargetId());
            attachmentDTO.setOrderCol(1);

            // 保存附件
            attachmentService.saveFile(attachmentDTO);

            // 删除临时文件
            FileUtil.del(tempFile);

            log.info("DICOM预览图保存成功:{}, 文件名:{}", attachment.getTargetId(), previewFileName);

        } catch (Exception e) {
            log.error("保存DICOM预览图失败:{}", attachment.getTargetId(), e);
        }
    }
}
