package com.endovas.cps.service.measurement;

import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskRecordSearchFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskRecordListVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:48
 */
public interface MeasurementTaskRecordService {
    /**
     * 获取测量任务日志列表
     * @param searchFO
     * @param loginUser
     * @return
     */
    List<MeasurementTaskRecordListVO> list(MeasurementTaskRecordSearchFO searchFO, LoginUser loginUser);

    /**
     * 创建测量任务日志
     * @param beforePO
     * @param afterPO
     * @param opContent
     * @param opUserId
     * @param opUserBelong
     */
    void add(MeasurementTask beforePO, MeasurementTask afterPO, String opContent, String opUserId, String opUserBelong);
}
