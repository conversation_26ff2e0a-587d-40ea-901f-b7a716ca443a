package com.endovas.cps.service.surgery.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.surgery.SurgeryType;
import com.endovas.cps.pojo.fo.surgery.SurgeryTypeAddFO;
import com.endovas.cps.pojo.fo.surgery.SurgeryTypeEditFO;
import com.endovas.cps.pojo.vo.surgery.SurgeryTypeListVO;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.google.common.collect.Lists;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.exception.BusinessAssertException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/7/15
 * Time: 15:06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SurgeryTypeServiceImpl implements SurgeryTypeService {
    private final SurgeryTypeDAO surgeryTypeDAO;
    private final DicomDAO dicomDAO;


    @Override
    public List<SurgeryTypeListVO> list() {
        List<SurgeryTypeListVO> parents = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc().stream().map(x -> new SurgeryTypeListVO().convertFrom(x)).collect(Collectors.toList());
        List<SurgeryTypeListVO> all = surgeryTypeDAO.findByParentIdIsNotNullOrderBySerialNumberAsc().stream().map(x -> new SurgeryTypeListVO().convertFrom(x)).collect(Collectors.toList());
        parents.forEach(x -> getChildren(all, x));
        return parents;
    }

    @Override
    public List<SurgeryTypeListVO> select() {
        List<SurgeryTypeListVO> parents = surgeryTypeDAO.findByParentIdIsNullAndActiveIsTrueOrderBySerialNumberAsc().stream().map(x -> new SurgeryTypeListVO().convertFrom(x)).collect(Collectors.toList());
        List<SurgeryTypeListVO> all = surgeryTypeDAO.findByParentIdIsNotNullAndActiveIsTrueOrderBySerialNumberAsc().stream().map(x -> new SurgeryTypeListVO().convertFrom(x)).collect(Collectors.toList());
        parents.forEach(x -> getChildren(all, x));
        return parents;
    }

    @Override
    public void del(String id) {
        SurgeryType currentSurgeryType = surgeryTypeDAO.findById(id).orElseThrow(() -> new BusinessAssertException("分类ID不存在"));
        List<SurgeryType> all = surgeryTypeDAO.findByParentIdIsNotNullOrderBySerialNumberAsc();
        List<String> childrenId = Lists.newArrayList();
        getChildrenId(all, currentSurgeryType, childrenId);
        childrenId.add(currentSurgeryType.getId());
        List<Dicom> dicoms = dicomDAO.findBySurgeryTypeIdIn(childrenId);
        if (CollectionUtil.isNotEmpty(dicoms)) {
            throw new BusinessAssertException("当前主题/子分类有关联影像文件记录，无法删除");
        }
        surgeryTypeDAO.deleteAllById(childrenId);
    }

    private void getChildrenId(List<SurgeryType> all, SurgeryType parent, List<String> childrenId) {
        for (SurgeryType vo : all) {
            if (parent.getId().equals(vo.getParentId())) {
                getChildrenId(all, vo, childrenId);
                childrenId.add(vo.getId());
            }
        }
    }

    private void getChildren(List<SurgeryTypeListVO> all, SurgeryTypeListVO parent) {
        for (SurgeryTypeListVO vo : all) {
            if (parent.getId().equals(vo.getParentId())) {
                getChildren(all, vo);
                parent.getChildren().add(vo);

            }
        }
        if (CollUtil.isEmpty(parent.getChildren())) {
            parent.setChildren(Lists.newArrayList());
        }
    }


    @Override
    public void add(SurgeryTypeAddFO input) {
        SurgeryType surgeryType = new SurgeryType();
        surgeryType.setName(input.getName());
        surgeryType.setSerialNumber(input.getSerialNumber());
        surgeryType.setActive(true);
        surgeryType.setNameWithParent(input.getNameWithParent());
        surgeryType.setParentId(input.getParentId());
        surgeryTypeDAO.save(surgeryType);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void edit(SurgeryTypeEditFO input) {
        SurgeryType surgeryType = surgeryTypeDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("分类ID不存在"));
        List<SurgeryType> all = surgeryTypeDAO.findByParentIdIsNotNullOrderBySerialNumberAsc();
        List<String> childrenId = Lists.newArrayList();
        getChildrenId(all, surgeryType, childrenId);
        List<SurgeryType> children = surgeryTypeDAO.findAllById(childrenId);
        for (SurgeryType child : children) {
            child.setNameWithParent(child.getNameWithParent().replaceFirst(surgeryType.getName(), input.getName()));
        }
        surgeryTypeDAO.saveAll(children);
        surgeryType.setName(input.getName());
        surgeryType.setSerialNumber(input.getSerialNumber());
        surgeryType.setNameWithParent(input.getNameWithParent());
        surgeryTypeDAO.save(surgeryType);
    }


    @Override
    public void switchAvailable(String id) {
        SurgeryType surgeryType = surgeryTypeDAO.findById(id).orElseThrow(() -> new BusinessAssertException("分类ID不存在"));
        surgeryType.setActive(!surgeryType.getActive());
        surgeryTypeDAO.save(surgeryType);
    }

    @Override
    public List<String> parentAndChildren(String id) {
        //目前就两级别,所以如果是子节点,返回直接点,如果是父节点,返回父节点和子节点
        SurgeryType surgeryType = surgeryTypeDAO.findById(id).orElseThrow(() -> new BusinessAssertException("分类ID不存在"));
        if (StrUtil.isNotEmpty(surgeryType.getParentId())) {
            return Lists.newArrayList(id);
        } else {
            List<String> allSurgeryTypeIds = surgeryTypeDAO.findByParentIdAndActiveIsTrue(surgeryType.getId()).stream().map(MysqlBase::getId).collect(Collectors.toList());
            allSurgeryTypeIds.add(surgeryType.getId());
            return allSurgeryTypeIds;
        }
    }
}
