package com.endovas.cps.service.equipment;

import com.endovas.cps.pojo.fo.equipment.EquipmentAddFO;
import com.endovas.cps.pojo.fo.equipment.EquipmentEditFO;
import com.endovas.cps.pojo.fo.equipment.EquipmentSearchFO;
import com.endovas.cps.pojo.vo.platform.equip.EquipmentDetailVO;
import com.endovas.cps.pojo.vo.platform.equip.EquipmentListVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:27
 */
public interface EquipmentService {
    PageVO<EquipmentListVO> list(EquipmentSearchFO search, PageFO pageFO);
    void add(EquipmentAddFO input);
    void edit(EquipmentEditFO input);
    EquipmentDetailVO allocateEquip(String organizationId,String softName);
    EquipmentDetailVO allocateEquipKickOther(String organizationId,String softName);
}
