package com.endovas.cps.service.hospital.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.constant.DictConst;
import com.endovas.cps.dao.agent.MedAgentDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.hospital.HospitalMedAgentDAO;
import com.endovas.cps.dao.trolley.TrolleyDAO;
import com.endovas.cps.entity.hospital.Hospital;
import com.endovas.cps.entity.hospital.HospitalMedAgent;
import com.endovas.cps.entity.trolley.Trolley;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.fo.hospital.HospitalAddFO;
import com.endovas.cps.pojo.fo.hospital.HospitalEditFO;
import com.endovas.cps.pojo.fo.hospital.HospitalSearchFO;
import com.endovas.cps.pojo.vo.EnterpriseSelectVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalListVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalSelectVO;
import com.endovas.cps.service.DictService;
import com.endovas.cps.service.RegionService;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.hospital.HospitalService;
import com.endovas.cps.service.hospital.HospitalUserService;
import com.google.common.collect.Lists;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HospitalServiceImpl implements HospitalService {
    private final HospitalDAO hospitalDAO;
    private final HospitalMedAgentDAO hospitalMedAgentDAO;
    private final AttachmentService attachmentService;
    private final HospitalUserService hospitalUserService;
    private final MedAgentDAO medAgentDAO;
    private final TrolleyDAO trolleyDAO;

    private final RegionService regionService;
    private final DictService dictService;


    @Override
    public PageVO<HospitalListVO> list(HospitalSearchFO searchFO, PageFO pageFO) {
        return query(StrUtil.EMPTY, searchFO, pageFO);
    }

    private PageVO<HospitalListVO> query(String medAgentId, HospitalSearchFO searchFO, PageFO pageFO) {
        Specification<Hospital> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            List<String> hospitalIds = Lists.newArrayList();

            if (StrUtil.isNotEmpty(medAgentId)) {
                hospitalIds.addAll(hospitalMedAgentDAO.findByMedAgentId(medAgentId).stream().map(HospitalMedAgent::getHospitalId).collect(Collectors.toList()));
            }

            if (StrUtil.isNotEmpty(searchFO.getName())) {
                Predicate p1 = criteriaBuilder.like(root.get(Hospital.NAME), "%" + searchFO.getName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getMedAgentName())) {
                List<String> medAgentIds = medAgentDAO.findByNameContaining(searchFO.getMedAgentName()).stream().map(MysqlBase::getId).collect(Collectors.toList());
                hospitalIds.addAll(hospitalMedAgentDAO.findByMedAgentIdIn(medAgentIds).stream().map(HospitalMedAgent::getHospitalId).collect(Collectors.toList()));

            }

            if (StrUtil.isNotEmpty(searchFO.getUdi())) {
                hospitalIds.addAll(trolleyDAO.findByUdiContaining(searchFO.getUdi()).stream().map(Trolley::getHospitalId).collect(Collectors.toList()));
            }

            if (StrUtil.isNotEmpty(searchFO.getSysVer())) {
                hospitalIds.addAll(trolleyDAO.findBySysVer(searchFO.getSysVer()).stream().map(Trolley::getHospitalId).collect(Collectors.toList()));
            }
            if (StrUtil.isNotEmpty(searchFO.getSoftVer())) {
                hospitalIds.addAll(trolleyDAO.findBySoftVer(searchFO.getSoftVer()).stream().map(Trolley::getHospitalId).collect(Collectors.toList()));
            }

            if (CollectionUtil.isNotEmpty(hospitalIds)) {
                CriteriaBuilder.In<String> inClause = criteriaBuilder.in(root.get(MysqlBase.FIELD_ID));
                CollectionUtil.distinct(hospitalIds).forEach(inClause::value);
                list.add(inClause);
            }
            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };
        Page<HospitalListVO> list = hospitalDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> {
            HospitalListVO one = new HospitalListVO().convertFrom(x);
            dictService.listDict(DictConst.HOSPITAL_LEVEL).stream().filter(d -> d.getCode().equals(x.getLevelCode())).findAny().ifPresent(d -> {
                one.setLevel(d.getName());
            });
            one.setTrolleyAmount(trolleyDAO.countByHospitalId(x.getId()));
            HospitalMedAgent hospitalMedAgent = hospitalMedAgentDAO.getByHospitalId(x.getId());
            if (Objects.nonNull(hospitalMedAgent)) {
                medAgentDAO.findById(hospitalMedAgent.getMedAgentId()).ifPresent(medAgent -> {
                    one.setMedAgentName(medAgent.getName());
                });
            }

            one.setHospitalUserAmount(hospitalUserService.count(x.getId()));
            one.setCountry(regionService.getNameByAddressCode(x.getCountryCode()));
            one.setProvince(regionService.getNameByAddressCode(x.getProvinceCode()));


            one.setLogo(attachmentService.getByTargetIdAndType(x.getId(), AttachmentTypeEnum.HOSPITAL_LOGO));
            return one;
        });
        return PageUtil.convert(list);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(HospitalAddFO input) {
        Hospital existHospital = hospitalDAO.getByName(input.getName());
        if (Objects.nonNull(existHospital)) {
            throw new BusinessAssertException("医院名称不能重复");
        }
        Hospital hospital = new Hospital();
        input.convertTo(hospital);
        hospitalDAO.save(hospital);

        if (StrUtil.isNotEmpty(input.getLogoAttachmentId())) {
            attachmentService.updateTmpUrlById(input.getLogoAttachmentId(), hospital.getId(), AttachmentTypeEnum.HOSPITAL_LOGO);
        }
        if (StrUtil.isNotEmpty(input.getMedAgentId())) {
            HospitalMedAgent hospitalMedAgent = new HospitalMedAgent();
            hospitalMedAgent.setHospitalId(hospital.getId());
            hospitalMedAgent.setMedAgentId(input.getMedAgentId());
            hospitalMedAgentDAO.save(hospitalMedAgent);
        }

    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void edit(HospitalEditFO input) {
        Hospital hospital = hospitalDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("医院不存在"));
        Hospital existHospital = hospitalDAO.getByName(input.getName());
        if (Objects.nonNull(existHospital) && !existHospital.getId().equals(hospital.getId())) {
            throw new BusinessAssertException("医院名称不能重复");
        }

        input.convertTo(hospital);
        hospitalDAO.save(hospital);
        if (StrUtil.isNotEmpty(input.getLogoAttachmentId())) {
            attachmentService.updateTmpUrlById(input.getLogoAttachmentId(), hospital.getId(), AttachmentTypeEnum.HOSPITAL_LOGO);
        }
        if (StrUtil.isNotEmpty(input.getMedAgentId())) {
            HospitalMedAgent hospitalMedAgent = hospitalMedAgentDAO.getByHospitalId(input.getId());
            if (Objects.isNull(hospitalMedAgent)) {
                hospitalMedAgent = new HospitalMedAgent();
            }
            hospitalMedAgent.setHospitalId(hospital.getId());
            hospitalMedAgent.setMedAgentId(input.getMedAgentId());
            hospitalMedAgentDAO.save(hospitalMedAgent);
        }
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void del(String id) {
        Hospital hospital = hospitalDAO.findById(id).orElseThrow(() -> new BusinessAssertException("医院不存在"));
        hospitalDAO.delete(hospital);
        hospitalUserService.batchDelete(id);

    }


    @Override
    public List<HospitalSelectVO> select() {
        return hospitalDAO.findAllByDelIsFalseOrderByCreateTimeDesc().stream().map(x -> {
            HospitalSelectVO one = new HospitalSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            one.setLogo(attachmentService.getByTargetIdAndType(x.getId(), AttachmentTypeEnum.HOSPITAL_LOGO));
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public List<EnterpriseSelectVO> select(String name) {
        return hospitalDAO.findByNameContaining(name).stream().map(x -> {
            EnterpriseSelectVO one = new EnterpriseSelectVO();
            one.setEnterpriseId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public List<HospitalSelectVO> selectByTrolleyId(String trolleyId) {
        //获取当前台车的信息,得到绑定的代理商
        Trolley trolley = trolleyDAO.findById(trolleyId).orElseThrow(() -> new BusinessAssertException("台车信息不存在"));
        List<String> hospitalIds = hospitalMedAgentDAO.findByMedAgentId(trolley.getMedAgentId()).stream().map(HospitalMedAgent::getHospitalId).collect(Collectors.toList());
        //根据代理商,获取所有这个代理商关联的医院
        return hospitalDAO.findByIdIn(hospitalIds).stream().map(x -> {
            HospitalSelectVO one = new HospitalSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList());
    }
}
