package com.endovas.cps.service.dashboard;

import com.endovas.cps.entity.patient.Patient;
import com.endovas.cps.pojo.vo.dashboard.*;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/6
 * Time: 15:38
 */
public interface DashboardService {
    DashBoardVO index(String year, LoginUser loginUser);

    List<RankVO> rank(String rankType, LoginUser loginUser);

    List<HospitalGPSVO> hospitalsLocationStats(LoginUser loginUser);

    PatientStatVO patientsStats(List<Patient> patients,Boolean needPatientLocationStats);

    DicomSelfStatsVO dicomSelfStats(String year, LoginUser loginUser);

    LabelStatsVO labelSelfStats(String year, LoginUser loginUser);

    MeasurementStatsVO measurementSelfStats(String year,LoginUser loginUser);

}
