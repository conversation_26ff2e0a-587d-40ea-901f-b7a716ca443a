package com.endovas.cps.service.resource.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.dao.MedAgentUserDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.resource.DicomSensitiveFieldDAO;
import com.endovas.cps.dao.resource.mapper.DicomMapper;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.resource.DicomSensitiveField;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.DicomStatusEnum;
import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAddFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAdditionalInfoFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomEditFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomSearchFO;
import com.endovas.cps.pojo.vo.FileUploadKeyParam;
import com.endovas.cps.pojo.vo.resource.DicomListVO;
import com.endovas.cps.pojo.vo.resource.DicomSelectVO;
import com.endovas.cps.service.access.DataAccessService;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.resource.DicomService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.FilePathUtils;
import com.endovas.cps.util.MimeTypes2;
import com.endovas.cps.util.NameHiddenUtil;
import com.google.common.collect.Lists;
import io.daige.starter.common.Project;
import io.daige.starter.common.database.mybatis.conditions.query.QueryWrap;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/7
 * Time: 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DicomServiceImpl extends ServiceImpl<DicomMapper, Dicom> implements DicomService {
    private final AttachmentService attachmentService;
    private final SurgeryTypeService surgeryTypeService;
    private final SurgeryTypeDAO surgeryTypeDAO;

    private final ServerProperties serverProperties;
    private final AliOSSProperties aliOSSProperties;
    private final DicomSensitiveFieldDAO dicomSensitiveFieldDAO;
    private final HospitalDAO hospitalDAO;
    private final PatientDAO patientDAO;


    private final PlatformUserDAO platformUserDAO;
    private final MedAgentUserDAO medAgentUserDAO;

    private final CreatorTypeService creatorTypeService;
    private final DataAccessService dataAccessService;

    @Override
    public List<DicomSelectVO> select(Boolean excludePatientId, SurgeryStageEnum surgeryStageEnum,
                                      LoginUser loginUser) {
        QueryWrap<Dicom> query = new QueryWrap();
        query = dataAccessService.filterDataAuth(query, loginUser);
        query.orderByDesc(MysqlBase.COL_CREATE_TIME);
        if (BooleanUtil.isFalse(excludePatientId)) {
            query.isNull(Dicom.COL_PATIENT_ID);
        }
        // query.eq(Dicom.COL_HAS_DICOM_FILE,true); // 这里关闭这个特性，因为有些文件比较大分析很慢，所以这里就不管是否包含dicom文件 wk_20241123
        query.eq(Dicom.COL_SURGERY_STAGE, surgeryStageEnum.getCode());
        return baseMapper.selectList(query).stream().map(x -> {
            DicomSelectVO one = new DicomSelectVO();
            one.setId(x.getId());
            one.setPatientId(x.getPatientId());
            one.setGender(x.getPatientGender());
            one.setName(x.getName());
            one.setHospitalId(x.getHospitalId());
            one.setSurgeryTypeId(x.getSurgeryTypeId());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadKeyParam save(DicomAddFO input, SurgeryStageEnum surgeryStageEnum, LoginUser loginUser) {
        Dicom dicom = new Dicom();
        dicom.setHasDicomFile(false);
        dicom.setName(input.getFileName());
        dicom.setStatus(DicomStatusEnum.UPLOADING.getCode());
        dicom.setTempFileStatus(DicomStatusEnum.TEMPFILE_CLEANEDUP.getCode());
        dicom.setSurgeryTypeId(input.getSurgeryTypeId());
        dicom.setPatientId(input.getPatientId());
        dicom.setHospitalId(input.getHospitalId());
        dicom.setRemark(input.getRemark());
        dicom.setSurgeryStage(surgeryStageEnum.getCode());
        dicom.setPassword(input.getPassword());
        dicom.setCreatorBelong(loginUser.getBelong().getCode());
        dicom.setMedAgentId(loginUser.getMedAgentId());
        dicom.setCreatorId(loginUser.getId()); // 这里手动设置创建者是因为 处理邮件附件的时候，是系统调用的，无法获取到用户信息
        baseMapper.insert(dicom);

        List<DicomSensitiveField> dicomSensitiveFields = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(input.getSensitiveFields())) {
            for (String sensitiveField : input.getSensitiveFields()) {
                DicomSensitiveField dicomSensitiveField = new DicomSensitiveField();
                dicomSensitiveField.setDicomId(dicom.getId());
                dicomSensitiveField.setCode(sensitiveField);
                dicomSensitiveFields.add(dicomSensitiveField);
            }
        }


        if (CollectionUtil.isNotEmpty(dicomSensitiveFields)) {
            dicomSensitiveFieldDAO.saveAll(dicomSensitiveFields);
        }

        String targetId = dicom.getId();
        String filePath = FilePathUtils.genOssFileFullPath(AttachmentTypeEnum.DICOM.getCode(), targetId,
                input.getFileName());
        Attachment attachment = new Attachment();
        attachment.setTargetId(targetId);
        attachment.setType(AttachmentTypeEnum.DICOM.getCode());
        attachment.setName(input.getFileName());
        attachment.setUrl(filePath);
        attachment.setContentType(MimeTypes2.getContentType(input.getFileName()));
        attachment.setCreatorId(loginUser.getId()); // 这里手动设置创建者是因为 处理邮件附件的时候，是系统调用的，无法获取到用户信息
        attachmentService.saveFile(attachment);


        FileUploadKeyParam result = new FileUploadKeyParam();
        result.setFileName(input.getFileName());
        result.setFilePath(filePath);
        result.setCallbackUrl(aliOSSProperties.getCallBackDomain() + serverProperties.getServlet().getContextPath() + Project.ANON + "/aliOss/callback/process");
        result.setTargetId(targetId);
        result.setAttachmentId(attachment.getId());
        return result;
    }


    @Override
    public PageVO<DicomListVO> list(DicomSearchFO param, SurgeryStageEnum surgeryStageEnum, PageFO pageFO,
                                    LoginUser loginUser) {
        QueryWrap<Dicom> query = new QueryWrap();
        query = dataAccessService.filterDataAuth(query, loginUser);

        query.eq(Dicom.COL_SURGERY_STAGE, surgeryStageEnum.getCode());
        // 排序
        query.orderByDesc(MysqlBase.COL_CREATE_TIME);

        IPage<Dicom> page = PageUtil.initMybatisPage(pageFO);
        // 影像名称
        if (StringUtils.isNotBlank(param.getName())) {
            query.like(Dicom.COL_NAME, param.getName());
        }

        //疾病类型
        if (StringUtils.isNotBlank(param.getSurgeryTypeId())) {
            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(param.getSurgeryTypeId());
            query.in(Dicom.COL_SURGERY_TYPE_ID, allSurgeryTypeIds);
        }

        //影像描述
        if (StringUtils.isNotBlank(param.getRemark())) {
            query.like(Dicom.COL_REMARK, param.getRemark());
        }
        //患者姓名
        if (StringUtils.isNotBlank(param.getPatientName())) {
            query.eq(Dicom.COL_PATIENT_NAME, param.getPatientName());
        }

        //医院名称
        if (StringUtils.isNotBlank(param.getHospitalName())) {
            List<String> hospitalIds =
                    hospitalDAO.findByNameContaining(param.getHospitalName()).stream().map(MysqlBase::getId).collect(Collectors.toList());
            query.in(Dicom.COL_HOSPITAL_ID, hospitalIds);
        }
        //上传人
        if (StringUtils.isNotBlank(param.getCreator())) {
            List<String> creatorIds = Lists.newArrayList();
            switch (loginUser.getBelong()) {
                case PLATFORM_USER: {

                    List<String> platformCreatorIds =
                            platformUserDAO.findByNickNameContaining(param.getCreator()).stream().map(MysqlBase::getId).collect(Collectors.toList());
                    List<String> medAgentCreatorIds =
                            medAgentUserDAO.findByNickNameContaining(param.getCreator()).stream().map(MysqlBase::getId).collect(Collectors.toList());
                    creatorIds.addAll(platformCreatorIds);
                    creatorIds.addAll(medAgentCreatorIds);

                    break;
                }
                case MED_AGENT_USER: {
                    creatorIds =
                            medAgentUserDAO.findByNickNameContaining(param.getCreator()).stream().map(MysqlBase::getId).collect(Collectors.toList());
                    break;
                }
            }
            if (CollectionUtil.isNotEmpty(creatorIds)) {
                query.in(MysqlBase.COL_CREATOR_ID, creatorIds);
            } else {
                query.in(MysqlBase.COL_CREATOR_ID, param.getCreator());
            }
        }

        IPage<DicomListVO> dicomIPage = baseMapper.selectPage(page, query).convert(x -> convert(x, loginUser));
        return PageUtil.convert(dicomIPage);
    }

    @Override
    public List<DicomListVO> refreshStatus(List<String> ids, LoginUser loginUser) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectBatchIds(ids).stream().map(x -> convert(x, loginUser)).collect(Collectors.toList());
    }


    @Override
    public List<DicomListVO> list(String patientId, SurgeryStageEnum surgeryStageEnum, LoginUser loginUser) {
        QueryWrap<Dicom> query = new QueryWrap();
        query = dataAccessService.filterDataAuth(query, loginUser);

        query.eq(Dicom.COL_SURGERY_STAGE, surgeryStageEnum.getCode());
        query.eq(Dicom.COL_PATIENT_ID, patientId);
        return baseMapper.selectList(query).stream().map(x -> convert(x, loginUser)).collect(Collectors.toList());
    }

    private DicomListVO convert(Dicom dicom, LoginUser loginUser) {
        DicomListVO one = new DicomListVO().convertFrom(dicom);
        one.setPatientName(NameHiddenUtil.hidden(dicom.getPatientName()));
        CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(),
                BelongEnum.valueOf(dicom.getCreatorBelong()), dicom.getCreatorId());
        one.setCreator(creatorTypeDTO.getCreator());
        one.setCreatorType(creatorTypeDTO.getCreatorType());
        if (StrUtil.isNotEmpty(dicom.getHospitalId())) {
            hospitalDAO.findById(dicom.getHospitalId()).ifPresent(y -> {
                one.setHospitalName(y.getName());
            });
        }
        one.setGender(dicom.getPatientGender());
        if (StrUtil.isNotEmpty(dicom.getPatientId())) {
            patientDAO.findById(dicom.getPatientId()).ifPresent(y -> {
                one.setPatientName(NameHiddenUtil.hidden(y.getName()));
                one.setPatientId(y.getId());
                one.setGender(y.getGender());
            });
        }


        if (StrUtil.isNotEmpty(dicom.getSurgeryTypeId())) {
            surgeryTypeDAO.findById(dicom.getSurgeryTypeId()).ifPresent(x -> one.setSurgeryType(x.getName()));
        }

        one.setDicom(attachmentService.getByTargetIdAndType(dicom.getId(), AttachmentTypeEnum.DICOM));
        one.setDicomPic(attachmentService.getByTargetIdAndType(dicom.getId(),AttachmentTypeEnum.DICOM_PICTURE));
        return one;
    }


    @Override
    public void edit(DicomEditFO param) {
        Dicom dicom = baseMapper.selectById(param.getId());
        if (Objects.nonNull(dicom)) {
            dicom.setName(param.getName());
            dicom.setSurgeryTypeId(param.getSurgeryTypeId());
            dicom.setHospitalId(param.getHospitalId());
            baseMapper.updateById(dicom);
        }
    }

    @Override
    public void additionalInfo(DicomAdditionalInfoFO input) {
        Dicom dicom = baseMapper.selectById(input.getId());
        if (Objects.nonNull(dicom)) {
            dicom.setClinicalSymptoms(input.getClinicalSymptoms());
            dicom.setClinicalDiagnosis(input.getClinicalDiagnosis());
            dicom.setExaminationFindings(input.getExaminationFindings());
            baseMapper.updateById(dicom);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(String id) {
        baseMapper.deleteById(id);
        dicomSensitiveFieldDAO.deleteByDicomId(id);
    }

    @Async
    @Override
    public void updateStatusToProcess(String id) {
        Dicom dicom = baseMapper.selectById(id);
        dicom.setStatus(DicomStatusEnum.PROCESSING.getCode());
        baseMapper.updateById(dicom);
    }

    @Async
    @Override
    public void updateTempFileStatusToFinish(String id) {
        Dicom dicom = baseMapper.selectById(id);
        dicom.setTempFileStatus(DicomStatusEnum.TEMPFILE_READY.getCode());
        baseMapper.updateById(dicom);
    }

    @Override
    public void updateStatusToFail(DicomSearchFO param, LoginUser loginUser) {
        Dicom po = baseMapper.selectById(param.getId());
        po.setStatus(DicomStatusEnum.UPLOAD_FAIL.getCode());
        baseMapper.updateById(po);
    }
}
