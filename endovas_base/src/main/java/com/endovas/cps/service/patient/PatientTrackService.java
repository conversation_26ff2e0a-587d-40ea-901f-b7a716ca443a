package com.endovas.cps.service.patient;

import com.endovas.cps.pojo.fo.patient.track.PatientTrackAddFO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackEditFO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackSearchFO;
import com.endovas.cps.pojo.vo.patient.track.PatientTrackListVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:39
 */
public interface PatientTrackService {
    void add(PatientTrackAddFO input,LoginUser loginUser);
    void edit(PatientTrackEditFO input);
    PageVO<PatientTrackListVO> list(PatientTrackSearchFO search, PageFO pageFO, LoginUser loginUser);
    List<PatientTrackListVO> listByPatientId(String patientId, LoginUser loginUser);
}
