package com.endovas.cps.service.mailattach.impl;

import com.endovas.cps.dao.mailattach.MailAttachDAO;
import com.endovas.cps.dao.mailattach.MailAttachLinkDAO;
import com.endovas.cps.entity.mailattach.MailAttach;
import com.endovas.cps.entity.mailattach.MailAttachLink;
import com.endovas.cps.enums.MailAttachExtractStatusEnum;
import com.endovas.cps.enums.MailAttachLinkStatusEnum;
import com.endovas.cps.pojo.dto.mailattach.MailAccountBindDTO;
import com.endovas.cps.pojo.dto.mailattach.MailAddressDTO;
import com.endovas.cps.pojo.dto.mailattach.MailAttachConfigDTO;
import com.endovas.cps.service.mailattach.MailAttachService;
import com.endovas.cps.service.mailattach.NeteaseMailService;
import com.endovas.cps.service.mailattach.QQMailService;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import io.daige.starter.common.utils.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.mail.Address;
import javax.mail.BodyPart;
import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Part;
import javax.mail.Session;
import javax.mail.internet.InternetAddress;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

/**
 * @author: wk
 * @Date: 2025/2/11
 * @Time: 15:44
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MailAttachServiceImpl implements MailAttachService {

    private final String MAIL_SERVER_HOST = "imap.qiye.aliyun.com";
    private final String TENCENT_MAIL_SUFFIX = "@qq.com";
    private final String NETEASE_163_MAIL_SUFFIX = "@163.com";
    private final String NETEASE_126_MAIL_SUFFIX = "@126.com";

    private final String RECEIVE_FOLDER = "INBOX";
    private final String TRASH_FOLDER = "已删除邮件";

    private final MailAttachDAO mailAttachDAO;
    private final MailAttachLinkDAO mailAttachLinkDAO;
    private final QQMailService qqMailService;
    private final NeteaseMailService neteaseMailService;

    @Override
    public void extractBigFileLink(List<MailAttachConfigDTO> configList) throws MessagingException, IOException {

        // 设置连接属性
        Properties properties = new Properties();
        properties.put("mail.store.protocol", "imap");
        properties.put("mail.imap.host", MAIL_SERVER_HOST);
        properties.put("mail.imap.port", "993");
        properties.put("mail.imap.ssl.enable", "true");
        HashMap<String, String> IAM = new HashMap();
        IAM.put("name", "endovas_plt");
        IAM.put("version", "1.0.0");
        IAM.put("vendor", "endovas_plt");

        // 创建邮件会话
        Session emailSession = Session.getInstance(properties);


        for (MailAttachConfigDTO configDto : configList) {
            // 创建IMAP存储对象并连接到邮件服务器
            IMAPStore store = (IMAPStore) emailSession.getStore("imap");
            store.connect(configDto.getMailAccount(), configDto.getMailPassword());
            store.id(IAM);

            // 打开收件箱
            Folder inbox = store.getFolder(RECEIVE_FOLDER);
            if (!inbox.exists()) {
                log.info("No inbox folder found.");
                return;
            }
            inbox.open(Folder.READ_WRITE);

            // 打开已删除邮件文件夹
            Folder deletedFolder = store.getFolder(TRASH_FOLDER); // 根据你的邮箱服务商调整路径，例如Gmail的“All Mail”相当于其他服务器的“已删除邮件”
            IMAPFolder trash = (IMAPFolder) deletedFolder;
            //javamail中使用id命令有校验checkOpened, 所以要去掉id方法中的checkOpened();
            trash.doCommand(p -> {
                p.id("FUTONG");
                return null;
            });
            trash.open(Folder.READ_WRITE);

            // 获取绑定的邮件账号
            Map<String, String> bindMap = getMailReceiverAccountBindMap(configDto.getMailReceiverAccountBindList());

            // 收取收件箱的消息
            Message[] messageList = inbox.getMessages();
            for (Message msg : messageList) {
                // 判断邮件是否已经处理成功
                String mailId = calMessageId(msg);
                // 判断邮件的来源
                String content = getBody(msg);

                MailAttach mailAttach;
                MailAttach existingMailAttach = mailAttachDAO.getByMailId(mailId);

                if (existingMailAttach != null) {
                    // 如果邮件已存在且 状态是成功，则跳过
                    if (MailAttachExtractStatusEnum.SUCCESS.getCode().equals(existingMailAttach.getExtractStatus())) {
                        continue;
                    }

                    // 如果是初始化状态，则重新处理
                    mailAttach = existingMailAttach;
                } else {
                    // 创建新的邮件记录
                    mailAttach = new MailAttach();
                    mailAttach.setMailAccount(configDto.getMailAccount());
                    mailAttach.setMailId(mailId);
                    mailAttach.setMailContent(content);
                    mailAttach.setExtractStatus(MailAttachExtractStatusEnum.INIT.getCode());
                    mailAttachDAO.save(mailAttach);
                }

                try {
                    MailAddressDTO addressDTO = getLegalReceiver(msg, bindMap.keySet());
                    boolean extractSuccess = false;
                    if (Objects.isNull(addressDTO)) {
                        mailAttach.setExtractStatus(MailAttachExtractStatusEnum.SUCCESS.getCode());
                        mailAttachDAO.save(mailAttach);
                        inbox.copyMessages(new Message[]{msg}, trash);
                        continue;
                    }
                    if (qqMailService.containsLargeAttach(content)) {
                        // 提取QQ邮件链接
                        String downloadLink = qqMailService.extractDownloadLink(content);
                        if (StringUtils.isNotBlank(downloadLink)) {
                            saveMailAttachLink(mailAttach.getId(), downloadLink, content,
                                    bindMap.get(addressDTO.getReceiverAddress()), "QQ");
                            inbox.copyMessages(new Message[]{msg}, trash);
                            extractSuccess = true;
                        }
                    } else if (neteaseMailService.containsLargeAttach(content)) {
                        // 提取网易邮件链接
                        String downloadLink = neteaseMailService.extractDownloadLink(content);
                        if (StringUtils.isNotBlank(downloadLink)) {
                            String mailType = addressDTO.getSenderAddress().contains(NETEASE_163_MAIL_SUFFIX) ?
                                    "NETEASE_163" : "NETEASE_126";
                            saveMailAttachLink(mailAttach.getId(), downloadLink, content,
                                    bindMap.get(addressDTO.getReceiverAddress()), mailType);
                            inbox.copyMessages(new Message[]{msg}, trash);
                            extractSuccess = true;
                        }
                    } else {
                        inbox.copyMessages(new Message[]{msg}, trash);
                    }

                    // 更新邮件处理状态
                    if (extractSuccess) {
                        mailAttach.setExtractStatus(MailAttachExtractStatusEnum.SUCCESS.getCode());
                        mailAttach.setErrorMsg(null);
                    } else {
                        mailAttach.setExtractStatus(MailAttachExtractStatusEnum.FAILED.getCode());
                        mailAttach.setErrorMsg("未能提取到有效的下载链接");
                        inbox.copyMessages(new Message[]{msg}, trash);
                    }
                    mailAttachDAO.save(mailAttach);

                } catch (Exception e) {
                    log.error("处理邮件附件失败: {}", mailId, e);
                    mailAttach.setExtractStatus(MailAttachExtractStatusEnum.FAILED.getCode());
                    mailAttach.setErrorMsg(e.getMessage());
                    mailAttachDAO.save(mailAttach);
                }
            }

            // Close the store and folder objects
            inbox.expunge();
            inbox.close(true);
            trash.close(false);
            store.close();
        }

    }


    private String calMessageId(Message msg) throws MessagingException {
        Date processDate = new Date();
        String subject = msg.getSubject();
        Address[] from = msg.getFrom();
        String fromAddress = ((InternetAddress) from[0]).getAddress();
        return String.format("%s%s%s", DateUtil.format(processDate, "yyyyMMddHHmmss"), subject, fromAddress);
    }

    private MailAddressDTO getLegalReceiver(Message msg, Set<String> mailReceiverSet) throws MessagingException {
        Address[] recipients = msg.getAllRecipients();

        // 检查收件人里候是否有配置的邮件账号
        for (Address recipient : recipients) {
            String recipientAddress = ((InternetAddress) recipient).getAddress();
            if (mailReceiverSet.contains(recipientAddress)) {

                Address[] from = msg.getFrom();
                String fromAddress = ((InternetAddress) from[0]).getAddress();
                MailAddressDTO result = new MailAddressDTO(fromAddress, recipientAddress);
                return result;
            }
        }

        // 检查发件人是否匹配配置的邮件账号
        Address[] from = msg.getFrom();
        String fromAddress = ((InternetAddress) from[0]).getAddress();
        if (mailReceiverSet.contains(fromAddress)) {
            MailAddressDTO result = new MailAddressDTO(fromAddress, fromAddress);
            return result;
        }

        return null;
    }

    private String getBody(Part part) throws MessagingException, IOException {
        if (part.isMimeType("text/html")) {
            return part.getContent().toString();
        }

        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                String body = getBody(bodyPart);
                if (StringUtils.isNotBlank(body)) {
                    return body;
                }
            }
        }

        return "";
    }

    private Map<String, String> getMailReceiverAccountBindMap(List<MailAccountBindDTO> mailSenderAccountBindList) {
        Map<String, String> mailSenderAccountBindMap = new HashMap<>();
        for (MailAccountBindDTO mailAccountBindDTO : mailSenderAccountBindList) {
            mailSenderAccountBindMap.put(mailAccountBindDTO.getReceiver(), mailAccountBindDTO.getPltAccount());
        }
        return mailSenderAccountBindMap;
    }

    private void saveMailAttachLink(String mailAttachId, String downloadLink, String content,
                                    String receiverAccount, String mailType) {
        String fileName = extractFileName(content, mailType);

        MailAttachLink linkEntity = new MailAttachLink();
        linkEntity.setMailAttachId(mailAttachId);
        linkEntity.setDownloadLink(downloadLink);
        linkEntity.setFileName(fileName);
        linkEntity.setReceiverAccount(receiverAccount);
        linkEntity.setMailType(mailType);
        linkEntity.setStatus(MailAttachLinkStatusEnum.INIT.getCode());

        mailAttachLinkDAO.save(linkEntity);
    }

    private String extractFileName(String content, String mailType) {
        // 根据邮件类型提取文件名的逻辑
        if ("QQ".equals(mailType)) {
            return qqMailService.extractFileName(content);
        } else {
            return neteaseMailService.extractFileName(content);
        }
    }
}
