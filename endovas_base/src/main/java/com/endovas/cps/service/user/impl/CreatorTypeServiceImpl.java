package com.endovas.cps.service.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.endovas.cps.dao.ExpertDAO;
import com.endovas.cps.dao.HospitalUserDAO;
import com.endovas.cps.dao.MedAgentUserDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.agent.MedAgentDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.entity.Role;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.service.role.UserRoleService;
import com.endovas.cps.service.user.CreatorTypeService;
import io.daige.starter.common.enums.BelongEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static io.daige.starter.common.enums.BelongEnum.*;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/1
 * Time: 15:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreatorTypeServiceImpl implements CreatorTypeService {
    private final PlatformUserDAO platformUserDAO;
    private final MedAgentUserDAO medAgentUserDAO;
    private final HospitalUserDAO hospitalUserDAO;
    private final ExpertDAO expertDAO;
    private final MedAgentDAO medAgentDAO;
    private final HospitalDAO hospitalDAO;

    private final UserRoleService userRoleService;

    @Override
    public CreatorTypeDTO convert(BelongEnum currentLoginUserBelongEnum, BelongEnum belongEnum, String creatorId) {
        CreatorTypeDTO result = new CreatorTypeDTO();
        // 当前登录人和数据创建人类型一致
        if (currentLoginUserBelongEnum.getCode().equals(belongEnum.getCode())) {
            switch (belongEnum) {
                case PLATFORM_USER: {
                    platformUserDAO.findById(creatorId).ifPresent(y -> {
                        result.setCreator(y.getNickName());
                        List<String> roleNames = userRoleService.findByUserId(creatorId).stream().map(Role::getName).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(roleNames)) {
                            result.setCreatorType(CollectionUtil.join(roleNames, ","));
                        }
                    });
                    return result;
                }
                case MED_AGENT_USER: {
                    medAgentUserDAO.findById(creatorId).ifPresent(y -> {
                        result.setCreator(y.getNickName());
                        List<String> roleNames = userRoleService.findByUserId(creatorId).stream().map(Role::getName).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(roleNames)) {
                            result.setCreatorType(CollectionUtil.join(roleNames, ","));
                        }
                    });
                    return result;
                }

            }

        } else {
            switch (belongEnum) {
                case MED_AGENT_USER: {
                    medAgentUserDAO.findById(creatorId).ifPresent(y -> {
                        result.setCreator(y.getNickName());
                        medAgentDAO.findById(y.getMedAgentId()).ifPresent(z -> {
                            result.setCreatorType(z.getName());
                        });
                    });
                    return result;
                }
                case HOSPITAL_USER: {
                    hospitalUserDAO.findById(creatorId).ifPresent(y -> {
                        result.setCreator(y.getNickName());
                        hospitalDAO.findById(y.getHospitalId()).ifPresent(z -> {
                            result.setCreatorType(z.getName());
                        });
                    });
                    return result;
                }
                case EXPERT: {
                    expertDAO.findById(creatorId).ifPresent(y -> {
                        result.setCreator(y.getNickName());
                        result.setCreatorType(EXPERT.getDesc());
                    });
                    return result;
                }
            }
        }


        return result;
    }
}
