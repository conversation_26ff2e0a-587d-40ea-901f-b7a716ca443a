package com.endovas.cps.service.mailattach;

import com.endovas.cps.pojo.dto.mailattach.MailAttachConfigDTO;

import javax.mail.MessagingException;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * @author: wk
 * @Date: 2025/2/11
 * @Time: 15:42
 */
public interface MailAttachService {
    /**
     * 从邮件中提取大文件下载链接
     * @param configList
     */
    void extractBigFileLink(List<MailAttachConfigDTO> configList) throws MessagingException, IOException, ExecutionException, InterruptedException;
}
