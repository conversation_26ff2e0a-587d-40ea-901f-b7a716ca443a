package com.endovas.cps.service.dashboard.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.constant.LabelTaskRecordConst;
import com.endovas.cps.constant.MeasurementTaskRecordConst;
import com.endovas.cps.dao.MedAgentUserDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.RegionDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.label.LabelTaskDAO;
import com.endovas.cps.dao.label.LabelTaskRecordDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskRecordDAO;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientTrackDAO;
import com.endovas.cps.dao.platform.EquipmentDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.dao.trolley.TrolleyDAO;
import com.endovas.cps.entity.Region;
import com.endovas.cps.entity.label.LabelTaskRecord;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.measurement.MeasurementTaskRecord;
import com.endovas.cps.entity.patient.Patient;
import com.endovas.cps.entity.patient.PatientTrack;
import com.endovas.cps.entity.platform.Equipment;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.surgery.SurgeryType;
import com.endovas.cps.entity.trolley.Trolley;
import com.endovas.cps.enums.*;
import com.endovas.cps.pojo.dto.dashboard.*;
import com.endovas.cps.pojo.dto.dashboard.label.LabelStats;
import com.endovas.cps.pojo.dto.dashboard.label.LabelStatsMonth;
import com.endovas.cps.pojo.dto.dashboard.measurement.MeasurementStats;
import com.endovas.cps.pojo.dto.dashboard.measurement.MeasurementStatsMonth;
import com.endovas.cps.pojo.vo.dashboard.*;
import com.endovas.cps.service.dashboard.DashboardService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.google.common.collect.Lists;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/6
 * Time: 15:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {
    private final DicomDAO dicomDAO;
    private final PatientDAO patientDAO;
    private final PatientTrackDAO patientTrackDAO;

    private final LabelTaskDAO labelTaskDAO;

    private final MeasurementTaskDAO measurementTaskDAO;

    private final SurgeryTypeDAO surgeryTypeDAO;
    private final SurgeryTypeService surgeryTypeService;

    private final EquipmentDAO equipmentDAO;
    private final TrolleyDAO trolleyDAO;
    private final HospitalDAO hospitalDAO;
    private final RegionDAO regionDAO;
    private final MeasurementTaskRecordDAO measurementTaskRecordDAO;
    private final PlatformUserDAO platformUserDAO;
    private final MedAgentUserDAO medAgentUserDAO;
    private final LabelTaskRecordDAO labelTaskRecordDAO;


    @Override
    public DashBoardVO index(String year, LoginUser loginUser) {
        DashBoardVO result = new DashBoardVO();
        List<Dicom> dicoms = dicomDAO.findBySurgeryStage(SurgeryStageEnum.PREOP.getCode());
        result.setDicomTotalAmt(Long.valueOf(dicoms.size()));

        Long totalSize = dicoms.stream().map(Dicom::getFileSize).filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .sum();
        result.setDicomTotalSize(totalSize);
        List<Patient> patients = patientDAO.findAll();
        result.setPatientTotalAmt(Long.valueOf(patients.size()));
        List<PatientTrack> patientTracks = patientTrackDAO.findAll();
        result.setPatientTrackTotalAmt(Long.valueOf(patientTracks.size()));
        List<Equipment> equipments = equipmentDAO.findAll();
        result.setEquipmentTotalAmt(Long.valueOf(equipments.size()));


        result.setMeasureTotalAmt(measurementTaskDAO.count());
        //todo 台车当前手术中数量/手术量
        result.setEquipmentIdleAmt(equipmentDAO.countByStatus(EquipmentStatusEnum.IDLE.getCode()));
        result.setTrolleyMeetingAmt(0L);
        long x = result.getMeasureTotalAmt() - 10;
        result.setSurgeryTotalAmt(x > 0 ? x : 0);

        List<Trolley> trolleys = trolleyDAO.findAll();
        result.setTrolleyTotalAmt(Long.valueOf(trolleys.size()));


        List<SurgeryType> surgeryTypes = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc();
        List<SurgeryTypeStats> surgeryTypeStat = Lists.newArrayList();
        for (SurgeryType surgeryType : surgeryTypes) {
            SurgeryTypeStats one = new SurgeryTypeStats();
            one.setSurgeryTypeName(surgeryType.getName());

            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
            one.setAmt(dicomDAO.countBySurgeryTypeIdIn(allSurgeryTypeIds));
            surgeryTypeStat.add(one);
        }
        result.setDicomTotalSurgeryTypeStats(surgeryTypeStat);

        List<SurgeryTypeStatsMonth> surgeryTypeStatsMonths = Lists.newLinkedList();
        for (int i = 1; i < 13; i++) {
            LocalDateTime firstDayOfMonth = DateUtil.beginOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            LocalDateTime lastDayOfMonth = DateUtil.endOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            List<SurgeryTypeStats> surgeryTypeStats = Lists.newArrayList();
            for (SurgeryType surgeryType : surgeryTypes) {
                SurgeryTypeStats one = new SurgeryTypeStats();
                one.setSurgeryTypeName(surgeryType.getName());

                List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
                one.setAmt(dicomDAO.countBySurgeryTypeIdInAndCreateTimeBetween(allSurgeryTypeIds, firstDayOfMonth, lastDayOfMonth));
                surgeryTypeStats.add(one);
            }
            SurgeryTypeStatsMonth month = new SurgeryTypeStatsMonth();
            month.setMonth(i);
            month.setSurgeryTypeStats(surgeryTypeStats);
            surgeryTypeStatsMonths.add(month);

        }
        result.setDicomMonthSurgeryTypeStats(surgeryTypeStatsMonths);
        List<PatientTrackStats> patientTrackStats = Lists.newLinkedList();
        for (PatientTrackMethodEnum value : PatientTrackMethodEnum.values()) {
            PatientTrackStats one = new PatientTrackStats();
            one.setName(value.getDesc());
            one.setAmt(patientTrackDAO.countByTrackMethod(value.getCode()));
            patientTrackStats.add(one);
        }

        result.setPatientTrackStats(patientTrackStats);
        return result;
    }

    @Override
    public List<RankVO> rank(String rankType, LoginUser loginUser) {
        RankTypeEnum rankTypeEnum = RankTypeEnum.valueOf(rankType);
        switch (rankTypeEnum) {
            case MEASURE_USER: {
                List<MeasurementTask> measurementTasks = measurementTaskDAO.findByTaskStatus(MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode());
                return top5MeasurementTask(measurementTasks);
            }
            case HOSPITAL: {
                List<Dicom> dicoms = dicomDAO.findBySurgeryStage(SurgeryStageEnum.PREOP.getCode());
                return top10HospitalByDicom(dicoms);
            }

        }
        return Lists.newArrayList();
    }

    private List<RankVO> top5MeasurementTask(List<MeasurementTask> measurementTasks) {
        List<RankVO> result = Lists.newLinkedList();
        Map<String, Long> measurerIds = measurementTasks.stream().map(MeasurementTask::getMeasurerId).filter(Objects::nonNull).collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        Map<String, Long> sortMeasurerIds = MapUtil.sortByValue(measurerIds, true);
        int i = 0;
        for (String sortMeasurerId : sortMeasurerIds.keySet()) {
            platformUserDAO.findById(sortMeasurerId).ifPresent(platformUser -> {
                RankVO one = new RankVO();
                one.setName(platformUser.getNickName());
                one.setAmt(sortMeasurerIds.get(sortMeasurerId));
                result.add(one);
            });
            medAgentUserDAO.findById(sortMeasurerId).ifPresent(medAgentUser -> {
                RankVO one = new RankVO();
                one.setName(medAgentUser.getNickName());
                one.setAmt(sortMeasurerIds.get(sortMeasurerId));
                result.add(one);
            });


            i++;
            if (i > 5) {
                break;
            }
        }
        return result;
    }

    private List<RankVO> top10HospitalByDicom(List<Dicom> dicoms) {
        List<RankVO> result = Lists.newLinkedList();
        Map<String, Long> hospitalIds = dicoms.stream().map(Dicom::getHospitalId).filter(Objects::nonNull).collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        Map<String, Long> sortHospitalIds = MapUtil.sortByValue(hospitalIds, true);
        int i = 0;
        for (String sortHospitalId : sortHospitalIds.keySet()) {
            hospitalDAO.findById(sortHospitalId).ifPresent(hospital -> {
                RankVO one = new RankVO();
                one.setName(hospital.getName());
                one.setAmt(sortHospitalIds.get(sortHospitalId));
                result.add(one);
            });
            i++;
            if (i > 10) {
                break;
            }
        }
        return result;
    }

    private List<RankVO> top5HospitalByMeasurement(List<MeasurementTask> measurementTasks) {
        List<RankVO> result = Lists.newLinkedList();
        Map<String, Long> hospitalIds = measurementTasks.stream().map(MeasurementTask::getHospitalId).filter(Objects::nonNull).collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        Map<String, Long> sortHospitalIds = MapUtil.sortByValue(hospitalIds, true);
        int i = 0;
        for (String sortHospitalId : sortHospitalIds.keySet()) {
            hospitalDAO.findById(sortHospitalId).ifPresent(hospital -> {
                RankVO one = new RankVO();
                one.setName(hospital.getName());
                one.setAmt(sortHospitalIds.get(sortHospitalId));
                result.add(one);
            });
            i++;
            if (i > 5) {
                break;
            }
        }
        return result;
    }

    private List<RankVO> top10HospitalByPatient(List<Patient> patients) {
        List<RankVO> result = Lists.newLinkedList();
        Map<String, Long> hospitalIds = patients.stream().map(Patient::getHospitalId).filter(Objects::nonNull).collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        Map<String, Long> sortHospitalIds = MapUtil.sortByValue(hospitalIds, true);
        int i = 0;
        for (String sortHospitalId : sortHospitalIds.keySet()) {
            hospitalDAO.findById(sortHospitalId).ifPresent(hospital -> {
                RankVO one = new RankVO();
                one.setId(hospital.getId());
                one.setName(hospital.getName());
                one.setAmt(sortHospitalIds.get(sortHospitalId));
                Region region = regionDAO.getByAddressCode(hospital.getProvinceCode());
                if (Objects.nonNull(region)) {
                    one.setProvinceName(region.getName());
                }
                result.add(one);
            });
            i++;
            if (i > 10) {
                break;
            }
        }
        return result;
    }


    @Override
    public List<HospitalGPSVO> hospitalsLocationStats(LoginUser loginUser) {
        return hospitalDAO.findAll().stream().filter(x -> StrUtil.isNotEmpty(x.getProvinceCode())).map(x -> {
            HospitalGPSVO one = new HospitalGPSVO();
            one.setName(x.getName());
            Region region = regionDAO.getByAddressCode(x.getProvinceCode());
            one.setGps(region.getCenter());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public PatientStatVO patientsStats(List<Patient> patients, Boolean needPatientLocationStats) {
        if (CollUtil.isEmpty(patients) && BooleanUtil.isTrue(needPatientLocationStats)) {
            patients = patientDAO.findAll();
        }
        PatientStatVO result = new PatientStatVO();
        result.setFemaleTotalAmt(patients.stream().filter(x -> StrUtil.isNotEmpty(x.getGender()) && BizCommonConst.FEMALE.equals(x.getGender())).count());
        result.setMaleTotalAmt(patients.stream().filter(x -> StrUtil.isNotEmpty(x.getGender()) && BizCommonConst.MALE.equals(x.getGender())).count());

        List<PatientAgeStats> patientAgeStats = Lists.newLinkedList();
        for (PatientAgeStatEnum value : PatientAgeStatEnum.values()) {
            PatientAgeStats one = new PatientAgeStats();
            one.setName(value.getDesc());
            one.setAmt(patientDAO.countByIdInAndAgeBetween(patients.stream().map(MysqlBase::getId).collect(Collectors.toList()), value.getStart(), value.getEnd()));
            patientAgeStats.add(one);
        }
        result.setAgeStats(patientAgeStats);

        if (BooleanUtil.isTrue(needPatientLocationStats)) {
            List<PatientLocationStats> patientLocationStats = Lists.newLinkedList();
            List<RankVO> top10Hospital = top10HospitalByPatient(patients);
            for (RankVO rankVO : top10Hospital) {
                PatientLocationStats one = new PatientLocationStats();
                one.setProvinceName(rankVO.getProvinceName());
                one.setMaleAmt(patients.stream().filter(x -> x.getHospitalId().equals(rankVO.getId()) && StrUtil.isNotEmpty(x.getGender()) && BizCommonConst.MALE.equals(x.getGender())).count());
                one.setFemaleAmt(patients.stream().filter(x -> x.getHospitalId().equals(rankVO.getId()) && StrUtil.isNotEmpty(x.getGender()) && BizCommonConst.FEMALE.equals(x.getGender())).count());
                patientLocationStats.add(one);
            }
            result.setLocationStats(patientLocationStats);
        }
        return result;
    }

    @Override

    public DicomSelfStatsVO dicomSelfStats(String year, LoginUser loginUser) {
        DicomSelfStatsVO result = new DicomSelfStatsVO();
        List<Dicom> dicoms = dicomDAO.findByCreatorIdAndStatusIn(loginUser.getId(), Lists.newArrayList(DicomStatusEnum.UPLOADING.getCode(), DicomStatusEnum.FINISH.getCode(), DicomStatusEnum.PROCESSING.getCode()));
        result.setDicomAmt(Long.valueOf(dicoms.size()));

        result.setSubmitLabelAmt(labelTaskDAO.countByCreatorId(loginUser.getId()));

        Long totalSize = dicoms.stream().map(Dicom::getFileSize).filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .sum();
        result.setDicomTotalSize(totalSize);
        List<Patient> patients = patientDAO.findByCreatorId(loginUser.getId());
        result.setPatientAmt(Long.valueOf(patients.size()));

        List<SurgeryType> surgeryTypes = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc();
        List<SurgeryTypeStats> surgeryTypeStats = Lists.newArrayList();
        for (SurgeryType surgeryType : surgeryTypes) {
            SurgeryTypeStats one = new SurgeryTypeStats();
            one.setSurgeryTypeName(surgeryType.getName());
            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
            one.setAmt(dicomDAO.countBySurgeryTypeIdInAndCreatorId(allSurgeryTypeIds, loginUser.getId()));
            surgeryTypeStats.add(one);
        }
        result.setSurgeryTypeStats(surgeryTypeStats);

        List<DicomMonthStats> dicomMonthStatsList = Lists.newLinkedList();
        for (int i = 1; i < 13; i++) {
            LocalDateTime firstDayOfMonth = DateUtil.beginOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            LocalDateTime lastDayOfMonth = DateUtil.endOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            DicomMonthStats dicomMonthStats = new DicomMonthStats();
            dicomMonthStats.setDicomAmt(dicomDAO.countByCreatorIdAndCreateTimeBetween(loginUser.getId(), firstDayOfMonth, lastDayOfMonth));
            dicomMonthStats.setSubmitMeasureAmt(measurementTaskDAO.countByCreatorIdAndCreateTimeBetween(loginUser.getId(), firstDayOfMonth, lastDayOfMonth));
            dicomMonthStatsList.add(dicomMonthStats);
        }
        result.setMonths(dicomMonthStatsList);
        return result;

    }

    @Override
    public LabelStatsVO labelSelfStats(String year, LoginUser loginUser) {
        LabelStatsVO result = new LabelStatsVO();
        result.setUnclaimedLabelTotalAmt(labelTaskDAO.countByTaskStatus(LabelStatusEnum.TASKSTATUS_UNCLAIMED.getCode()));
        result.setCompletedLabelTotalAmt(labelTaskDAO.countByLabelerIdAndTaskStatus(loginUser.getId(), LabelStatusEnum.TASKSTATUS_COMPLETED.getCode()));
        result.setGiveUpLabelTotalAmt(labelTaskRecordDAO.findByOperaterIdAndContent(loginUser.getId(), LabelTaskRecordConst.GIVE_UP).stream().map(LabelTaskRecord::getLabelTaskId).distinct().count());
        result.setAlreadyClaimedLabelTotalAmt(labelTaskDAO.countByLabelerIdAndTaskStatus(loginUser.getId(), LabelStatusEnum.TASKSTATUS_ALREADYCLAIMED.getCode()));
        List<SurgeryType> surgeryTypes = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc();
        List<SurgeryTypeStats> surgeryTypeStat = Lists.newArrayList();
        for (SurgeryType surgeryType : surgeryTypes) {
            SurgeryTypeStats one = new SurgeryTypeStats();
            one.setSurgeryTypeName(surgeryType.getName());

            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
            one.setAmt(labelTaskDAO.countByLabelerIdAndSurgeryTypeIdInAndTaskStatus(loginUser.getId(), allSurgeryTypeIds, LabelStatusEnum.TASKSTATUS_COMPLETED.getCode()));
            surgeryTypeStat.add(one);
        }

        result.setSurgeryTypeStats(surgeryTypeStat);

        List<LabelStatsMonth> labelStatsMonths = Lists.newLinkedList();
        for (int i = 1; i < 13; i++) {
            LocalDateTime firstDayOfMonth = DateUtil.beginOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            LocalDateTime lastDayOfMonth = DateUtil.endOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            LabelStatsMonth one = new LabelStatsMonth();
            one.setMonth(i);
            LabelStats labelStats = new LabelStats();
            labelStats.setName("完成任务数");
            labelStats.setAmt(labelTaskDAO.countByLabelerIdAndTaskStatusAndCreateTimeBetween(loginUser.getId(), LabelStatusEnum.TASKSTATUS_COMPLETED.getCode(), firstDayOfMonth, lastDayOfMonth));
            one.setLabelStats(Lists.newArrayList(labelStats));
            labelStatsMonths.add(one);

        }
        result.setLabelStatsMonths(labelStatsMonths);
        return result;
    }

    @Override
    public MeasurementStatsVO measurementSelfStats(String year, LoginUser loginUser) {
        MeasurementStatsVO result = new MeasurementStatsVO();
        result.setUnclaimedMeasurementTotalAmt(measurementTaskDAO.countByTaskStatus(MeasurementStatusEnum.TASKSTATUS_UNCLAIMED.getCode()));
        result.setCompletedMeasurementTotalAmt(measurementTaskDAO.countByMeasurerIdAndTaskStatus(loginUser.getId(), MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode()));
        result.setGiveUpMeasurementTotalAmt(measurementTaskRecordDAO.findByOperaterIdAndContent(loginUser.getId(), MeasurementTaskRecordConst.GIVE_UP).stream().map(MeasurementTaskRecord::getMeasurementId).distinct().count());
        result.setAlreadyClaimedMeasurementTotalAmt(measurementTaskDAO.countByMeasurerIdAndTaskStatusIn(loginUser.getId(), Lists.newArrayList(MeasurementStatusEnum.TASKSTATUS_ALREADYCLAIMED.getCode(), MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode())));
        List<SurgeryType> surgeryTypes = surgeryTypeDAO.findByParentIdIsNullOrderBySerialNumberAsc();
        List<SurgeryTypeStats> surgeryTypeStat = Lists.newArrayList();
        for (SurgeryType surgeryType : surgeryTypes) {
            SurgeryTypeStats one = new SurgeryTypeStats();
            one.setSurgeryTypeName(surgeryType.getName());

            List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(surgeryType.getId());
            one.setAmt(measurementTaskDAO.countByMeasurerIdAndSurgeryTypeIdInAndTaskStatus(loginUser.getId(), allSurgeryTypeIds, MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode()));
            surgeryTypeStat.add(one);
        }

        result.setSurgeryTypeStats(surgeryTypeStat);

        List<MeasurementStatsMonth> measurementStatsMonths = Lists.newLinkedList();
        for (int i = 1; i < 13; i++) {
            LocalDateTime firstDayOfMonth = DateUtil.beginOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            LocalDateTime lastDayOfMonth = DateUtil.endOfMonth(DateUtil.parse(year + "-" + i + "-01")).toLocalDateTime();
            MeasurementStatsMonth one = new MeasurementStatsMonth();
            one.setMonth(i);
            MeasurementStats measurementStats = new MeasurementStats();
            measurementStats.setName("完成任务数");
            measurementStats.setAmt(measurementTaskDAO.countByMeasurerIdAndTaskStatusAndCreateTimeBetween(loginUser.getId(), MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode(), firstDayOfMonth, lastDayOfMonth));
            one.setMeasurementStats(Lists.newArrayList(measurementStats));
            measurementStatsMonths.add(one);

        }
        result.setMeasurementStatsMonths(measurementStatsMonths);


        //医院排名
        List<MeasurementTask> measurementTasks = measurementTaskDAO.findByMeasurerIdAndTaskStatus(loginUser.getId(), MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode());
        result.setRanks(top5HospitalByMeasurement(measurementTasks));

        //病患画像
        List<Patient> patients = patientDAO.findByIdIn(measurementTasks.stream().map(MeasurementTask::getPatientId).collect(Collectors.toList()));
        result.setPatientStat(patientsStats(patients, false));
        return result;
    }
}
