package com.endovas.cps.service.surgery;

import com.endovas.cps.pojo.fo.surgery.SurgeryTypeAddFO;
import com.endovas.cps.pojo.fo.surgery.SurgeryTypeEditFO;
import com.endovas.cps.pojo.vo.surgery.SurgeryTypeListVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/7/15
 * Time: 15:06
 */
public interface SurgeryTypeService {
    List<SurgeryTypeListVO> list();
    List<SurgeryTypeListVO> select();

    void add(SurgeryTypeAddFO input);

    void edit(SurgeryTypeEditFO input);

    void del(String id);

    void switchAvailable(String id);

    List<String> parentAndChildren(String id);
}
