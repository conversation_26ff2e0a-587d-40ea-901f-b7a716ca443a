package com.endovas.cps.service.agent.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.agent.MedAgentDAO;
import com.endovas.cps.dao.hospital.HospitalMedAgentDAO;
import com.endovas.cps.dao.trolley.TrolleyDAO;
import com.endovas.cps.entity.agent.MedAgent;
import com.endovas.cps.entity.hospital.HospitalMedAgent;
import com.endovas.cps.entity.trolley.Trolley;
import com.endovas.cps.pojo.fo.agent.MedAgentAddFO;
import com.endovas.cps.pojo.fo.agent.MedAgentEditFO;
import com.endovas.cps.pojo.fo.agent.MedAgentSearchFO;
import com.endovas.cps.pojo.fo.organization.OrganizationAddFO;
import com.endovas.cps.pojo.vo.EnterpriseSelectVO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentListVO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentSelectVO;
import com.endovas.cps.service.RegionService;
import com.endovas.cps.service.agent.MedAgentService;
import com.endovas.cps.service.agent.MedAgentUserService;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.trolley.TrolleyService;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 17:19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MedAgentServiceImpl implements MedAgentService {
    private final MedAgentDAO medAgentDAO;
    private final MedAgentUserService medAgentUserService;

    private final RegionService regionService;
    private final HospitalMedAgentDAO hospitalMedAgentDAO;
    private final OrganizationService organizationService;
    private final TrolleyService trolleyService;
    private final TrolleyDAO   trolleyDAO;

    @Override
    public PageVO<MedAgentListVO> list(MedAgentSearchFO searchFO, PageFO pageFO) {
        Specification<MedAgent> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            if (StrUtil.isNotEmpty(searchFO.getUdi())) {
                Trolley trolley = trolleyService.getTrolleyIdsByUdi(searchFO.getUdi());
                if (Objects.nonNull(trolley)) {
                    Predicate p1 = criteriaBuilder.equal(root.get(MedAgent.FIELD_ID), trolley.getMedAgentId());
                    list.add(p1);
                }

            }
            if (StrUtil.isNotEmpty(searchFO.getName())) {
                Predicate p1 = criteriaBuilder.like(root.get(MedAgent.NAME), "%" + searchFO.getName() + "%");
                list.add(p1);
            }
            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };


        Page<MedAgentListVO> list = medAgentDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> {
            MedAgentListVO one = new MedAgentListVO().convertFrom(x);
            one.setCountry(regionService.getNameByAddressCode(x.getCountryCode()));
            one.setProvince(regionService.getNameByAddressCode(x.getProvinceCode()));
            one.setTrolleyAmount(trolleyDAO.countByMedAgentId(x.getId()));
            return one;
        });

        return PageUtil.convert(list);
    }

    @Override
    public List<MedAgentSelectVO> select() {
        return medAgentDAO.findAll().stream().map(x -> {
            MedAgentSelectVO one = new MedAgentSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public List<EnterpriseSelectVO> select(String name) {
        return medAgentDAO.findByNameContaining(name).stream().map(x -> {
            EnterpriseSelectVO one = new EnterpriseSelectVO();
            one.setEnterpriseId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(MedAgentAddFO input) {
        MedAgent existMedAgent = medAgentDAO.getByName(input.getName());
        if (Objects.nonNull(existMedAgent)) {
            throw new BusinessAssertException("代理商名称不能重复");
        }
        MedAgent medAgent = new MedAgent();
        input.convertTo(medAgent);
        medAgentDAO.save(medAgent);

        OrganizationAddFO param = new OrganizationAddFO();
        param.setName(input.getName());
        organizationService.add(param, medAgent.getId(), BelongEnum.MED_AGENT_USER.getCode());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void edit(MedAgentEditFO input) {
        MedAgent medAgent = medAgentDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("代理商不存在"));
        MedAgent existMedAgent = medAgentDAO.getByName(input.getName());
        if (Objects.nonNull(existMedAgent) && !existMedAgent.getId().equals(medAgent.getId())) {
            throw new BusinessAssertException("代理商名称不能重复");
        }
        input.convertTo(medAgent);
        medAgentDAO.save(medAgent);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void del(String id) {
        List<HospitalMedAgent> hospitalMedAgents = hospitalMedAgentDAO.findByMedAgentId(id);
        if (CollectionUtil.isNotEmpty(hospitalMedAgents)) {
            throw new BusinessAssertException("代理商已关联医院，请先解除关联后删除");
        }
        MedAgent medAgent = medAgentDAO.findById(id).orElseThrow(() -> new BusinessAssertException("代理商不存在"));
        medAgentDAO.delete(medAgent);
        medAgentUserService.batchDelete(id);
    }


}
