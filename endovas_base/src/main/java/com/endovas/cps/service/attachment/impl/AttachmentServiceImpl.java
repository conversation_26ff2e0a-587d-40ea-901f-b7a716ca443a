package com.endovas.cps.service.attachment.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.dao.attachment.AttachmentDAO;
import com.endovas.cps.dao.attachment.AttachmentTempDAO;
import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.entity.attachment.AttachmentTemp;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.dto.AttachmentLocalFileDTO;
import com.endovas.cps.pojo.dto.AttachmentUploadFileDTO;
import com.endovas.cps.pojo.vo.AttachmentVO;
import com.endovas.cps.service.UserService;
import com.endovas.cps.service.attachment.AttachmentService;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.exception.HttpStatusResponseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/17
 * Time: 上午11:19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AttachmentServiceImpl implements AttachmentService {
    private final AttachmentDAO attachmentDAO;
    private final AttachmentTempDAO attachmentTempDAO;
    private final AliOSSProperties aliOSSProperties;
    private final ServerProperties serverProperties;
    private final OSSClient ossClient;
    private final String FILE_ROOT_DIR = "endovas/attachment" ;
    private final String FILE_VIEW_API = BizCommonConst.PREFIX_PATH + "/base/attachment/view" ;
    private final UserService userService;


    /**
     * 传的附件信息会被临时存储到AttachmentTemp表中,然后返回attachmentTempId,
     *
     * @param dto
     * @return
     */
    @Override
    public AttachmentVO uploadFile(AttachmentUploadFileDTO dto) {
        if (StrUtil.isBlank(dto.getAttachmentType())) {
            throw new HttpStatusResponseException(500, "附件类型不能为空");
        }
        String fileName = getFileName(dto.getBody().getOriginalFilename());
        String downloadUrl = null;
        try {
            // TODO 要求前端统一更改下载处理方式
            if (dto.getAttachmentType().equals(AttachmentTypeEnum.MEASUREMENT_RESULT.getCode())
                    || dto.getAttachmentType().equals(AttachmentTypeEnum.DICOM.getCode())) {
                downloadUrl = upload2DefaultDirWithAliLink(dto.getBody().getInputStream(), FILE_ROOT_DIR, fileName);
            } else {
                downloadUrl = upload2DefaultDirWithSelfLink(dto.getBody().getInputStream(), FILE_ROOT_DIR, fileName);
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("上传文件到阿里异常", e);
        }
        AttachmentTemp attachmentTemp = new AttachmentTemp();
        attachmentTemp.setTmpOrderCol(dto.getOrderCol());
        attachmentTemp.setTmpType(dto.getAttachmentType());
        attachmentTemp.setTmpName(fileName);
        attachmentTemp.setTmpUrl(downloadUrl);
        attachmentTemp.setTmpContentType(dto.getContentType());
        attachmentTempDAO.save(attachmentTemp);


        AttachmentVO result = new AttachmentVO();
        result.setId(attachmentTemp.getId());
        result.setName(fileName);
        result.setUrl(downloadUrl);
        result.setContentType(dto.getContentType());
        result.setType(dto.getAttachmentType());
        result.setOrderCol(dto.getOrderCol());
        return result;
    }

    @Override
    public AttachmentVO saveFile(AttachmentLocalFileDTO dto) throws IOException {
        ByteArrayInputStream inputStream = IoUtil.toStream(dto.getFileData());
        String downloadUrl = upload2DefaultDirWithSelfLink(inputStream, FILE_ROOT_DIR, dto.getFileName());
        Attachment attachment = new Attachment();
        attachment.setType(dto.getAttachmentType());
        attachment.setOrderCol(dto.getOrderCol());
        attachment.setTargetId(dto.getTargetId());
        attachment.setUrl(downloadUrl);
        attachment.setContentType(dto.getContentType());
        attachment.setAttachmentTempId(null);
        attachment.setName(dto.getFileName());
        attachmentDAO.save(attachment);
        AttachmentVO result = new AttachmentVO();
        result.setId(attachment.getId());
        result.setName(dto.getFileName());
        result.setUrl(downloadUrl);
        result.setContentType(dto.getContentType());
        result.setType(dto.getAttachmentType());
        result.setOrderCol(dto.getOrderCol());
        return result;
    }

    @Override
    public void saveFile(Attachment attachment) {
        attachmentDAO.save(attachment);
    }

    @Override
    public void physicalDeleteFile(String id) {
        attachmentDAO.deleteById(id);
    }


    @Override
    public OSSObject viewFile(String filePath) {
        try {
            filePath = filePath.replace(getDownloadPrefixUrl(), "");
            return ossClient.getObject(aliOSSProperties.getBucketName(), filePath);
        } catch (Exception exception) {
            throw new BusinessAssertException("文件不存在");
        }
    }

    @Override
    public void deleteFile(String id) {
        Attachment attachment = attachmentDAO.getAttachmentById(id);
        if (Objects.nonNull(attachment)) {
            attachment.setDel(true);
            attachmentDAO.save(attachment);
        }
        AttachmentTemp attachmentTemp = attachmentTempDAO.getAttachmentTempById(id);
        if (Objects.nonNull(attachmentTemp)) {
            attachmentTempDAO.delete(attachmentTemp);
        }
    }

    @Override
    public List<AttachmentVO> findByTargetIdAndType(String targetId, AttachmentTypeEnum type) {
        return attachmentDAO.findByTargetIdAndType(targetId, type.getCode()).stream().map(x -> {
            AttachmentVO result = new AttachmentVO();
            result.setId(x.getId());
            result.setName(x.getName());
            result.setOrderCol(x.getOrderCol());
            result.setType(x.getType());
            result.setContentType(x.getContentType());
            result.setUrl(x.getUrl());
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public AttachmentVO getByTargetIdAndType(String targetId, AttachmentTypeEnum type) {
        Attachment attachment = attachmentDAO.getByTargetIdAndType(targetId, type.getCode());
        AttachmentVO result = new AttachmentVO();
        if (Objects.nonNull(attachment)) {
            result.setId(attachment.getId());
            result.setName(attachment.getName());
            result.setOrderCol(attachment.getOrderCol());
            result.setType(attachment.getType());
            result.setContentType(attachment.getContentType());
            result.setUrl(attachment.getUrl());
            result.setCreator(userService.getNickName(attachment.getCreatorId()));
        }
        return result;
    }


    /**
     * 临时附件信息替换到正式附件表中
     * 使用场景:处于编辑页面,附件已存在,用户更新了附件,此时不是真正的将附件替换,而且需要点击[确认]按钮之后才会将真正的附件替换.
     * 这个接口就是用在编辑接口[确认]按钮的业务逻辑中调用.
     * 因为上传附件的逻辑是若已经存在附件,则上传的附件信息会被临时存储到AttachmentTemp表中,然后返回attachmentTempId
     * 这个方法,是在业务逻辑更新操作的同时,将存储到AttachmentTemp表中的数据更新到Attachment表中
     *
     * @param existAttachmentTempOrAttachmentId 注意:必须是网页端传入的值,不要自己从数据库中去拿值!!!!!!
     *                                          existAttachmentTempOrAttachmentId中包含 attachment表的id和attachment_temp表的id
     */
    @Override
    public void updateTmpUrlById(String existAttachmentTempOrAttachmentId, String targetId, AttachmentTypeEnum type) {
        //        //处理逻辑删除,转化成真删除
        List<Attachment> attachments = attachmentDAO.findByTargetIdAndTypeAndDelIsTrue(targetId, type.getCode());
        for (Attachment attachment : attachments) {
            if (Objects.equals(existAttachmentTempOrAttachmentId, attachment.getId())) {
                attachment.setDel(false);
                attachmentDAO.save(attachment);
            } else {
                attachmentDAO.delete(attachment);
            }
        }

        if (StrUtil.isEmpty(existAttachmentTempOrAttachmentId)) {
            return;
        }
        //处理更新逻辑
        AttachmentTemp attachmentTemp = attachmentTempDAO.getAttachmentTempById(existAttachmentTempOrAttachmentId);
        if (Objects.nonNull(attachmentTemp)) {
            Attachment attachment = attachmentDAO.getByTargetIdAndType(targetId, attachmentTemp.getTmpType());
            if (Objects.isNull(attachment)) {
                attachment = new Attachment();
            }
            attachment.setType(attachmentTemp.getTmpType());
            attachment.setOrderCol(attachmentTemp.getTmpOrderCol());
            attachment.setTargetId(targetId);
            attachment.setAttachmentTempId(attachmentTemp.getId());
            attachment.setUrl(attachmentTemp.getTmpUrl());
            attachment.setContentType(attachmentTemp.getTmpContentType());
            attachment.setName(attachmentTemp.getTmpName());
            attachmentDAO.save(attachment);
        }
    }

    @Override
    public void updateTmpUrlById(List<String> existAttachmentTempOrAttachmentIds, String targetId,
                                 AttachmentTypeEnum type) {
        //处理逻辑删除,转化成真删除
        List<String> willDeleteAttachmentIds = attachmentDAO.findByTargetIdAndTypeAndDelIsTrue(targetId,
                type.getCode()).stream().map(x -> x.getId()).collect(Collectors.toList());
        List<Attachment> attachments = attachmentDAO.findByIdIn(willDeleteAttachmentIds);
        for (Attachment attachment : attachments) {
            if (CollUtil.isNotEmpty(existAttachmentTempOrAttachmentIds) && existAttachmentTempOrAttachmentIds.contains(attachment.getId())) {
                attachment.setDel(false);
                attachmentDAO.save(attachment);
            } else {
                attachmentDAO.delete(attachment);
            }
        }

        //处理更新逻辑
        if (CollUtil.isNotEmpty(existAttachmentTempOrAttachmentIds)) {
            for (String existAttachmentTempId : existAttachmentTempOrAttachmentIds) {
                AttachmentTemp attachmentTemp = attachmentTempDAO.getAttachmentTempById(existAttachmentTempId);
                if (Objects.nonNull(attachmentTemp)) {
                    Attachment attachment = attachmentDAO.getByAttachmentTempId(existAttachmentTempId);
                    if (Objects.isNull(attachment)) {
                        attachment = new Attachment();
                    }
                    attachment.setType(attachmentTemp.getTmpType());
                    attachment.setOrderCol(attachmentTemp.getTmpOrderCol());
                    attachment.setTargetId(targetId);
                    attachment.setAttachmentTempId(attachmentTemp.getId());
                    attachment.setUrl(attachmentTemp.getTmpUrl());
                    attachment.setContentType(attachmentTemp.getTmpContentType());
                    attachment.setName(attachmentTemp.getTmpName());
                    attachmentDAO.save(attachment);
                }
            }
        }
    }


    private String getFileName(String originalFilename) {
        int unixSep = originalFilename.lastIndexOf(47);
        int winSep = originalFilename.lastIndexOf(92);
        int pos = winSep > unixSep ? winSep : unixSep;
        if (pos != -1) {
            originalFilename = originalFilename.substring(pos + 1);
        }

        return URLUtil.encode(originalFilename);
    }

    private String upload2DefaultDirWithAliLink(InputStream inputStream, String fileDir, String fileName) {
        StringBuilder fileUrl = new StringBuilder();
        fileName = IdUtil.fastSimpleUUID() + fileName.substring(fileName.indexOf("."));
        if (!fileDir.endsWith(StrUtil.SLASH)) {
            fileDir = fileDir.concat(StrUtil.SLASH);
        }
        fileUrl.append(fileDir);
        fileUrl.append(DateUtil.format(new Date(), "yyyy-MM-dd"));
        fileUrl.append(StrUtil.SLASH);
        fileUrl.append(fileName);

        // String downloadUrl = getDownloadPrefixUrl() + fileUrl;
        ossClient.putObject(aliOSSProperties.getBucketName(), fileUrl.toString(), inputStream);
        return fileUrl.toString();
    }

    private String upload2DefaultDirWithSelfLink(InputStream inputStream, String fileDir, String fileName) {
        StringBuilder fileUrl = new StringBuilder();
        fileName = IdUtil.fastSimpleUUID() + fileName.substring(fileName.indexOf("."));
        if (!fileDir.endsWith(StrUtil.SLASH)) {
            fileDir = fileDir.concat(StrUtil.SLASH);
        }
        fileUrl.append(fileDir);
        fileUrl.append(DateUtil.format(new Date(), "yyyy-MM-dd"));
        fileUrl.append(StrUtil.SLASH);
        fileUrl.append(fileName);

        String downloadUrl = getDownloadPrefixUrl() + fileUrl;
        ossClient.putObject(aliOSSProperties.getBucketName(), fileUrl.toString(), inputStream);
        return downloadUrl;
    }


    private String getDownloadPrefixUrl() {
        String downloadUrl =
                aliOSSProperties.getDomain() + serverProperties.getServlet().getContextPath() + FILE_VIEW_API +
                        "?file=" ;
        return downloadUrl;
    }

}
