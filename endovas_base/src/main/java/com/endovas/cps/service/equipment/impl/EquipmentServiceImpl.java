package com.endovas.cps.service.equipment.impl;

import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.OrganizationDAO;
import com.endovas.cps.dao.platform.EquipmentDAO;
import com.endovas.cps.entity.agent.MedAgent;
import com.endovas.cps.entity.platform.Equipment;
import com.endovas.cps.enums.EquipmentStatusEnum;
import com.endovas.cps.pojo.fo.equipment.EquipmentAddFO;
import com.endovas.cps.pojo.fo.equipment.EquipmentEditFO;
import com.endovas.cps.pojo.fo.equipment.EquipmentSearchFO;
import com.endovas.cps.pojo.vo.platform.equip.EquipmentDetailVO;
import com.endovas.cps.pojo.vo.platform.equip.EquipmentListVO;
import com.endovas.cps.service.equipment.EquipmentService;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentServiceImpl implements EquipmentService {
    private final EquipmentDAO equipmentDAO;
    private final OrganizationDAO organizationDAO;


    @Override
    public PageVO<EquipmentListVO> list(EquipmentSearchFO search, PageFO pageFO) {
        Specification<Equipment> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StrUtil.isNotEmpty(search.getName())) {
                Predicate p1 = criteriaBuilder.like(root.get(MedAgent.NAME), "%" + search.getName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(search.getSoftName())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Equipment.SOFT_NAME), search.getSoftName());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(search.getStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Equipment.STATUS), search.getStatus());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(search.getOrganizationId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Equipment.ORGANIZATION_ID), search.getOrganizationId());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(search.getOs())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Equipment.OS), search.getOs());
                list.add(p1);
            }


            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };
        Page<EquipmentListVO> list = equipmentDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> {
            EquipmentListVO one = new EquipmentListVO().convertFrom(x);
            if (Objects.nonNull(x.getOrganizationId())) {
                organizationDAO.findById(x.getOrganizationId()).ifPresent(y -> {
                    one.setOrganizationName(y.getName());
                });
            }
            return one;
        });
        return PageUtil.convert(list);
    }

    @Override
    public void add(EquipmentAddFO input) {
        Equipment equipment = new Equipment();
        input.convertTo(equipment);
        equipment.setStatus(EquipmentStatusEnum.IDLE.getCode());
        equipmentDAO.save(equipment);
    }

    @Override
    public void edit(EquipmentEditFO input) {
        Equipment equipment = equipmentDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("设备资产信息不存在"));
        input.convertTo(equipment);
        equipmentDAO.save(equipment);

    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public EquipmentDetailVO allocateEquip(String organizationId,String softName) {
        Equipment po = equipmentDAO.findFirstByStatusAndSoftNameAndDelIsFalse(EquipmentStatusEnum.IDLE.getCode(),softName);
        if (Objects.nonNull(po)) {
            po.setStatus(EquipmentStatusEnum.INUSE.getCode());
            equipmentDAO.save(po);
            return new EquipmentDetailVO().convertFrom(po);
        } else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public EquipmentDetailVO allocateEquipKickOther(String organizationId,String softName) {
        // 将最早使用中的设备分配出去
        Equipment po = equipmentDAO.findFirstByStatusAndSoftNameAndDelIsFalseOrderByUpdateTimeAsc(EquipmentStatusEnum.INUSE.getCode(),softName);
        if (Objects.nonNull(po)) {
            po.setStatus(EquipmentStatusEnum.INUSE.getCode());
            equipmentDAO.save(po);
            return new EquipmentDetailVO().convertFrom(po);
        } else {
            return null;
        }
    }
}
