package com.endovas.cps.service.patient.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientTrackDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.patient.Patient;
import com.endovas.cps.entity.patient.PatientTrack;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.MeasurementStatusEnum;
import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.patient.MedicalHisEditFO;
import com.endovas.cps.pojo.fo.patient.PatientAddFO;
import com.endovas.cps.pojo.fo.patient.PatientEditFO;
import com.endovas.cps.pojo.fo.patient.PatientSearchFO;
import com.endovas.cps.pojo.vo.patient.LabTestReportVO;
import com.endovas.cps.pojo.vo.patient.PatientDetailVO;
import com.endovas.cps.pojo.vo.patient.PatientListVO;
import com.endovas.cps.pojo.vo.patient.PatientSelectVO;
import com.endovas.cps.service.access.DataAccessService;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.patient.PatientService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.NameHiddenUtil;
import com.google.common.collect.Lists;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatientServiceImpl implements PatientService {
    private final PatientDAO patientDAO;
    private final DicomDAO dicomDAO;
    private final SurgeryTypeDAO surgeryTypeDAO;
    private final SurgeryTypeService surgeryTypeService;
    private final MeasurementTaskDAO measurementTaskDAO;

    private final HospitalDAO hospitalDAO;
    private final CreatorTypeService creatorTypeService;
    private final PatientTrackDAO patientTrackDAO;

    private final DataAccessService dataAccessService;

    private final AttachmentService attachmentService;

    @Override
    public PageVO<PatientListVO> list(PatientSearchFO searchFO, PageFO page, LoginUser loginUser) {
        Specification<Patient> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = dataAccessService.filterDataAuth(root, criteriaBuilder, loginUser);

            List<String> allPatientIds = Lists.newArrayList();
            if (StrUtil.isNotEmpty(searchFO.getDicomName())) {
                List<String> patientIds =
                        dicomDAO.findByNameContainingAndPatientIdIsNotNull(searchFO.getDicomName()).stream().map(Dicom::getPatientId).collect(Collectors.toList());
                allPatientIds.addAll(patientIds);
            }

            if (StrUtil.isNotEmpty(searchFO.getDicomRemark())) {
                List<String> patientIds =
                        dicomDAO.findByRemarkContainingAndPatientIdIsNotNull(searchFO.getDicomRemark()).stream().map(Dicom::getPatientId).collect(Collectors.toList());
                allPatientIds.addAll(patientIds);
            }

            if (CollectionUtil.isNotEmpty(allPatientIds)) {
                CriteriaBuilder.In<String> inClause = criteriaBuilder.in(root.get(MysqlBase.FIELD_ID));
                CollectionUtil.distinct(allPatientIds).forEach(inClause::value);
                list.add(inClause);
            }

            if (StrUtil.isNotEmpty(searchFO.getSurgeryTypeId())) {
                List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(searchFO.getSurgeryTypeId());
                Predicate p1 = criteriaBuilder.in(root.get(MeasurementTask.SURGERY_TYPE_ID)).value(allSurgeryTypeIds);
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getName())) {
                Predicate p1 = criteriaBuilder.like(root.get(Patient.NAME), "%" + searchFO.getName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getHospitalId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Patient.HOSPITAL_ID), searchFO.getHospitalId());
                list.add(p1);
            }
            criteriaQuery.where(criteriaBuilder.and(list.toArray(new Predicate[list.size()])));
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get(Patient.CREATE_TIME)));
            return criteriaQuery.getRestriction();
        };
        page.setSort(Patient.CREATE_TIME);
        Page<PatientListVO> list = patientDAO.findAll(specification, PageUtil.initJPAPage(page)).map(x -> {
            PatientListVO one = new PatientListVO();
            one.convertFrom(x);
            one.setName(NameHiddenUtil.hidden(x.getName()));
            surgeryTypeDAO.findById(x.getSurgeryTypeId()).ifPresent(y -> {
                one.setSurgeryTypeName(y.getName());
            });
            hospitalDAO.findById(x.getHospitalId()).ifPresent(y -> {
                one.setHospitalName(y.getName());
            });
            Long preopImgCount = dicomDAO.countByPatientIdAndSurgeryStage(x.getId(), SurgeryStageEnum.PREOP.getCode());
            Long intraopImgCount = dicomDAO.countByPatientIdAndSurgeryStage(x.getId(),
                    SurgeryStageEnum.INTRAOP.getCode());
            Long postopImgCount = dicomDAO.countByPatientIdAndSurgeryStage(x.getId(),
                    SurgeryStageEnum.POSTOP.getCode());
            one.setHasPreopImg(preopImgCount > 0);
            one.setHasIntraopImg(intraopImgCount > 0);
            one.setHasPostopImg(postopImgCount > 0);


            //todo =========逻辑完善==============
            one.setSurgeryDate("2022-10-08 19:13");
            one.setHospitalUserName("李四");
            //todo =========逻辑完善==============

            List<MeasurementTask> measurementTasks = measurementTaskDAO.findByPatientIdAndTaskStatus(x.getId(),
                    MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode());
            one.setHasPreopMPlan(CollectionUtil.isNotEmpty(measurementTasks));
            List<PatientTrack> patientTracks = patientTrackDAO.findByPatientId(x.getId());
            one.setHasTrack(CollectionUtil.isNotEmpty(patientTracks));
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(),
                    BelongEnum.valueOf(x.getCreatorBelong()), x.getCreatorId());
            one.setCreator(creatorTypeDTO.getCreator());
            one.setCreatorType(creatorTypeDTO.getCreatorType());
            return one;


        });
        return PageUtil.convert(list);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(PatientAddFO input, LoginUser loginUser) {
        Patient patient = new Patient();
        input.convertTo(patient);
        patient.setMedAgentId(loginUser.getMedAgentId());
        patient.setCreatorBelong(loginUser.getBelong().getCode());
        dicomDAO.findById(input.getDicomId()).ifPresent(y -> {
            if (StrUtil.isNotEmpty(y.getPatientId())) {
                throw new BusinessAssertException("影像文件已被其他患者绑定");
            }
            y.setPatientId(patient.getId());
            String dicomPatientNameHidden = NameHiddenUtil.hidden(y.getPatientName());
            //如果影像上的患者姓名脱敏后和输入的一致,用影像上的患者姓名
            if (dicomPatientNameHidden.equals(input.getName())) {
                patient.setName(y.getPatientName());
            }
            dicomDAO.save(y);
        });
        patientDAO.save(patient);
        return patient.getId();
    }

    @Override
    public void edit(PatientEditFO input) {
        Patient patient = patientDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("患者不存在"));
        input.convertTo(patient);
        patientDAO.save(patient);
    }

    @Override
    public void editMedicalHis(MedicalHisEditFO input) {
        Patient patient = patientDAO.findById(input.getPatientId()).orElseThrow(() -> new BusinessAssertException("患者不存在"));
        patient.setChiefComplaint(input.getChiefComplaint());
        patient.setPresentIllnessHistory(input.getPresentIllnessHistory());
        patient.setPastMedicalHistory(input.getPastMedicalHistory());
        patientDAO.save(patient);
    }

    @Override
    public void del(String id) {
        Patient patient = patientDAO.findById(id).orElseThrow(() -> new BusinessAssertException("患者不存在"));
        patientDAO.delete(patient);
    }

    @Override
    public PatientDetailVO detail(String id, LoginUser loginUser) {
        Patient patient = patientDAO.findById(id).orElseThrow(() -> new BusinessAssertException("患者不存在"));
        PatientDetailVO result = new PatientDetailVO();
        result.convertFrom(patient);
        result.setName(NameHiddenUtil.hidden(patient.getName()));
        surgeryTypeDAO.findById(patient.getSurgeryTypeId()).ifPresent(y -> {
            result.setSurgeryTypeName(y.getName());
        });
        hospitalDAO.findById(patient.getHospitalId()).ifPresent(y -> {
            result.setHospitalName(y.getName());
        });

        //todo =========逻辑完善==============
        result.setHospitalUserName("李四");

        CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(),
                BelongEnum.valueOf(patient.getCreatorBelong()), patient.getCreatorId());
        result.setCreator(creatorTypeDTO.getCreator());
        result.setCreatorType(creatorTypeDTO.getCreatorType());
        return result;

    }

    @Override
    public List<PatientSelectVO> select(LoginUser loginUser) {
        Specification<Patient> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = dataAccessService.filterDataAuth(root, criteriaBuilder, loginUser);
            criteriaQuery.where(criteriaBuilder.and(list.toArray(new Predicate[list.size()])));
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get(Patient.CREATE_TIME)));
            return criteriaQuery.getRestriction();
        };

        return patientDAO.findAll(specification).stream().map(x -> {
            PatientSelectVO one = new PatientSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList());
    }


    @Override
    public void addLabTestReport(String patientId, AttachmentTypeEnum type, String attachmentId) {
        attachmentService.updateTmpUrlById(attachmentId, patientId, type);
    }

    @Override
    public LabTestReportVO labTestReport(String patientId) {
        LabTestReportVO one = new LabTestReportVO();
        one.setCbc(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.CBC));
        one.setUa(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.UA));
        one.setBmp(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.BMP));
        one.setAbg(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.ABG));
        one.setIs(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.IS));
        one.setCk(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.CK));
        one.setMb(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.MB));
        one.setCoag(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.COAG));
        one.setD_dimer(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.D_DIMER));
        one.setCrp(attachmentService.getByTargetIdAndType(patientId, AttachmentTypeEnum.CRP));
        return one;
    }
}
