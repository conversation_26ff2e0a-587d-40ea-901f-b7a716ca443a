package com.endovas.cps.service.resource.impl;

import com.endovas.cps.dao.resource.NormalFileDAO;
import com.endovas.cps.entity.resource.NormalFile;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.resource.NormalFileAddFO;
import com.endovas.cps.pojo.fo.resource.NormalFileEditFO;
import com.endovas.cps.pojo.vo.resource.NormalFileListVO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.resource.NormalFileService;
import com.endovas.cps.service.user.CreatorTypeService;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 13:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NormalFileServiceImpl implements NormalFileService {
    private final NormalFileDAO normalFileDAO;
    private final AttachmentService attachmentService;
    private final CreatorTypeService creatorTypeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(NormalFileAddFO input, LoginUser loginUser) {
        NormalFile normalFile = new NormalFile();
        normalFile.setRemark(input.getRemark());
        normalFile.setName(input.getFileName());
        normalFile.setPatientId(input.getPatientId());
        normalFile.setCreatorBelong(loginUser.getBelong().getCode());
        normalFile.setFileSize(input.getFileSize());
        normalFileDAO.save(normalFile);
        attachmentService.updateTmpUrlById(input.getAttachmentId(), normalFile.getId(), AttachmentTypeEnum.NORMAL_FILE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(NormalFileEditFO input) {
        NormalFile normalFile = normalFileDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("影像文件不存在"));
        normalFile.setRemark(input.getRemark());
        normalFile.setName(input.getFileName());
        normalFile.setFileSize(input.getFileSize());
        normalFileDAO.save(normalFile);
        attachmentService.updateTmpUrlById(input.getAttachmentId(), normalFile.getId(), AttachmentTypeEnum.NORMAL_FILE);
    }

    @Override
    public void del(String id) {
        NormalFile normalFile = normalFileDAO.findById(id).orElseThrow(() -> new BusinessAssertException("影像文件不存在"));
        normalFileDAO.delete(normalFile);
    }

    @Override
    public List<NormalFileListVO> list(String patientId, LoginUser loginUser) {
        return normalFileDAO.findByPatientId(patientId).stream().map(x -> {
            NormalFileListVO one = new NormalFileListVO();
            one.setId(x.getId());
            one.setName(x.getName());
            one.setRemark(x.getRemark());
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(x.getCreatorBelong()), x.getCreatorId());
            one.setCreator(creatorTypeDTO.getCreator());
            one.setCreatorType(creatorTypeDTO.getCreatorType());
            one.setCreateTime(x.getCreateTime());
            one.setAttachment(attachmentService.getByTargetIdAndType(x.getId(), AttachmentTypeEnum.NORMAL_FILE));
            return one;
        }).collect(Collectors.toList());

    }
}
