package com.endovas.cps.service.trolley;


import com.endovas.cps.entity.trolley.Trolley;
import com.endovas.cps.enums.TrolleyUseStatusEnum;
import com.endovas.cps.pojo.fo.trolley.TrolleyAddFO;
import com.endovas.cps.pojo.fo.trolley.TrolleyEditFO;
import com.endovas.cps.pojo.fo.trolley.TrolleySearchFO;
import com.endovas.cps.pojo.fo.trolley.TrolleyTransferFO;
import com.endovas.cps.pojo.vo.trolley.TrolleyListVO;
import com.endovas.cps.pojo.vo.trolley.TrolleySelectVO;
import com.endovas.cps.pojo.vo.trolley.TrolleyTransferVO;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:27
 */
public interface TrolleyService {
    PageVO<TrolleyListVO> list(TrolleySearchFO searchFO, PageFO pageFO);
    void add(TrolleyAddFO input);
     void edit(TrolleyEditFO input);
    void assignTrolley(String medAgentId, String udi, String installedDate);

    PageVO<TrolleyListVO> list4MedAgent(String medAgentId, TrolleySearchFO searchFO, PageFO pageFO);

    List<TrolleyListVO> listByHospitalId(String hospitalId);

    void bindHospital(String hospitalId, String udi,String cathRoomId, String installedDate);

    List<TrolleySelectVO> canBindTrolleyByHospitalId(String hospitalId);

    List<TrolleySelectVO> canBindTrolleyByMedAgentId(String medAgentId);




    Trolley getTrolleyIdsByUdi(String udi);

    PageVO<TrolleyListVO> listBuyTrolley(String medAgentId, PageFO pageFO);


    void transfer(TrolleyTransferFO input);

    TrolleyTransferVO transferLog(String trolleyId, BelongEnum belong);

    void changeUseStatus(String udi, TrolleyUseStatusEnum userStatus);
    void changeUseStatusOnLine(String udi);

}
