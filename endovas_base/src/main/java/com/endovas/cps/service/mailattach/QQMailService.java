package com.endovas.cps.service.mailattach;

import com.endovas.cps.pojo.dto.mailattach.MailAttachDownloadDTO;

/**
 * @author: wk
 * @Date: 2025/2/10
 * @Time: 18:18
 */
public interface QQMailService {

    /**
     * 判断是否包含qq的超大附件
     * @param mailContent
     * @return
     */
    boolean containsLargeAttach(String mailContent);

    /**
     * 提取下载链接
     */
    String extractDownloadLink(String mailContent);

    /**
     * 提取文件名
     */
    String extractFileName(String mailContent);

    /**
     * 下载并处理附件
     */
    MailAttachDownloadDTO downloadAndProcess(String downloadLink, String fileName, String receiverAccount);
}
