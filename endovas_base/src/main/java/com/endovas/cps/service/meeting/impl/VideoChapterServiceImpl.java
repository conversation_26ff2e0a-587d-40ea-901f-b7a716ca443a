package com.endovas.cps.service.meeting.impl;

import com.endovas.cps.dao.meeting.VideoChapterDAO;
import com.endovas.cps.entity.meeting.VideoChapter;
import com.endovas.cps.pojo.dto.meeting.VideoChapterDTO;
import com.endovas.cps.pojo.vo.meeting.VideoChapterAddFO;
import com.endovas.cps.service.meeting.VideoChapterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/11
 * Time: 12:41
 */
@Service
@RequiredArgsConstructor
public class VideoChapterServiceImpl implements VideoChapterService {
    private final VideoChapterDAO videoChapterDAO;


    @Override
    public List<VideoChapterDTO> list(String deviceId) {
        return videoChapterDAO.findByDeviceIdOrderByCreateTimeAsc(deviceId).stream().map(x -> {
            VideoChapterDTO one = new VideoChapterDTO();
            one.setId(x.getId());
            one.setTime(x.getTime());
            one.setRemark(x.getRemark());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public void add(VideoChapterAddFO input) {
        VideoChapter one = new VideoChapter();
        one.setDeviceId(input.getDeviceId());
        one.setRemark(input.getRemark());
        one.setTime(input.getTime());
        videoChapterDAO.save(one);
    }
}
