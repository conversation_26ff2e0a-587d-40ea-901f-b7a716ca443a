package com.endovas.cps.service.measurement.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.model.OSSObject;
import com.endovas.cps.dao.attachment.AttachmentTempDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskResultDAO;
import com.endovas.cps.dao.measurement.mapper.MeasurementTaskResultMapper;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.entity.attachment.AttachmentTemp;
import com.endovas.cps.entity.hospital.Hospital;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.measurement.MeasurementTaskResult;
import com.endovas.cps.entity.patient.Patient;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.GenderEnum;
import com.endovas.cps.enums.MeasureSourceTypeEnum;
import com.endovas.cps.enums.MeasurementStatusEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.dto.measurement.MeasurementTaskResultDTO;
import com.endovas.cps.pojo.fo.measurement.MeasurementResultAddFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskResultFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultByPatientIdVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultVO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.measurement.MeasurementResultService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.PdfExtractUtil;
import com.google.common.collect.Lists;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.PdfPageBase;
import com.spire.pdf.exporting.PdfImageInfo;
import com.spire.pdf.texts.PdfTextExtractOptions;
import com.spire.pdf.texts.PdfTextExtractor;
import com.spire.pdf.texts.PdfTextStrategy;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import javax.persistence.criteria.Predicate;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/25
 * @Time: 14:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeasurementResultServiceImpl implements MeasurementResultService {

    private final MeasurementTaskResultDAO measurementTaskResultDAO;
    private final MeasurementTaskResultMapper measurementTaskResultMapper;
    private final MeasurementTaskDAO measurementTaskDAO;
    private final HospitalDAO hospitalDAO;
    private final PatientDAO patientDAO;
    private final AttachmentService attachmentService;
    private final CreatorTypeService creatorTypeService;
    private final AttachmentTempDAO attachmentTempDAO;
    private final DicomDAO dicomDAO;


    private final String MEASUREMENT_TASK_RESULT_TEMPLATE_ID = "0";

    @Override
    public void upload(MeasurementResultAddFO input, LoginUser loginUser) {

        AttachmentTemp attachmentTemp = attachmentTempDAO.getAttachmentTempById(input.getAttachmentId());
        MeasurementTaskResult po = new MeasurementTaskResult();
        po.setSourceType(MeasureSourceTypeEnum.UPLOAD.getCode());
        po.setMeasurementId(input.getMeasurementId());
        po.setResultFileName(attachmentTemp.getTmpName());
        po.setResultFileHash(input.getFileHash());

        if (StrUtil.isNotEmpty(attachmentTemp.getTmpContentType()) && attachmentTemp.getTmpContentType().contains(
                "application")) {
            OSSObject report = attachmentService.viewFile(attachmentTemp.getTmpUrl());
            PdfDocument doc = new PdfDocument();
            doc.loadFromStream(report.getObjectContent());
            PdfPageBase page = doc.getPages().get(0);
            PdfTextExtractor textExtractor = new PdfTextExtractor(page);
            PdfTextExtractOptions extractOptions = new PdfTextExtractOptions();
            extractOptions.setStrategy(PdfTextStrategy.Simple);
            String text = textExtractor.extract(extractOptions);
            String firstComment = PdfExtractUtil.extractFirstComment(text);
            po.setComment(firstComment);
            String interventionPlan = PdfExtractUtil.extractInterventionPlan(text);
            po.setIpp(interventionPlan);
            PdfImageInfo[] images = page.getImagesInfo();

            BufferedImage processedImage;
            if (images.length >= 2) {
                processedImage = images[1].getImage();
            } else {
                processedImage = images[0].getImage();
            }
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                ImageIO.write(processedImage, "PNG", baos);
                po.setPreview(baos.toByteArray());
            } catch (IOException e) {
                log.error("转换预览图失败", e);
            }
        }

        measurementTaskResultDAO.save(po);
        attachmentService.updateTmpUrlById(input.getAttachmentId(), po.getId(), AttachmentTypeEnum.MEASUREMENT_RESULT);
    }

    @Override
    public void saveResult(MeasurementTaskResultFO input, LoginUser loginUser) {
        MeasurementTaskResult po =
                measurementTaskResultDAO.findByMeasurementIdAndSourceTypeAndDelIsFalse(input.getMeasurementId(),
                        MeasureSourceTypeEnum.LOCAL.getCode());
        if (Objects.isNull(po)) {
            po = new MeasurementTaskResult();
            po.setMeasurementId(input.getMeasurementId());
        }
        po.setResult(input.getResult());
        po.setSourceType(MeasureSourceTypeEnum.LOCAL.getCode());
        measurementTaskResultDAO.save(po);
    }

    @Override
    public void delete(MeasurementTaskResultFO input, LoginUser loginUser) {
        MeasurementTaskResult po =
                measurementTaskResultDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException(
                        "未查询到测量结果"));
        if (po.getSourceType().equals(MeasureSourceTypeEnum.LOCAL.getCode())) {
            new BusinessAssertException("本地编辑的测量结果不允许删除");
        }

        po.setDel(true);
        measurementTaskResultDAO.save(po);
    }

    @Override
    public List<MeasurementTaskResultListVO> getResultList(MeasurementTaskResultFO input, LoginUser loginUser) {
        List<MeasurementTaskResultDTO> poList =
                measurementTaskResultMapper.findListWithoutResultByMeasurementId(input.getMeasurementId());
        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }

        // 将本地编辑的排在第一位
        List<MeasurementTaskResultDTO> localList =
                poList.stream().filter(m -> m.getSourceType().equals(MeasureSourceTypeEnum.LOCAL.getCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(localList)) {
            poList.removeIf(m -> m.getSourceType().equals(MeasureSourceTypeEnum.LOCAL.getCode()));
            poList.add(0, localList.get(0));
        }

        // 补充附件
        List<MeasurementTaskResultListVO> resultList =
                poList.stream().map(x -> new MeasurementTaskResultListVO().convertFrom(x)).collect(Collectors.toList());
        resultList.stream().forEach(x -> {
            if (x.getSourceType().equals(MeasureSourceTypeEnum.UPLOAD.getCode())) {
                x.setAttachment(attachmentService.getByTargetIdAndType(x.getId(),
                        AttachmentTypeEnum.MEASUREMENT_RESULT));
            }
        });

        return resultList;
    }

    @Override
    public List<MeasurementTaskResultByPatientIdVO> listResultByPatientId(String patientId, LoginUser loginUser) {
        Specification<MeasurementTask> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = Lists.newArrayList();
            Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.PATIENT_ID), patientId);
            list.add(p1);
            Predicate p2 = criteriaBuilder.equal(root.get(MeasurementTask.TASK_STATUS),
                    MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode());
            list.add(p2);
            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };

        List<MeasurementTask> measurementTasks = measurementTaskDAO.findAll(specification);
        List<MeasurementTaskResultByPatientIdVO> results = Lists.newArrayList();
        for (MeasurementTask measurementTask : measurementTasks) {
            List<MeasurementTaskResult> measurementTaskResults =
                    measurementTaskResultDAO.findByMeasurementIdAndDelIsFalse(measurementTask.getId());
            for (MeasurementTaskResult measurementTaskResult : measurementTaskResults) {
                MeasurementTaskResultByPatientIdVO one = new MeasurementTaskResultByPatientIdVO();
                one.setId(measurementTaskResult.getId());
                one.setMeasurementId(measurementTaskResult.getMeasurementId());
                CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(),
                        BelongEnum.valueOf(measurementTask.getCreatorBelong()), measurementTaskResult.getCreatorId());
                one.setCreator(creatorTypeDTO.getCreator());
                one.setTaskCompletedTime(measurementTask.getTaskCompletedTime());
                dicomDAO.findById(measurementTask.getDicomId()).ifPresent(x -> {
                    one.setSurgeryStage(x.getSurgeryStage());
                });
                one.setSourceType(measurementTaskResult.getSourceType());
                if (MeasureSourceTypeEnum.UPLOAD.getCode().equals(measurementTaskResult.getSourceType())) {
                    one.setIpp(measurementTaskResult.getIpp());
                    one.setComment(measurementTaskResult.getComment());
                    if (Objects.nonNull(measurementTaskResult.getPreview())) {
                        one.setPreview("data:image/png;base64," + Base64.getEncoder().encodeToString(measurementTaskResult.getPreview()));
                    }
                    one.setReport(attachmentService.getByTargetIdAndType(measurementTaskResult.getId(),
                            AttachmentTypeEnum.MEASUREMENT_RESULT));
                } else {
                    if (StrUtil.isNotEmpty(measurementTaskResult.getResult())) {
                        JSONArray jsonArray = JSONUtil.parseObj(measurementTaskResult.getResult()).getJSONObject(
                                "data").getJSONArray("main");
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject dataObject = jsonArray.getJSONObject(i);
                            if(dataObject.containsKey("type")&& dataObject.getStr("type").equals("image")){
                                one.setPreview(dataObject.getStr("value"));
                            }
                        }

                    }


                }


                results.add(one);
            }
        }
        //将sourceType是local的排在前面
        CollectionUtil.sort(results, (o1, o2) -> {
            boolean o1IsLocal = MeasureSourceTypeEnum.LOCAL.getCode().equals(o1.getSourceType());
            boolean o2IsLocal = MeasureSourceTypeEnum.LOCAL.getCode().equals(o2.getSourceType());
            return Boolean.compare(o2IsLocal, o1IsLocal);
        });
        return results;
    }

    @Override
    public MeasurementTaskResultVO getResult(MeasurementTaskResultFO input, LoginUser loginUser) {
        MeasurementTaskResult po =
                measurementTaskResultDAO.findByMeasurementIdAndSourceTypeAndDelIsFalse(input.getMeasurementId(),
                        MeasureSourceTypeEnum.LOCAL.getCode());
        MeasurementTask taskPo =
                measurementTaskDAO.findById(input.getMeasurementId()).orElseThrow(() -> new BusinessAssertException(
                        "未查询到测量记录"));

        MeasurementTaskResultVO vo = new MeasurementTaskResultVO();
        // 补充患者信息
        Optional<Patient> patientOp = patientDAO.findById(taskPo.getPatientId());
        if (patientOp.isPresent()) {
            vo.setPatientName(patientOp.get().getName());
            vo.setGender(GenderEnum.get(patientOp.get().getGender()).getDesc());
            vo.setAge(String.valueOf(patientOp.get().getAge()));
        }

        if (Objects.isNull(po)) {
            po = measurementTaskResultDAO.findByMeasurementIdAndSourceTypeAndDelIsFalse(MEASUREMENT_TASK_RESULT_TEMPLATE_ID, MeasureSourceTypeEnum.LOCAL.getCode());
            po.setUpdateTime(LocalDateTime.now());
            vo = vo.convertFrom(po);

            Optional<Hospital> hospitalOp = hospitalDAO.findById(taskPo.getHospitalId());
            String hospitalName = "";
            if (hospitalOp.isPresent()) {
                hospitalName = hospitalOp.get().getName();
            }
            StringBuilder patientInfo = new StringBuilder();
            if (patientOp.isPresent()) {
                patientInfo.append(patientOp.get().getName());
                patientInfo.append("  ");
                patientInfo.append(GenderEnum.get(patientOp.get().getGender()).getDesc());
                patientInfo.append("  ");
                patientInfo.append(patientOp.get().getAge());
                patientInfo.append("岁");
            }
            vo.setResult(po.getResult().replace("##hospital##", hospitalName)
                    .replace("##patient##", patientInfo.toString()));
        } else {
            vo = vo.convertFrom(po);
        }

        if (taskPo.getTaskStatus().equals(MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode())) {
            vo.setIsFinished(true);
        } else {
            vo.setIsFinished(false);
        }
        return vo;
    }
}
