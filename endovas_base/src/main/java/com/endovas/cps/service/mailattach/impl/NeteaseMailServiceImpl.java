package com.endovas.cps.service.mailattach.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.dto.mailattach.MailAttachDownloadDTO;
import com.endovas.cps.pojo.dto.mailattach.NeteaseRespDTO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAddFO;
import com.endovas.cps.pojo.vo.FileUploadKeyParam;
import com.endovas.cps.service.DictService;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.mailattach.NeteaseMailService;
import com.endovas.cps.service.platform.PlatformUserService;
import com.endovas.cps.service.resource.DicomService;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

/**
 * @author: wk
 * @Date: 2025/2/12
 * @Time: 17:35
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NeteaseMailServiceImpl implements NeteaseMailService {
    private final String DOWNLOAD_KEYWORD = "https://mail.163.com/large-attachment-download";
    private final String NETEASE_163_LARGE_ATTACH_KEYWORD = "从网易163邮箱发来的超大附件";
    private final String NETEASE_126_LARGE_ATTACH_KEYWORD = "从网易126邮箱发来的超大附件";
    private final String MAIL_ATTACH_HOSPITAL_BIND_CONFIGKEY = "MAIL_ATTACH_HOSPITAL_BIND_CONFIG";

    private final FileService fileService;
    private final AliOSSProperties aliOSSProperties;
    private final DicomService dicomService;
    private final PlatformUserService platformUserService;
    private final DictService dictService;

    /**
     * 判断是否包含163的超大附件
     * @param mailContent
     * @return
     */
    @Override
    public boolean containsLargeAttach(String mailContent) {
        return mailContent.contains(NETEASE_163_LARGE_ATTACH_KEYWORD)
                || mailContent.contains(NETEASE_126_LARGE_ATTACH_KEYWORD);
    }

    /**
     * 处理邮件
     * @param mailContent
     * @param receiverAccount
     * @return
     */
    @Override
    public String extractDownloadLink(String mailContent) {
        if (!mailContent.contains(DOWNLOAD_KEYWORD)) {
            return null;
        }

        try {
            // 解析邮件内容，获取linkKey
            int linkIdx = mailContent.lastIndexOf(DOWNLOAD_KEYWORD);
            String downloadPageLinkTemp = mailContent.substring(linkIdx);
            String downloadPageLink = downloadPageLinkTemp.substring(0, downloadPageLinkTemp.indexOf("\""));
            log.info("downloadPageLink:" + downloadPageLink);

            Map<String, String> paramMap = HttpUtil.decodeParamMap(downloadPageLink, StandardCharsets.UTF_8);
            String linkKey = paramMap.get("file");
            log.info("linkKey:" + linkKey);

            // 请求并获取真正下载链接
            JSONObject object = new JSONObject();
            object.put("linkKey", linkKey);
            String postResponse = HttpUtil.post("https://mail.163.com/filehub/bg/dl/prepare", JSONUtil.toJsonStr(object));
            log.info("value:" + postResponse);

            if (StringUtils.isNotBlank(postResponse)) {
                NeteaseRespDTO neteaseRespDTO = JSONUtil.toBean(postResponse, NeteaseRespDTO.class);
                if (neteaseRespDTO.getCode() == 200
                        && Objects.nonNull(neteaseRespDTO.getData())
                        && StringUtils.isNotBlank(neteaseRespDTO.getData().getDownloadUrl())) {
                    return neteaseRespDTO.getData().getDownloadUrl();
                }
            }
        } catch (Exception e) {
            log.error("提取网易邮件下载链接失败", e);
        }

        return null;
    }

    @Override
    public String extractFileName(String mailContent) {
        try {
            int nameIdx = mailContent.lastIndexOf("filename=\"");
            if (nameIdx != -1) {
                String fileName = mailContent.substring(nameIdx + 10, mailContent.indexOf("\"", nameIdx + 11));
                log.info("fileName:" + fileName);
                return fileName;
            }
        } catch (Exception e) {
            log.error("提取网易邮件文件名失败", e);
        }
        return null;
    }

    @Override
    @Transactional
    public MailAttachDownloadDTO downloadAndProcess(String downloadLink, String fileName, String receiverAccount) {
        try {
            // 初始化dicom文件和对应的附件信息
            DicomAddFO addFO = new DicomAddFO();
            addFO.setFileName(fileName);

            // 获取邮件默认绑定的医院id
            String value = dictService.getOneParamValue(MAIL_ATTACH_HOSPITAL_BIND_CONFIGKEY);
            if (Objects.isNull(value)) {
                log.error("邮件处理，未配置默认医院");
                return null;
            }

            addFO.setHospitalId(value);
            addFO.setSensitiveFields(new ArrayList<>());

            PlatformUser user = platformUserService.getByAccount(receiverAccount);
            LoginUser loginUser = new LoginUser();
            loginUser.setId(user.getId());
            loginUser.setBelong(BelongEnum.PLATFORM_USER);

            FileUploadKeyParam fileUploadKeyParam = dicomService.save(addFO, SurgeryStageEnum.PREOP, loginUser);

            // 下载文件
            String downloadTmpDir = fileService.genLocalTmpDownLoadFilePath(
                aliOSSProperties.getTmpDir(),
                fileUploadKeyParam.getAttachmentId(),
                AttachmentTypeEnum.DICOM.getCode()
            );
            String downloadPath = downloadTmpDir + fileName;
            HttpUtil.download(downloadLink, Files.newOutputStream(new File(downloadPath).toPath()), true);

            MailAttachDownloadDTO result = new MailAttachDownloadDTO();
            result.setFileName(fileName);
            result.setTempFilePath(downloadPath);
            result.setOssFilePath(fileUploadKeyParam.getFilePath());
            result.setPltAccount(receiverAccount);
            return result;

        } catch (IOException e) {
            log.error("网易邮件附件下载处理异常", e);
            return null;
        }
    }


}
