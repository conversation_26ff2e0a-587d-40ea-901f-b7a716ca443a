package com.endovas.cps.service.wsmsg.impl;

import cn.hutool.json.JSONUtil;
import com.endovas.cps.pojo.dto.ws.TunnelDTO;
import com.endovas.cps.service.wsmsg.WebSocketMsgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: wk
 * @Date: 2025/1/2
 * @Time: 16:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketMsgServiceImpl implements WebSocketMsgService {

    // 用于存储每个用户客户端对象
    public static Map<String, TunnelDTO> onlineUserMap = new ConcurrentHashMap<>();

    /**
     * 給session连接推送消息
     */
    public void sendMessage(String userId, Object message) {
        try {
            TunnelDTO tunnel = onlineUserMap.get(userId);
            if (Objects.nonNull(tunnel)) {
                tunnel.getSession().getBasicRemote().sendText(JSONUtil.toJsonStr(message));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("向客户端推送数据发生错误", e);
        }
    }

    public void putTunnel(String userId, TunnelDTO tunnelDTO) {
        onlineUserMap.put(userId, tunnelDTO);
    }

    public TunnelDTO gutTunnel(String userId) {
        return onlineUserMap.get(userId);
    }

    public void removeTunnel(String userId) {
        onlineUserMap.remove(userId);
    }

    public int getTunnelSize() {
        return onlineUserMap.size();
    }
}
