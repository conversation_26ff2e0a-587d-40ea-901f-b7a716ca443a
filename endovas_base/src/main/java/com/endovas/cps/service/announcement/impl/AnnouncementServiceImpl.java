package com.endovas.cps.service.announcement.impl;

import com.endovas.cps.dao.announcement.AnnouncementDAO;
import com.endovas.cps.entity.announcement.Announcement;
import com.endovas.cps.pojo.fo.announcement.AnnouncementAddFO;
import com.endovas.cps.pojo.vo.AnnouncementVO;
import com.endovas.cps.service.announcement.AnnouncementService;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnnouncementServiceImpl implements AnnouncementService {
    private final AnnouncementDAO announcementDAO;

    @Override
    public void add(AnnouncementAddFO input) {
        Announcement announcement = new Announcement();
        announcement.setContent(input.getContent());
        announcement.setTitle(input.getTitle());
        announcementDAO.save(announcement);
    }

    @Override
    public AnnouncementVO detail(String id) {
        Announcement announcement = announcementDAO.findById(id).orElseThrow(() -> new BusinessAssertException("公告不存在"));
        AnnouncementVO result = new AnnouncementVO();
        result.convertFrom(announcement);
        return result;
    }

    @Override
    public PageVO<AnnouncementVO> list(PageFO pageFO) {
        Page<AnnouncementVO> result = announcementDAO.findByOrderByCreateTimeDesc(PageUtil.initJPAPage(pageFO)).map(x -> {
            AnnouncementVO one = new AnnouncementVO();
            one.convertFrom(x);
            return one;
        });
        return PageUtil.convert(result);
    }
}
