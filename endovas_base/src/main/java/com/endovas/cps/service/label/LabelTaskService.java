package com.endovas.cps.service.label;

import com.endovas.cps.pojo.fo.label.*;
import com.endovas.cps.pojo.vo.label.LabelTaskInfoVO;
import com.endovas.cps.pojo.vo.label.LabelTaskListVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:40
 */
public interface LabelTaskService {

    /**
     * 添加测量任务
     * @param input
     */
    void add(LabelTaskAddFO input, LoginUser loginUser);

    /**
     * 编辑测量任务
     * @param input
     */
    void edit(LabelTaskEditFO input);

    /**
     * 领取测量任务
     * @param input
     * @param loginUser
     */
    void collect(LabelTaskModifyFO input, LoginUser loginUser);

    /**
     * 放弃测量任务
     * @param input
     * @param loginUser
     */
    void giveUp(LabelTaskModifyFO input, LoginUser loginUser);

    /**
     * 完成测量任务
     * @param input
     * @param loginUser
     */
    void finish(LabelTaskModifyFO input, LoginUser loginUser);

    /**
     * 关闭测量任务
     * @param input
     * @param loginUser
     */
    void close(LabelTaskModifyFO input, LoginUser loginUser);

    /**
     * 重启测量任务
     * @param input
     * @param loginUser
     */
    void reOpen(LabelTaskModifyFO input, LoginUser loginUser);

    /**
     * 获取测量任务池子
     * @param searchFO
     * @param page
     * @return
     */
    PageVO<LabelTaskListVO> listSelfMissionPool(LabelTaskSearchSelfFO searchFO, PageFO page, LoginUser loginUser);

    /**
     * 获取测量任务池子
     * @param searchFO
     * @param page
     * @return
     */
    PageVO<LabelTaskListVO> listMissionPool(LabelTaskSearchFO searchFO, PageFO page, LoginUser loginUser);

    /**
     * 查看测量任务详情
     * @param detailFO
     * @param loginUser
     * @return
     */
    LabelTaskInfoVO detail(LabelTaskDetailFO detailFO, LoginUser loginUser);
}
