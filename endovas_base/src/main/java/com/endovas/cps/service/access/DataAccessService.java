package com.endovas.cps.service.access;

import io.daige.starter.common.database.mybatis.conditions.query.QueryWrap;
import io.daige.starter.common.security.LoginUser;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/8
 * Time: 20:38
 */
public interface DataAccessService {
    List<Predicate> filterDataAuth(Root root, CriteriaBuilder criteriaBuilder, LoginUser loginUser);
    QueryWrap filterDataAuth(QueryWrap query, LoginUser loginUser);
}
