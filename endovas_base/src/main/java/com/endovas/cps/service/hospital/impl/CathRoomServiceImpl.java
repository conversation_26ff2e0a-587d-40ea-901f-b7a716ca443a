package com.endovas.cps.service.hospital.impl;


import com.endovas.cps.dao.hospital.CathRoomDAO;
import com.endovas.cps.dao.hospital.CathRoomTrolleyDAO;
import com.endovas.cps.dao.trolley.TrolleyDAO;
import com.endovas.cps.entity.hospital.CathRoom;
import com.endovas.cps.entity.hospital.CathRoomTrolley;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.TrolleyUseStatusEnum;
import com.endovas.cps.pojo.fo.hospital.CathRoomAddFO;
import com.endovas.cps.pojo.fo.hospital.CathRoomEditFO;
import com.endovas.cps.pojo.vo.AttachmentVO;
import com.endovas.cps.pojo.vo.platform.hospital.CathRoomListVO;
import com.endovas.cps.pojo.vo.platform.hospital.CathRoomSelectVO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.hospital.CathRoomService;
import io.daige.starter.common.exception.BusinessAssertException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/8/14
 * Time: 14:17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CathRoomServiceImpl implements CathRoomService {
    private final CathRoomDAO cathRoomDAO;
    private final AttachmentService attachmentService;
    private final CathRoomTrolleyDAO cathRoomTrolleyDAO;
    private final TrolleyDAO trolleyDAO;


    @Override
    public List<CathRoomSelectVO> canBindSelect(String hospitalId) {
        List<String> cathRoomIds = cathRoomDAO.findByHospitalId(hospitalId).stream().map(CathRoom::getId).collect(Collectors.toList());
        List<String> notBindIds = cathRoomTrolleyDAO.findByCathRoomIdNotInAndHospitalId(cathRoomIds, hospitalId).stream().map(CathRoomTrolley::getCathRoomId).collect(Collectors.toList());
        return cathRoomDAO.findByIdIn(notBindIds).stream().map(x -> {
            CathRoomSelectVO one = new CathRoomSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(CathRoomAddFO input) {
        CathRoom cathRoom = new CathRoom();
        input.convertTo(cathRoom);
        cathRoom.setAvailable(true);
        cathRoomDAO.save(cathRoom);
        attachmentService.updateTmpUrlById(input.getAttachmentIds(), cathRoom.getId(), AttachmentTypeEnum.CATH_ROOM);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void edit(CathRoomEditFO input) {
        CathRoom cathRoom = cathRoomDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("导管室不存在"));
        input.convertTo(cathRoom);
        cathRoomDAO.save(cathRoom);
        attachmentService.updateTmpUrlById(input.getAttachmentIds(), cathRoom.getId(), AttachmentTypeEnum.CATH_ROOM);
    }

    @Override
    public void switchAvailable(String id) {
        CathRoom cathRoom = cathRoomDAO.findById(id).orElseThrow(() -> new BusinessAssertException("导管室不存在"));
        CathRoomTrolley cathRoomTrolley = cathRoomTrolleyDAO.getByCathRoomId(id);
        if (Objects.nonNull(cathRoomTrolley)) {
            throw new BusinessAssertException("导管室绑定了台车,请解除绑定后操作");
        }
        cathRoom.setAvailable(!cathRoom.getAvailable());
        cathRoomDAO.save(cathRoom);
    }

    @Override
    public List<CathRoomListVO> list(String hospitalId) {
        return cathRoomDAO.findByHospitalId(hospitalId).stream().map(x -> {
            CathRoomListVO one = new CathRoomListVO().convertFrom(x);


            CathRoomTrolley cathRoomTrolley = cathRoomTrolleyDAO.getByCathRoomId(x.getId());
            if (Objects.isNull(cathRoomTrolley)) {
                one.setStatus(TrolleyUseStatusEnum.NOT_BIND.getCode());
            } else {
                //todo 处理台车的是否有处于会议状态
                one.setStatus(TrolleyUseStatusEnum.OFFLINE.getCode());
//                Long meetingNum = meetingRoomDAO.countByHospitalIdAndStatus(x.getHospitalId(), MeetingRoomStatusEnum.MEETING.getCode());
//                if (meetingNum > 0) {
//                    one.setStatus(TrolleyUseStatusEnum.MEETING.getCode());
//                } else {
//                    trolleyDAO.findById(cathRoomTrolley.getTrolleyId()).ifPresent(y -> {
//                        one.setStatus(y.getStatus());
//                    });
//                }
            }

            List<AttachmentVO> attachments = attachmentService.findByTargetIdAndType(x.getId(), AttachmentTypeEnum.CATH_ROOM);
            one.setAttachments(attachments);
            return one;
        }).collect(Collectors.toList());


    }
}
