package com.endovas.cps.service.measurement;

import com.endovas.cps.pojo.fo.measurement.MeasurementOperationParamFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationAllocVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationDetailVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationPrepareVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/18
 * @Time: 16:54
 */
public interface MeasurementOperationService {

    /**
     * 尝试分配机器
     * @param searchFO
     * @param loginUser
     * @return
     */
    MeasurementOperationAllocVO tryAllocateEquip(MeasurementOperationParamFO searchFO, LoginUser loginUser);

    /**
     * 查询当前机器使用人
     * @param searchFO
     * @param loginUser
     * @return
     */
    MeasurementOperationAllocVO queryEquipCurrentUser(MeasurementOperationParamFO searchFO, LoginUser loginUser);

    /**
     * 准备测量环境
     * @param input
     * @param loginUser
     */
    MeasurementOperationPrepareVO prepare(MeasurementOperationParamFO input, LoginUser loginUser);

    /**
     * 查询测量操作列表
     * @param searchFO
     * @param login
     * @return
     */
    List<MeasurementOperationListVO> listRecord(MeasurementOperationParamFO searchFO, LoginUser login);

    /**
     * 获取测量记录
     * @param id
     * @return
     */
    MeasurementOperationDetailVO getRecord(String id);
}
