package com.endovas.cps.service.attachment;

import com.endovas.cps.pojo.vo.AliStsTokenVO;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 11:39
 */
public interface FileService {
    void aliOssUploaded(String ossFilePath) throws IOException, ExecutionException, InterruptedException;
    void processMailAttach(String ossFilePath) throws IOException, ExecutionException, InterruptedException;
    String genLocalTmpDownLoadFilePath(String temDir, String fileId, String type);
    AliStsTokenVO getAliStsToken() throws Exception;
    Boolean dicomCanView(String dicomId);
}
