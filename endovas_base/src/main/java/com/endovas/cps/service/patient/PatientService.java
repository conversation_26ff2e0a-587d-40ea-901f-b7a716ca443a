package com.endovas.cps.service.patient;

import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.fo.patient.MedicalHisEditFO;
import com.endovas.cps.pojo.fo.patient.PatientAddFO;
import com.endovas.cps.pojo.fo.patient.PatientEditFO;
import com.endovas.cps.pojo.fo.patient.PatientSearchFO;
import com.endovas.cps.pojo.vo.patient.LabTestReportVO;
import com.endovas.cps.pojo.vo.patient.PatientDetailVO;
import com.endovas.cps.pojo.vo.patient.PatientListVO;
import com.endovas.cps.pojo.vo.patient.PatientSelectVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:39
 */
public interface PatientService {
    PageVO<PatientListVO> list(PatientSearchFO input, PageFO page,LoginUser loginUser);
    String add(PatientAddFO input, LoginUser loginUser);
    void edit(PatientEditFO input);
    void del(String id);
    PatientDetailVO detail(String id,LoginUser loginUser);

    void editMedicalHis(MedicalHisEditFO input);

    List<PatientSelectVO> select(LoginUser loginUser);
    void addLabTestReport(String patientId, AttachmentTypeEnum type, String attachmentId);
    LabTestReportVO labTestReport(String patientId);

}
