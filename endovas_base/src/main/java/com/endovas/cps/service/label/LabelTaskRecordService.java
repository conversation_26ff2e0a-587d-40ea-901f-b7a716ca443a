package com.endovas.cps.service.label;

import com.endovas.cps.entity.label.LabelTask;
import com.endovas.cps.pojo.fo.label.LabelTaskRecordSearchFO;
import com.endovas.cps.pojo.vo.label.LabelTaskRecordListVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:48
 */
public interface LabelTaskRecordService {
    /**
     * 获取测量任务日志列表
     * @param searchFO
     * @param loginUser
     * @return
     */
    List<LabelTaskRecordListVO> list(LabelTaskRecordSearchFO searchFO, LoginUser loginUser);

    /**
     * 创建测量任务日志
     * @param beforePO
     * @param afterPO
     * @param opContent
     * @param opUserId
     * @param opUserBelong
     */
    void add(LabelTask beforePO, LabelTask afterPO, String opContent, String opUserId, String opUserBelong);
}
