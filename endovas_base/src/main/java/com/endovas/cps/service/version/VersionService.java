package com.endovas.cps.service.version;

import com.endovas.cps.pojo.fo.version.VersionAddFO;
import com.endovas.cps.pojo.fo.version.VersionEditFO;
import com.endovas.cps.pojo.vo.version.VersionListVO;
import com.endovas.cps.pojo.vo.version.VersionSelectVO;
import com.endovas.cps.pojo.vo.version.VersionUpdateStrategyVO;
import com.endovas.cps.pojo.vo.version.VersionUpdateVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:09
 */
public interface VersionService {
    PageVO<VersionListVO> list(PageFO pageFO);

    List<VersionSelectVO> select(String terminalType, String os);

    void add(VersionAddFO input);

    void edit(VersionEditFO input);

    void switchAvailable(String id);

    List<VersionUpdateStrategyVO> listStrategy(String versionId);

    VersionUpdateVO checkUpdate(String terminalType, String os, String currentVersion);
}
