package com.endovas.cps.service.stage.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.endovas.cps.pojo.dto.meeting.VideoDeviceDTO;
import com.endovas.cps.pojo.dto.product.ProductListDTO;
import com.endovas.cps.pojo.vo.AttachmentVO;
import com.endovas.cps.pojo.vo.stage.IntraopVO;
import com.endovas.cps.service.stage.SurgeryIntraopService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/6
 * Time: 14:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SurgeryIntraopServiceImpl implements SurgeryIntraopService {


    @Override
    public IntraopVO detail(String patientId) {
        //todo 实现逻辑
        IntraopVO intraopVO = new IntraopVO();
        intraopVO.setSurgeryDate("2024-10-31");
        intraopVO.setSurgeryStartTime(DateUtil.parse("2024-10-31 14:30", DatePattern.NORM_DATETIME_FORMAT).toLocalDateTime());
        intraopVO.setSurgeryStartTime(DateUtil.parse("2024-10-31 15:30", DatePattern.NORM_DATETIME_FORMAT).toLocalDateTime());
        intraopVO.setSameSurgeryTypeSameHospitalUserAmt(29L);

        intraopVO.setSameSurgeryTypeSameHospitalUserUsedTime(50L);
        intraopVO.setSameSurgeryTypeSameHospitalUsedTime(18L);
        intraopVO.setSameSurgeryTypeUsedTime(16L);


        List<ProductListDTO> products = Lists.newArrayList();
        ProductListDTO one = new ProductListDTO();
        one.setSpec("C322610-2003010");
        one.setName("Castor®分支型主动脉覆膜支架及输送系统");
        AttachmentVO oneAttachment = new AttachmentVO();
        oneAttachment.setUrl("http://dev.endovas.microfissiondata.cn:9090/cps/api/v1/base/attachment/view?file=attachment/8e4b3c1ed4a649d9a184e89ba482bdef.png");
        one.setAttachment(oneAttachment);
        products.add(one);
        ProductListDTO two = new ProductListDTO();
        two.setSpec("L12312312321");
        two.setName("L-REBOA®主动脉阻断球囊导管");
        AttachmentVO twoAttachment = new AttachmentVO();
        twoAttachment.setUrl("http://dev.endovas.microfissiondata.cn:9090/cps/api/v1/base/attachment/view?file=attachment/8f6729d4f086456f8300b5b52f4a539d.png");
        two.setAttachment(twoAttachment);
        products.add(two);
        intraopVO.setProducts(products);

        List<VideoDeviceDTO> videos = Lists.newArrayList();
        VideoDeviceDTO oneVideo = new VideoDeviceDTO();
        oneVideo.setDeviceId("1111");
        oneVideo.setDeviceName("DSA");
        oneVideo.setTime(8580L);
        AttachmentVO oneCover = new AttachmentVO();
        oneCover.setUrl("http://dev.endovas.microfissiondata.cn:9090/cps/api/v1/base/attachment/view?file=attachment/bdcae869c4c6433099505453f0f55606.png");
        oneVideo.setCover(oneCover);
        AttachmentVO oneVideoAttachment = new AttachmentVO();
        oneVideoAttachment.setUrl("http://192.168.1.250:9443/api/public/dl/2I-dHM44?inline=true");
        oneVideo.setAttachments(Lists.newArrayList(oneVideoAttachment));
        videos.add(oneVideo);
        intraopVO.setVideos(videos);
        return intraopVO;
    }
}
