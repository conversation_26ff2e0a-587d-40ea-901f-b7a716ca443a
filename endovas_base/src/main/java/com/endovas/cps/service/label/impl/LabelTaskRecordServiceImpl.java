package com.endovas.cps.service.label.impl;

import com.endovas.cps.dao.label.LabelTaskRecordDAO;
import com.endovas.cps.entity.label.LabelTask;
import com.endovas.cps.entity.label.LabelTaskRecord;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.label.LabelTaskRecordSearchFO;
import com.endovas.cps.pojo.vo.label.LabelTaskRecordListVO;
import com.endovas.cps.service.label.LabelTaskRecordService;
import com.endovas.cps.service.user.CreatorTypeService;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelTaskRecordServiceImpl implements LabelTaskRecordService {

    private final LabelTaskRecordDAO labelTaskRecordDAO;
    private final CreatorTypeService creatorTypeService;

    @Override
    public List<LabelTaskRecordListVO> list(LabelTaskRecordSearchFO searchFO, LoginUser loginUser) {
        List<LabelTaskRecord> poList = labelTaskRecordDAO.findByLabelTaskIdOrderByCreateTimeDesc(searchFO.getLabelTaskId());

        List<LabelTaskRecordListVO> resultList = poList.stream().map(po -> {
            LabelTaskRecordListVO vo = new LabelTaskRecordListVO();
            vo.convertFrom(po);

            // 发起人
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(po.getOperaterBelong()), po.getOperaterId());
            vo.setOperater(creatorTypeDTO.getCreator());
            vo.setOperaterType(creatorTypeDTO.getCreatorType());

            return vo;
        }).collect(Collectors.toList());

        return resultList;
    }

    @Override
    public void add(LabelTask beforePO, LabelTask afterPO, String opContent, String opUserId, String opUserBelong) {
        LabelTaskRecord po = new LabelTaskRecord();
        po.setLabelTaskId(afterPO.getId());

        if (Objects.nonNull(beforePO)) {
            po.setBeforeTaskStatus(beforePO.getTaskStatus());
        }

        po.setAfterTaskStatus(afterPO.getTaskStatus());

        po.setContent(opContent);
        po.setOperaterId(opUserId);
        po.setOperaterBelong(opUserBelong);

        labelTaskRecordDAO.save(po);
    }
}
