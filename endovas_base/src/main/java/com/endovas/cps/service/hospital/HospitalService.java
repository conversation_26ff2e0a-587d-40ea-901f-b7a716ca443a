package com.endovas.cps.service.hospital;

import com.endovas.cps.pojo.fo.hospital.HospitalAddFO;
import com.endovas.cps.pojo.fo.hospital.HospitalEditFO;
import com.endovas.cps.pojo.fo.hospital.HospitalSearchFO;
import com.endovas.cps.pojo.vo.EnterpriseSelectVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalListVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalSelectVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:38
 */
public interface HospitalService {
    PageVO<HospitalListVO> list(HospitalSearchFO searchFO, PageFO pageFO);
    void add(HospitalAddFO input);

    void edit(HospitalEditFO input);

    void del(String id);
    List<HospitalSelectVO> select();
    List<EnterpriseSelectVO> select(String name);
    List<HospitalSelectVO> selectByTrolleyId(String trolleyId);

}
