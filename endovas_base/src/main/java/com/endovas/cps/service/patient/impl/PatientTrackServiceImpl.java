package com.endovas.cps.service.patient.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.patient.PatientTrackDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.patient.PatientTrack;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackAddFO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackEditFO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackSearchFO;
import com.endovas.cps.pojo.vo.patient.track.PatientTrackListVO;
import com.endovas.cps.service.access.DataAccessService;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.patient.PatientTrackService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.NameHiddenUtil;
import com.google.common.collect.Lists;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/4
 * Time: 14:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatientTrackServiceImpl implements PatientTrackService {
    private final PatientTrackDAO patientTrackDAO;
    private final PatientDAO patientDAO;
    private final SurgeryTypeDAO surgeryTypeDAO;
    private final SurgeryTypeService surgeryTypeService;

    private final AttachmentService attachmentService;
    private final CreatorTypeService creatorTypeService;
    private final DataAccessService dataAccessService;

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(PatientTrackAddFO input, LoginUser loginUser) {
        PatientTrack patientTrack = new PatientTrack();
        input.convertTo(patientTrack);
        patientTrack.setCreatorBelong(loginUser.getBelong().getCode());
        patientTrack.setCreatorName(loginUser.getNickName());
        patientTrackDAO.save(patientTrack);
        attachmentService.updateTmpUrlById(input.getAttachmentId(), patientTrack.getId(), AttachmentTypeEnum.TRACK);
    }

    @Override
    public void edit(PatientTrackEditFO input) {
        PatientTrack patientTrack = patientTrackDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("随访不存在"));
        input.convertTo(patientTrack);
        patientTrackDAO.save(patientTrack);
        attachmentService.updateTmpUrlById(input.getAttachmentId(), patientTrack.getId(), AttachmentTypeEnum.TRACK);
    }

    @Override
    public PageVO<PatientTrackListVO> list(PatientTrackSearchFO search, PageFO pageFO, LoginUser loginUser) {
        Specification<PatientTrack> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = dataAccessService.filterDataAuth(root, criteriaBuilder, loginUser);

            List<String> patientIds = Lists.newArrayList();
            if (StrUtil.isNotEmpty(search.getTrackTime())) {
                String startTime = search.getTrackTime() + " 00:00:00";
                String endTime = search.getTrackTime() + " 23:59:59";
                Predicate p1 = criteriaBuilder.between(root.get(PatientTrack.TRACK_TIME), startTime, endTime);
                list.add(p1);
            }


            if (StrUtil.isNotEmpty(search.getCreatorName())) {
                Predicate p1 = criteriaBuilder.like(root.get(PatientTrack.CREATOR_NAME), "%" + search.getCreatorName() + "%");
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(search.getSurgeryTypeId())) {
                List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(search.getSurgeryTypeId());
                patientIds.addAll(patientDAO.findBySurgeryTypeIdIn(allSurgeryTypeIds).stream().map(MysqlBase::getId).collect(Collectors.toList()));
            }
            if (StrUtil.isNotEmpty(search.getPatientName())) {
                patientIds.addAll(patientDAO.findByNameContaining(search.getPatientName()).stream().map(MysqlBase::getId).collect(Collectors.toList()));
            }
            //todo =========逻辑完善==============
            if (StrUtil.isNotEmpty(search.getSurgeryDate())) {

            }
            //todo =========逻辑完善==============

            if (StrUtil.isNotEmpty(search.getTrackMethod())) {
                Predicate p1 = criteriaBuilder.equal(root.get(PatientTrack.TRACK_METHOD), search.getTrackMethod());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(search.getTrackObject())) {
                Predicate p1 = criteriaBuilder.equal(root.get(PatientTrack.TRACK_OBJECT), search.getTrackObject());
                list.add(p1);
            }

            if (CollectionUtil.isNotEmpty(patientIds)) {
                CriteriaBuilder.In<String> inClause = criteriaBuilder.in(root.get(PatientTrack.PATIENT_ID));
                CollectionUtil.distinct(patientIds).forEach(inClause::value);
                list.add(inClause);
            }

            criteriaQuery.where(criteriaBuilder.and(list.toArray(new Predicate[list.size()])));
            criteriaQuery.orderBy(criteriaBuilder.desc(root.get(PatientTrack.CREATE_TIME)));
            return criteriaQuery.getRestriction();
        };
        pageFO.setSort(PatientTrack.CREATE_TIME);
        Page<PatientTrackListVO> list = patientTrackDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> convert(x, loginUser));
        return PageUtil.convert(list);


    }

    @Override
    public List<PatientTrackListVO> listByPatientId(String patientId, LoginUser loginUser) {
        return patientTrackDAO.findByPatientId(patientId).stream().map(x -> convert(x, loginUser)).collect(Collectors.toList());
    }


    private PatientTrackListVO convert(PatientTrack x, LoginUser loginUser) {
        PatientTrackListVO one = new PatientTrackListVO().convertFrom(x);
        CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(x.getCreatorBelong()), x.getCreatorId());
        one.setCreator(creatorTypeDTO.getCreator());
        one.setCreatorType(creatorTypeDTO.getCreatorType());

        patientDAO.findById(x.getPatientId()).ifPresent(y -> {
            one.setPatientName(NameHiddenUtil.hidden(y.getName()));
            surgeryTypeDAO.findById(y.getSurgeryTypeId()).ifPresent(z -> {
                one.setSurgeryTypeName(z.getName());
            });
        });

        //todo =========逻辑完善==============
        one.setSurgeryDate("2022-10-08 19:13");
        //todo =========逻辑完善==============
        one.setAttachment(attachmentService.getByTargetIdAndType(x.getId(), AttachmentTypeEnum.TRACK));
        return one;
    }
}
