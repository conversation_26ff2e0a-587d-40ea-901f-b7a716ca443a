package com.endovas.cps.service.label.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.constant.LabelTaskRecordConst;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.label.LabelTaskDAO;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.label.LabelTask;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.LabelStatusEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.label.*;
import com.endovas.cps.pojo.vo.label.LabelTaskInfoVO;
import com.endovas.cps.pojo.vo.label.LabelTaskListVO;
import com.endovas.cps.service.access.DataAccessService;
import com.endovas.cps.service.label.LabelTaskRecordService;
import com.endovas.cps.service.label.LabelTaskService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.NameHiddenUtil;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.DateUtil;
import io.daige.starter.common.utils.PageUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelTaskServiceImpl implements LabelTaskService {

    private final LabelTaskDAO labelTaskDAO;
    private final RedisHelper redisHelper;
    private final DicomDAO dicomDAO;
    private final SurgeryTypeDAO surgeryTypeDAO;
    private final SurgeryTypeService surgeryTypeService;

    private final PlatformUserDAO platformUserDAO;
    private final HospitalDAO hospitalDAO;
    private final PatientDAO patientDAO;
    private final DataAccessService dataAccessService;

    private final CreatorTypeService creatorTypeService;
    private final LabelTaskRecordService labelTaskRecordService;

    /**
     * 获取自己测量任务池子（隔离非必要参数）
     *
     * @param searchFO
     * @param page
     * @return
     */
    @Override
    public PageVO<LabelTaskListVO> listSelfMissionPool(LabelTaskSearchSelfFO searchFO, PageFO page, LoginUser loginUser) {
        LabelTaskSearchFO fo = new LabelTaskSearchFO();
        BeanUtil.copyProperties(searchFO, fo);
        fo.setLabeler(loginUser.getNickName());
        return listMissionPool(fo, page, loginUser);
    }

    @Override
    public PageVO<LabelTaskListVO> listMissionPool(LabelTaskSearchFO searchFO, PageFO page, LoginUser loginUser) {
        Specification<LabelTask> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = dataAccessService.filterDataAuth(root, criteriaBuilder, loginUser);

            // 任务id
            if (StrUtil.isNotBlank(searchFO.getLabelTaskId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.LABEL_TASK_ID), searchFO.getLabelTaskId());
                list.add(p1);
            }

            // 疾病类型id
            if (StrUtil.isNotBlank(searchFO.getSurgeryTypeId())) {
                List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(searchFO.getSurgeryTypeId());
                Predicate p1 = criteriaBuilder.in(root.get(LabelTask.SURGERY_TYPE_ID)).value(allSurgeryTypeIds);
                list.add(p1);
            }

            // 发起人
            if (StrUtil.isNotBlank(searchFO.getCreator())) {

                List<PlatformUser> userList = platformUserDAO.findByNickNameContaining(searchFO.getCreator());
                if (CollectionUtil.isNotEmpty(userList)) {
                    List<String> userIdList = userList.stream().map(PlatformUser::getId).collect(Collectors.toList());
                    Predicate p1 = criteriaBuilder.in(root.get(LabelTask.CREATOR_ID)).value(userIdList);
                    list.add(p1);
                } else {
                    Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.CREATOR_ID), searchFO.getCreator());
                    list.add(p1);
                }
            }

            // 领取人
            if (StrUtil.isNotBlank(searchFO.getLabeler())) {

                List<PlatformUser> userList = platformUserDAO.findByNickNameContaining(searchFO.getLabeler());
                if (CollectionUtil.isNotEmpty(userList)) {
                    List<String> userIdList = userList.stream().map(PlatformUser::getId).collect(Collectors.toList());
                    Predicate p1 = criteriaBuilder.in(root.get(LabelTask.LABELER_ID)).value(userIdList);
                    list.add(p1);
                } else {
                    Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.LABELER_ID), searchFO.getLabeler());
                    list.add(p1);
                }
            }

            // 任务状态
            if (StrUtil.isNotBlank(searchFO.getTaskStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.TASK_STATUS), searchFO.getTaskStatus());
                list.add(p1);
            }

            // 医院名称
            if (StrUtil.isNotBlank(searchFO.getHospitalId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.HOSPITAL_ID), searchFO.getHospitalId());
                list.add(p1);
            }

            // 患者姓名
            if (StrUtil.isNotBlank(searchFO.getPatientName())) {
                Predicate p1 = criteriaBuilder.like(root.get(LabelTask.PATIENT_NAME), "%" + searchFO.getPatientName() + "%");
                list.add(p1);
            }

            // 患者id
            if (StrUtil.isNotBlank(searchFO.getPatientId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.PATIENT_ID), searchFO.getPatientId());
                list.add(p1);
            }

            // 影像名称
            if (StrUtil.isNotBlank(searchFO.getDicomName())) {
                Predicate p1 = criteriaBuilder.equal(root.get(LabelTask.DICOM_NAME), searchFO.getDicomName());
                list.add(p1);
            }

            // 创建时间
            if (StrUtil.isNotBlank(searchFO.getCreateStartDate()) && StrUtil.isNotBlank(searchFO.getCreateEndDate())) {
                LocalDateTime startDate = DateUtil.getStartTime(searchFO.getCreateStartDate());
                LocalDateTime endDate = DateUtil.getEndTime(searchFO.getCreateEndDate());
                Predicate p1 = criteriaBuilder.between(root.get(LabelTask.CREATE_TIME), startDate, endDate);
                list.add(p1);
            }

            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };


        Page<LabelTaskListVO> list = labelTaskDAO.findAll(specification, PageUtil.initJPAPage(page)).map(x -> {
            LabelTaskListVO one = new LabelTaskListVO();
            one.convertFrom(x);
            one.setPatientName(NameHiddenUtil.hidden(x.getPatientName()));

            // 发起人
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(x.getCreatorBelong()), x.getCreatorId());
            one.setCreator(creatorTypeDTO.getCreator());
            one.setCreatorType(creatorTypeDTO.getCreatorType());
            if (StringUtils.isNotBlank(x.getLabelerId())) {
                // 领取人
                CreatorTypeDTO measurerTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(x.getLabelerBelong()), x.getLabelerId());
                one.setLabeler(measurerTypeDTO.getCreator());
                one.setLabelerType(measurerTypeDTO.getCreatorType());
            }

            hospitalDAO.findById(x.getHospitalId()).ifPresent(h -> one.setHospitalName(h.getName()));

            // 手术类型名称
            if (StrUtil.isNotEmpty(x.getSurgeryTypeId())) {
                surgeryTypeDAO.findById(x.getSurgeryTypeId()).ifPresent(s -> one.setSurgeryTypeName(s.getName()));
            }

            return one;
        });
        return PageUtil.convert(list);
    }


    @Override
    public LabelTaskInfoVO detail(LabelTaskDetailFO detailFO, LoginUser loginUser) {

        LabelTask po = labelTaskDAO.findById(detailFO.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));

        LabelTaskInfoVO vo = new LabelTaskInfoVO();
        vo.convertFrom(po);
        // 发起人
        CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(po.getCreatorBelong()), po.getCreatorId());
        vo.setCreator(creatorTypeDTO.getCreator());
        vo.setCreatorType(creatorTypeDTO.getCreatorType());

        // 领取人
        if (StringUtils.isNotBlank(po.getLabelerId())) {
            CreatorTypeDTO measurerTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(po.getLabelerBelong()), po.getLabelerId());
            vo.setLabeler(measurerTypeDTO.getCreator());
            vo.setLabelerType(measurerTypeDTO.getCreatorType());
        }


        /**
         * 补充影像文件信息
         */
        Dicom dicomPO = dicomDAO.findById(po.getDicomId()).orElseThrow(() -> new BusinessAssertException("未查询到影像信息"));
        vo.setDicomName(dicomPO.getName());
        vo.setDicomUploadDateTime(dicomPO.getCreateTime());
        vo.setViewJsonUrl(dicomPO.getViewJsonUrl());
        // 影像文件上传人
        CreatorTypeDTO dicomUploaderTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(dicomPO.getCreatorBelong()), dicomPO.getCreatorId());
        vo.setDicomUploader(dicomUploaderTypeDTO.getCreator());
        vo.setDicomUploaderType(dicomUploaderTypeDTO.getCreatorType());

        // 手术类型名称
        if (StrUtil.isNotEmpty(po.getSurgeryTypeId())) {
            surgeryTypeDAO.findById(po.getSurgeryTypeId()).ifPresent(s -> vo.setSurgeryTypeName(s.getNameWithParent()));
        }

        return vo;
    }


    /**
     * 添加测量任务
     *
     * @param input
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(LabelTaskAddFO input, LoginUser loginUser) {
        // 生成id
        LabelTask taskPO = new LabelTask();
        taskPO.setLabelTaskId(getTaskId());
        // 设置状态
        taskPO.setTaskStatus(LabelStatusEnum.TASKSTATUS_UNCLAIMED.getCode());
        taskPO.setMedAgentId(loginUser.getMedAgentId());

        // 设置默认参数
        taskPO.setCreatorBelong(loginUser.getBelong().getCode());

        // 保存参数信息
        input.convertTo(taskPO);

        /**
         * 查询关联信息
         */
        // 影像类型
        Dicom dicomPO = dicomDAO.findById(taskPO.getDicomId()).orElseThrow(() -> new BusinessAssertException("未查询到影像信息"));
        dicomPO.setPatientId(input.getPatientId());
        dicomDAO.save(dicomPO);
        taskPO.setModality(dicomPO.getModality());
        taskPO.setDicomName(dicomPO.getName());
        taskPO.setDicomContentDate(dicomPO.getContentDate());

        // 患者姓名
        patientDAO.findById(taskPO.getPatientId()).ifPresent(p -> taskPO.setPatientName(p.getName()));

        // 保存记录
        labelTaskDAO.save(taskPO);

        // 保存日志
        labelTaskRecordService.add(null, taskPO, LabelTaskRecordConst.CREATE, taskPO.getCreatorId(), taskPO.getCreatorBelong());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(LabelTaskEditFO input) {
        LabelTask po = labelTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        po.setDataRangeType(input.getDataRangeType());

        labelTaskDAO.save(po);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collect(LabelTaskModifyFO input, LoginUser loginUser) {
        LabelTask po = labelTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        LabelTask beforePO = new LabelTask();
        BeanUtil.copyProperties(po, beforePO);

        // 领取人信息更新
        po.setLabelerId(loginUser.getId());
        po.setLabelerBelong(loginUser.getBelong().getCode());

        // 状态更新
        po.setTaskStatus(LabelStatusEnum.TASKSTATUS_ALREADYCLAIMED.getCode());

        labelTaskDAO.save(po);

        // 保存日志
        labelTaskRecordService.add(beforePO, po, LabelTaskRecordConst.COLLECT, loginUser.getId(), loginUser.getBelong().getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giveUp(LabelTaskModifyFO input, LoginUser loginUser) {
        LabelTask po = labelTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        LabelTask beforePO = new LabelTask();
        BeanUtil.copyProperties(po, beforePO);

        // 领取人信息更新
        po.setLabelerId("");
        po.setLabelerBelong("");

        // 状态更新
        po.setTaskStatus(LabelStatusEnum.TASKSTATUS_UNCLAIMED.getCode());

        labelTaskDAO.save(po);

        // 保存日志
        labelTaskRecordService.add(beforePO, po, LabelTaskRecordConst.GIVE_UP, loginUser.getId(), loginUser.getBelong().getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finish(LabelTaskModifyFO input, LoginUser loginUser) {
        LabelTask po = labelTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        LabelTask beforePO = new LabelTask();
        BeanUtil.copyProperties(po, beforePO);

        // 状态更新
        po.setTaskStatus(LabelStatusEnum.TASKSTATUS_COMPLETED.getCode());
        po.setTaskCompletedTime(LocalDateTime.now());
        labelTaskDAO.save(po);

        // 保存日志
        labelTaskRecordService.add(beforePO, po, LabelTaskRecordConst.COMPLETED, loginUser.getId(), loginUser.getBelong().getCode());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(LabelTaskModifyFO input, LoginUser loginUser) {
        LabelTask po = labelTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        LabelTask beforePO = new LabelTask();
        BeanUtil.copyProperties(po, beforePO);

        po.setTaskStatus(LabelStatusEnum.TASKSTATUS_CLOSED.getCode());

        labelTaskDAO.save(po);
        // 保存日志
        labelTaskRecordService.add(beforePO, po, LabelTaskRecordConst.CLOSE, loginUser.getId(), loginUser.getBelong().getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reOpen(LabelTaskModifyFO input, LoginUser loginUser) {
        LabelTask po = labelTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        LabelTask beforePO = new LabelTask();
        BeanUtil.copyProperties(po, beforePO);

        po.setTaskStatus(LabelStatusEnum.TASKSTATUS_UNCLAIMED.getCode());

        labelTaskDAO.save(po);
        // 保存日志
        labelTaskRecordService.add(beforePO, po, LabelTaskRecordConst.REOPEN, loginUser.getId(), loginUser.getBelong().getCode());
    }


    /**
     * 生成测量任务id
     *
     * @return
     */
    private String getTaskId() {
        String dateStr = DateUtil.format(LocalDate.now(), DatePattern.PURE_DATE_PATTERN);
        String task_seq = NumberUtil.decimalFormat("0000", redisHelper.strIncrement(RedisCacheKeys.getLabelTaskSeqKey(dateStr), 1L));

        StringBuilder sb = new StringBuilder("BB-");
        sb.append(dateStr);
        sb.append(task_seq);
        return sb.toString();
    }
}
