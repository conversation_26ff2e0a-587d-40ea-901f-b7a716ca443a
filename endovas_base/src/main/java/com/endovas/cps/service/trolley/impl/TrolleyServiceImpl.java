package com.endovas.cps.service.trolley.impl;

import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.MedAgentUserDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.hospital.CathRoomDAO;
import com.endovas.cps.dao.hospital.CathRoomTrolleyDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.hospital.HospitalMedAgentDAO;
import com.endovas.cps.dao.trolley.TrolleyDAO;
import com.endovas.cps.dao.trolley.TrolleyTransferDAO;
import com.endovas.cps.entity.hospital.CathRoomTrolley;
import com.endovas.cps.entity.hospital.HospitalMedAgent;
import com.endovas.cps.entity.trolley.Trolley;
import com.endovas.cps.entity.trolley.TrolleyTransfer;
import com.endovas.cps.entity.user.MedAgentUser;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.TrolleyStatusEnum;
import com.endovas.cps.enums.TrolleyUseStatusEnum;
import com.endovas.cps.pojo.fo.trolley.TrolleyAddFO;
import com.endovas.cps.pojo.fo.trolley.TrolleyEditFO;
import com.endovas.cps.pojo.fo.trolley.TrolleySearchFO;
import com.endovas.cps.pojo.fo.trolley.TrolleyTransferFO;
import com.endovas.cps.pojo.vo.trolley.TrolleyListVO;
import com.endovas.cps.pojo.vo.trolley.TrolleySelectVO;
import com.endovas.cps.pojo.vo.trolley.TrolleyTransferVO;
import com.endovas.cps.service.trolley.TrolleyService;
import com.google.common.collect.Lists;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrolleyServiceImpl implements TrolleyService {
    private final TrolleyDAO trolleyDAO;
    private final HospitalDAO hospitalDAO;
    private final HospitalMedAgentDAO hospitalMedAgentDAO;
    private final TrolleyTransferDAO trolleyTransferDAO;
    private final PlatformUserDAO platformUserDAO;
    private final MedAgentUserDAO medAgentUserDAO;

    private final CathRoomDAO cathRoomDAO;
    private final CathRoomTrolleyDAO cathRoomTrolleyDAO;


    @Override
    public PageVO<TrolleyListVO> list(TrolleySearchFO searchFO, PageFO pageFO) {
        return query(StrUtil.EMPTY, searchFO, pageFO);
    }

    @Override
    public PageVO<TrolleyListVO> list4MedAgent(String medAgentId, TrolleySearchFO searchFO, PageFO pageFO) {
        return query(medAgentId, searchFO, pageFO);
    }

    private PageVO<TrolleyListVO> query(String medAgentId, TrolleySearchFO searchFO, PageFO pageFO) {
        Specification<Trolley> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StrUtil.isNotEmpty(medAgentId)) {
                Predicate p1 = criteriaBuilder.equal(root.get(Trolley.MED_AGENT_ID), medAgentId);
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getProductName())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Trolley.PRODUCT_NAME), searchFO.getProductName());
                list.add(p1);
            }

            if (StrUtil.isNotEmpty(searchFO.getSoftVer())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Trolley.SOFT_VER), searchFO.getSoftVer());
                list.add(p1);
            }
            if (StrUtil.isNotEmpty(searchFO.getStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Trolley.STATUS), searchFO.getStatus());
                list.add(p1);
            }
            if (StrUtil.isNotEmpty(searchFO.getSpecName())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Trolley.SPEC_NAME), searchFO.getSpecName());
                list.add(p1);
            }
            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };
        Page<TrolleyListVO> list = trolleyDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> {
            TrolleyListVO one = new TrolleyListVO().convertFrom(x);
            //todo 处理台车的是否有处于会议状态
            one.setUseStatus(TrolleyUseStatusEnum.OFFLINE.getCode());
//            Long meetingNum = meetingRoomDAO.countByHospitalIdAndStatus(x.getHospitalId(), MeetingRoomStatusEnum.MEETING.getCode());
//            if (meetingNum > 0) {
//                one.setUseStatus(TrolleyUseStatusEnum.MEETING.getCode());
//            } else {
//                one.setUseStatus(x.getUseStatus());
//            }
            if (StrUtil.isNotEmpty(x.getHospitalId())) {
                hospitalDAO.findById(x.getHospitalId()).ifPresent(hospital -> {
                    one.setBindHospitalName(hospital.getName());
                });
            }
            return one;
        });
        return PageUtil.convert(list);
    }

    @Override
    public List<TrolleyListVO> listByHospitalId(String hospitalId) {
        List<String> trolleyIds = trolleyDAO.findByHospitalId(hospitalId).stream().map(MysqlBase::getId).collect(Collectors.toList());
        TrolleyTransfer existTransferLog = trolleyTransferDAO.getByTransferFromHospitalId(hospitalId);
        if (Objects.nonNull(existTransferLog)) {
            trolleyIds.add(existTransferLog.getTrolleyId());
        }
        return trolleyDAO.findByIdIn(trolleyIds).stream().map(x -> {
            TrolleyListVO one = new TrolleyListVO().convertFrom(x);
            TrolleyTransfer trolleyTransfer = trolleyTransferDAO.getByTrolleyIdAndTransferFromHospitalId(x.getId(), hospitalId);
            if (Objects.nonNull(trolleyTransfer)) {
                one.setHasTransferLog(!trolleyTransfer.getTransferToHospitalId().equals(trolleyTransfer.getTransferFromHospitalId()));
            } else {
                one.setHasTransferLog(false);
            }


            //添加导管室名称
            CathRoomTrolley cathRoomTrolley = cathRoomTrolleyDAO.getByTrolleyIdAndHospitalId(x.getId(), hospitalId);
            if (Objects.nonNull(cathRoomTrolley)) {
                cathRoomDAO.findById(cathRoomTrolley.getCathRoomId()).ifPresent(y -> {
                    one.setCathRoomName(y.getName());
                });
            }

            return one;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void bindHospital(String hospitalId, String udi, String cathRoomId, String installedDate) {
        Trolley trolley = trolleyDAO.getByUdi(udi);
        if (Objects.isNull(trolley)) {
            throw new BusinessAssertException("绑定的UDI不存在");
        }
        if (StrUtil.isNotEmpty(trolley.getHospitalId())) {
            throw new BusinessAssertException("填写的UDI已绑定医院");
        }

        //绑定台车和导管室的关系
        if (StrUtil.isNotEmpty(cathRoomId)) {
            CathRoomTrolley cathRoomTrolley = cathRoomTrolleyDAO.getByTrolleyIdAndCathRoomId(trolley.getId(), cathRoomId);
            if (Objects.isNull(cathRoomTrolley)) {
                cathRoomTrolley = new CathRoomTrolley();
                cathRoomTrolley.setHospitalId(hospitalId);
                cathRoomTrolley.setCathRoomId(cathRoomId);
                cathRoomTrolley.setTrolleyId(trolley.getId());
                cathRoomTrolleyDAO.save(cathRoomTrolley);
            }
        }

        trolley.setHospitalId(hospitalId);
        trolley.setStatus(TrolleyStatusEnum.INSTALLED.getCode());
        trolley.setInstalledDate(installedDate);
        trolleyDAO.save(trolley);
    }

    @Override
    public List<TrolleySelectVO> canBindTrolleyByHospitalId(String hospitalId) {
        HospitalMedAgent hospitalMedAgent = hospitalMedAgentDAO.getByHospitalId(hospitalId);
        if (Objects.isNull(hospitalMedAgent)) {
            return Lists.newArrayList();
        }
        return canBindTrolleyByMedAgentId(hospitalMedAgent.getMedAgentId());

    }

    @Override
    public List<TrolleySelectVO> canBindTrolleyByMedAgentId(String medAgentId) {
        return trolleyDAO.findByMedAgentIdAndStatus(medAgentId, TrolleyStatusEnum.BOUGHT.getCode()).stream().map(x -> {
            TrolleySelectVO one = new TrolleySelectVO();
            one.setId(x.getId());
            one.setUdi(x.getUdi());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public void add(TrolleyAddFO input) {
        Trolley trolley = new Trolley();
        Trolley existTrolley = trolleyDAO.getByUdi(input.getUdi());
        if (Objects.nonNull(existTrolley)) {
            throw new BusinessAssertException("已经存在相同的UDI设备");
        }

        input.convertTo(trolley);
        trolley.setStatus(TrolleyStatusEnum.SPOTS.getCode());
        trolley.setUseStatus(TrolleyUseStatusEnum.OFFLINE.getCode());
        trolleyDAO.save(trolley);
    }

    @Override
    public void assignTrolley(String medAgentId, String udi, String installedDate) {
        Trolley trolley = trolleyDAO.getByUdi(udi);
        if (Objects.isNull(trolley)) {
            throw new BusinessAssertException("输入的UDI不正确");
        }
        if (StrUtil.isNotEmpty(trolley.getMedAgentId())) {
            throw new BusinessAssertException("输入的UDI已被采购");
        }
        trolley.setMedAgentId(medAgentId);
        trolley.setInstalledDate(installedDate);
        trolley.setStatus(TrolleyStatusEnum.BOUGHT.getCode());
        trolleyDAO.save(trolley);
    }

    @Override
    public void edit(TrolleyEditFO input) {
        Trolley trolley = trolleyDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("台车信息不存在"));
        Trolley existTrolley = trolleyDAO.getByUdi(input.getUdi());
        if (Objects.nonNull(existTrolley) && !existTrolley.equals(trolley)) {
            throw new BusinessAssertException("已经存在相同的UDI设备");
        }
        input.convertTo(trolley);
        trolleyDAO.save(trolley);
    }

    @Override
    public Trolley getTrolleyIdsByUdi(String udi) {
        return trolleyDAO.getByUdi(udi);
    }

    @Override
    public PageVO<TrolleyListVO> listBuyTrolley(String medAgentId, PageFO pageFO) {
        return PageUtil.convert(trolleyDAO.findByMedAgentId(medAgentId, PageUtil.initJPAPage(pageFO)));
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void transfer(TrolleyTransferFO input) {
        //清除原有的绑定记录
        TrolleyTransfer existTransferLog = trolleyTransferDAO.getByTransferToHospitalId(input.getCurrentHospitalId());
        if (Objects.nonNull(existTransferLog)) {
            trolleyTransferDAO.delete(existTransferLog);
        }


        Trolley trolley = trolleyDAO.findById(input.getTrolleyId()).orElseThrow(() -> new BusinessAssertException("台车信息不存在"));
        trolley.setHospitalId(input.getToHospitalId());
        trolleyDAO.save(trolley);

        //处理台车和导管室的绑定关系
        if (StrUtil.isNotEmpty(input.getToCathRoomId())) {
            cathRoomDAO.findById(input.getToCathRoomId()).ifPresent(x -> {
                if (!input.getToHospitalId().equals(x.getHospitalId())) {
                    throw new BusinessAssertException("所属导管室不属于转移医院,请重新选择");
                }
            });

            CathRoomTrolley cathRoomTrolley = cathRoomTrolleyDAO.getByTrolleyIdAndHospitalId(input.getTrolleyId(), input.getToHospitalId());
            if (Objects.isNull(cathRoomTrolley)) {
                cathRoomTrolley = new CathRoomTrolley();
            }
            cathRoomTrolley.setTrolleyId(input.getTrolleyId());
            cathRoomTrolley.setCathRoomId(input.getToCathRoomId());
            cathRoomTrolley.setHospitalId(input.getToHospitalId());
            cathRoomTrolleyDAO.save(cathRoomTrolley);
        } else {
            CathRoomTrolley cathRoomTrolley = cathRoomTrolleyDAO.getByTrolleyIdAndHospitalId(input.getTrolleyId(), input.getToHospitalId());
            if (Objects.nonNull(cathRoomTrolley)) {
                cathRoomTrolleyDAO.delete(cathRoomTrolley);
            }
        }

        TrolleyTransfer trolleyTransfer = new TrolleyTransfer();
        input.convertTo(trolleyTransfer);
        trolleyTransfer.setTransferFromHospitalId(input.getCurrentHospitalId());
        trolleyTransfer.setTransferToHospitalId(input.getToHospitalId());
        trolleyTransferDAO.save(trolleyTransfer);
    }

    @Override
    public void changeUseStatus(String udi, TrolleyUseStatusEnum userStatus) {
        Trolley trolley = trolleyDAO.getByUdi(udi);
        if (Objects.nonNull(trolley)) {
            trolley.setUseStatus(userStatus.getCode());
            trolleyDAO.save(trolley);
        }
    }

    @Override
    public void changeUseStatusOnLine(String udi) {
        Trolley trolley = trolleyDAO.getByUdi(udi);
        if (Objects.nonNull(trolley) && TrolleyUseStatusEnum.OFFLINE.getCode().equals(trolley.getStatus())) {
            trolley.setUseStatus(TrolleyUseStatusEnum.ONLINE.getCode());
            trolleyDAO.save(trolley);
        }
    }

    @Override
    public TrolleyTransferVO transferLog(String trolleyId, BelongEnum belong) {
        TrolleyTransferVO result = new TrolleyTransferVO();
        TrolleyTransfer trolleyTransfer = trolleyTransferDAO.getByTrolleyId(trolleyId);
        if (Objects.nonNull(trolleyTransfer)) {
            String nickName = "";
            Optional<PlatformUser> platformUser = platformUserDAO.findById(trolleyTransfer.getCreatorId());
            if (platformUser.isPresent()) {
                nickName = platformUser.get().getNickName();
            }
            if (StrUtil.isEmpty(nickName)) {
                Optional<MedAgentUser> medAgentUser = medAgentUserDAO.findById(trolleyTransfer.getCreatorId());
                if (medAgentUser.isPresent()) {
                    nickName = medAgentUser.get().getNickName();
                }
            }
            result.setOperator(nickName);
            result.setCreateTime(trolleyTransfer.getCreateTime());
            result.setTransferDate(trolleyTransfer.getTransferDate());
            result.setInstalledDate(trolleyTransfer.getInstalledDate());
        }
        return result;
    }


}
