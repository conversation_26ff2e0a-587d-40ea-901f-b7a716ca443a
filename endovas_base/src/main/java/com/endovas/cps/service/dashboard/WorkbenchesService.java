package com.endovas.cps.service.dashboard;

import com.endovas.cps.pojo.vo.dashboard.MedImageMonthStatVO;
import com.endovas.cps.pojo.vo.dashboard.TodoListStatVO;
import com.endovas.cps.pojo.vo.dashboard.WorkbenchStat;
import io.daige.starter.common.security.LoginUser;

/**
 * @author: wk
 * @Date: 2024/12/30
 * @Time: 14:40
 */
public interface WorkbenchesService {
    /**
     * 统计数据
     * @param loginUser
     * @return
     */
    WorkbenchStat stat(LoginUser loginUser);

    /**
     * 医疗影像月度统计
     * @param year
     * @param loginUser
     * @return
     */
    MedImageMonthStatVO medImageMonthStat(String year, LoginUser loginUser);

    /**
     * 待办任务统计
     * @param loginUser
     * @return
     */
    TodoListStatVO todoListStat(LoginUser loginUser);
}
