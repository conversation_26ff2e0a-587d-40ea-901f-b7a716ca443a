package com.endovas.cps.service.measurement;

import com.endovas.cps.pojo.fo.measurement.MeasurementTaskAddFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskDetailFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskEditFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskModifyFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskSearchFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskSearchSelfFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskInfoVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultByPatientIdVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:40
 */
public interface MeasurementTaskService {

    /**
     * 添加测量任务
     * @param input
     */
    MeasurementTaskInfoVO add(MeasurementTaskAddFO input, LoginUser loginUser);

    /**
     * 编辑测量任务
     * @param input
     */
    void edit(MeasurementTaskEditFO input);

    /**
     * 领取测量任务
     * @param input
     * @param loginUser
     */
    void collect(MeasurementTaskModifyFO input, LoginUser loginUser);

    /**
     * 放弃测量任务
     * @param input
     * @param loginUser
     */
    void giveUp(MeasurementTaskModifyFO input, LoginUser loginUser);

    /**
     * 完成测量任务
     * @param input
     * @param loginUser
     */
    void finish(MeasurementTaskModifyFO input, LoginUser loginUser);

    /**
     * 关闭测量任务
     * @param input
     * @param loginUser
     */
    void close(MeasurementTaskModifyFO input, LoginUser loginUser);

    /**
     * 重启测量任务
     * @param input
     * @param loginUser
     */
    void reOpen(MeasurementTaskModifyFO input, LoginUser loginUser);

    /**
     * 获取测量任务池子
     * @param searchFO
     * @param page
     * @return
     */
    PageVO<MeasurementTaskListVO> listSelfMissionPool(MeasurementTaskSearchSelfFO searchFO, PageFO page,LoginUser loginUser);

    /**
     * 获取测量任务池子
     * @param searchFO
     * @param page
     * @return
     */
    PageVO<MeasurementTaskListVO> listMissionPool(MeasurementTaskSearchFO searchFO, PageFO page,LoginUser loginUser);




    /**
     * 查看测量任务详情
     * @param detailFO
     * @param loginUser
     * @return
     */
    MeasurementTaskInfoVO detail(MeasurementTaskDetailFO detailFO, LoginUser loginUser);

    /**
     * 测量任务操作日志
     *
     * @param taskId
     * @param status
     * @param loginUser
     */
    void measureOperationUpdateEnvPrepareStatus(String taskId, String status, LoginUser loginUser);
}
