package com.endovas.cps.service.mailattach.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.dto.mailattach.MailAttachDownloadDTO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAddFO;
import com.endovas.cps.pojo.vo.FileUploadKeyParam;
import com.endovas.cps.service.DictService;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.mailattach.QQMailService;
import com.endovas.cps.service.platform.PlatformUserService;
import com.endovas.cps.service.resource.DicomService;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

/**
 * @author: wk
 * @Date: 2025/2/11
 * @Time: 15:44
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QQMailServiceImpl implements QQMailService {
    private final String DOWNLOAD_KEYWORD = "https://wx.mail.qq.com/ftn/download";
    private final String TENCENT_LARGE_ATTACH_KEYWORD = "从QQ邮箱发来的超大附件";
    private final String DOWNLOAD_PAGE_LINK_KEYWORD = "https://wx.mail.qq.com/ftn/download";
    private final String MAIL_ATTACH_HOSPITAL_BIND_CONFIGKEY = "MAIL_ATTACH_HOSPITAL_BIND_CONFIG";

    private final FileService fileService;
    private final AliOSSProperties aliOSSProperties;
    private final DicomService dicomService;
    private final PlatformUserService platformUserService;
    private final DictService dictService;

    @Override
    public boolean containsLargeAttach(String mailContent) {
        return mailContent.contains(TENCENT_LARGE_ATTACH_KEYWORD);
    }

    @Override
    public String extractDownloadLink(String mailContent) {
        if (!mailContent.contains(DOWNLOAD_KEYWORD)) {
            return null;
        }

        try {
            // 解析下载链接
            int linkIdx = mailContent.lastIndexOf(DOWNLOAD_KEYWORD);
            String downloadPageLinkTemp = mailContent.substring(linkIdx);
            String downloadPageLink = downloadPageLinkTemp.substring(0, downloadPageLinkTemp.indexOf("<"));
            log.info("downloadPageLink:" + downloadPageLink);

            // 获取下载链接页面
            Document doc = Jsoup.connect(downloadPageLink).get();
            String downloadPageContent = doc.toString();

            if (downloadPageContent.contains(DOWNLOAD_PAGE_LINK_KEYWORD)) {
                String tempContent =
                        downloadPageContent.substring(downloadPageContent.indexOf(DOWNLOAD_PAGE_LINK_KEYWORD));
                String realLink = tempContent.substring(0, tempContent.indexOf("\""));

                // 将链接中的 \x26 转换为 &
                realLink = realLink.replaceAll("\\\\x26", "&");
                log.info("realDownloadLink:" + realLink);
                return realLink;


            }
        } catch (IOException e) {
            log.error("提取QQ邮件下载链接失败", e);
        }

        return null;
    }

    @Override
    public String extractFileName(String mailContent) {
        String downloadLink = extractDownloadLink(mailContent);
        // 获取302重定向后的真实下载链接
        String download302TmpDir = fileService.genLocalTmpDownLoadFilePath(
                aliOSSProperties.getTmpDir(), IdUtil.nanoId(), AttachmentTypeEnum.DICOM.getCode());
        String location = get302Location(downloadLink, download302TmpDir, "temp.data");
        FileUtil.del(download302TmpDir); // 删除临时文件

        if (StringUtils.isNotBlank(location)) {
            try {
                Map<String, String> paramMap = HttpUtil.decodeParamMap(location, StandardCharsets.UTF_8);
                String fileName = paramMap.get("fname");
                log.info("fileName:" + fileName);
                return fileName;
            } catch (Exception e) {
                log.error("提取QQ邮件文件名失败", e);
            }
        }
        return null;
    }

    @Override
    @Transactional
    public MailAttachDownloadDTO downloadAndProcess(String downloadLink, String fileName, String receiverAccount) {
        try {
            // 初始化dicom文件和对应的附件信息
            DicomAddFO addFO = new DicomAddFO();
            addFO.setFileName(fileName);

            // 获取邮件默认绑定的医院id
            String value = dictService.getOneParamValue(MAIL_ATTACH_HOSPITAL_BIND_CONFIGKEY);
            if (Objects.isNull(value)) {
                log.error("邮件处理，未配置默认医院");
                return null;
            }

            addFO.setHospitalId(value);
            addFO.setSensitiveFields(new ArrayList<>());

            PlatformUser user = platformUserService.getByAccount(receiverAccount);
            LoginUser loginUser = new LoginUser();
            loginUser.setId(user.getId());
            loginUser.setBelong(BelongEnum.PLATFORM_USER);

            FileUploadKeyParam fileUploadKeyParam = dicomService.save(addFO, SurgeryStageEnum.PREOP, loginUser);

            // 下载文件
            String downloadTmpDir = fileService.genLocalTmpDownLoadFilePath(
                    aliOSSProperties.getTmpDir(),
                    fileUploadKeyParam.getAttachmentId(),
                    AttachmentTypeEnum.DICOM.getCode()
            );
            String downloadPath = downloadTmpDir + fileName;

            // 获取302重定向后的真实下载链接
            String download302TmpDir = fileService.genLocalTmpDownLoadFilePath(
                    aliOSSProperties.getTmpDir(), IdUtil.nanoId(), AttachmentTypeEnum.DICOM.getCode());
            String location = get302Location(downloadLink, download302TmpDir, "temp.data");
            FileUtil.del(download302TmpDir); // 删除临时文件
            HttpUtil.download(location, Files.newOutputStream(new File(downloadPath).toPath()), true);

            MailAttachDownloadDTO result = new MailAttachDownloadDTO();
            result.setFileName(fileName);
            result.setTempFilePath(downloadPath);
            result.setOssFilePath(fileUploadKeyParam.getFilePath());
            result.setPltAccount(receiverAccount);
            return result;

        } catch (IOException e) {
            log.error("QQ邮件附件下载处理异常", e);
            return null;
        }
    }


    private String get302Location(String fileUrl, String filePath, String fileName) {
        HttpRequest request = cn.hutool.http.HttpUtil.createGet(fileUrl);
        try {
            HttpResponse response = request.execute();
            if (response.isOk()) {
                FileUtil.writeBytes(response.bodyBytes(), filePath + fileName);
                return null;
            } else {
                int status = response.getStatus();
                if (Objects.equals(status, 302)) {
                    // 重定向
                    return response.header("Location");
                } else {
                    log.error("请求失败：status:{},body:{}", response.getStatus(), response.body());
                }
            }
        } catch (IORuntimeException | HttpException e) {
            log.error("获取302重定向链接失败", e);
        }
        return null;
    }
}