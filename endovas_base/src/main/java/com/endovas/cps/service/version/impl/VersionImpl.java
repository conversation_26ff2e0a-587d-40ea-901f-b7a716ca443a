package com.endovas.cps.service.version.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.BooleanUtil;
import com.endovas.cps.dao.attachment.AttachmentTempDAO;
import com.endovas.cps.dao.version.VersionDAO;
import com.endovas.cps.dao.version.VersionUpdateStrategyDAO;
import com.endovas.cps.entity.attachment.AttachmentTemp;
import com.endovas.cps.entity.version.Version;
import com.endovas.cps.entity.version.VersionUpdateStrategy;
import com.endovas.cps.enums.VersionUpgradeScoreEnum;
import com.endovas.cps.pojo.fo.version.VersionAddFO;
import com.endovas.cps.pojo.fo.version.VersionEditFO;
import com.endovas.cps.pojo.fo.version.VersionStrategyAddFO;
import com.endovas.cps.pojo.vo.version.VersionListVO;
import com.endovas.cps.pojo.vo.version.VersionSelectVO;
import com.endovas.cps.pojo.vo.version.VersionUpdateStrategyVO;
import com.endovas.cps.pojo.vo.version.VersionUpdateVO;
import com.endovas.cps.service.version.VersionService;
import com.google.common.collect.Lists;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:09
 */
@Service
@RequiredArgsConstructor
public class VersionImpl implements VersionService {
    private final VersionDAO versionDAO;
    private final VersionUpdateStrategyDAO versionUpdateStrategyDAO;
    private final AttachmentTempDAO attachmentTempDAO;

    @Override
    public PageVO<VersionListVO> list(PageFO pageFO) {
        pageFO.setSort("applyTime");
        Pageable page = PageUtil.initJPAPage(pageFO);
        Page<VersionListVO> list = versionDAO.findAll(page).map(x -> {
            VersionListVO result = new VersionListVO().convertFrom(x);
            AttachmentTemp attachmentTemp = attachmentTempDAO.getAttachmentTempByTmpUrl(x.getDownloadUrl());
            if(Objects.nonNull(attachmentTemp)) {
                result.setAttachmentName(URLDecoder.decode(attachmentTemp.getTmpName(),Charset.defaultCharset()));
            }
            if (x.getApplyTime().isAfter(LocalDateTime.now())) {
                result.setStatus("未开始");
            } else {
                if (BooleanUtil.isTrue(x.getAvailable())) {
                    result.setStatus("生效中");
                } else {
                    result.setStatus("已失效");
                }
            }

            return result;
        });
        return PageUtil.convert(list);
    }


    @Override
    public List<VersionSelectVO> select(String terminalType, String os) {
        return versionDAO.findFirst10ByTerminalTypeAndOsAndAvailableIsTrueOrderByCreateTimeDesc(terminalType, os).stream().map(x -> new VersionSelectVO().convertFrom(x)).collect(Collectors.toList());
    }

    @Override
    public void switchAvailable(String id) {
        Version clientVersion = versionDAO.findById(id).orElseThrow(() -> new BusinessAssertException("版本不存在"));
        clientVersion.setAvailable(!clientVersion.getAvailable());
        versionDAO.save(clientVersion);
    }

    @Override
    public List<VersionUpdateStrategyVO> listStrategy(String versionId) {
        return versionUpdateStrategyDAO.findByVersionId(versionId).stream().map(x -> new VersionUpdateStrategyVO().convertFrom(x)).collect(Collectors.toList());
    }

    @Override
    public VersionUpdateVO checkUpdate(String terminalType, String os, String currentVersion) {
        Version latestVersion = versionDAO.findFirstByTerminalTypeAndOsAndApplyTimeLessThanAndAvailableIsTrueOrderByApplyTimeDesc(terminalType, os, LocalDateTime.now());
        //版本库没有数据不需要更新
        VersionUpdateVO result = new VersionUpdateVO();
        result.setShouldUpdate(false);
        if (Objects.isNull(latestVersion)) {
            return result;
        }
        //当前版本和最新版本一致不需要更新
        int compareResult = VersionComparator.INSTANCE.compare(currentVersion, latestVersion.getSoftVersion());
        if (compareResult >= 0) {
            result.setShouldUpdate(false);
            return result;
        }

        //判断当前版本的更新范围,没有策略,直接都升级
        if (VersionUpgradeScoreEnum.ALL.getCode().equals(latestVersion.getUpgradeScope())) {
            result.setShouldUpdate(true);
            result.setLatestVersion(latestVersion.getSoftVersion());
            result.setDownloadUrl(latestVersion.getDownloadUrl());
            result.setTitle(latestVersion.getTitle());
            result.setContent(latestVersion.getContent());
            result.setCancelText(latestVersion.getCancelText());
            result.setConfirmText(latestVersion.getConfirmText());
            result.setUpgradeMethod(latestVersion.getUpgradeMethod());
            return result;
        }


        List<VersionUpdateStrategy> versionUpdateStrategy = versionUpdateStrategyDAO.findByVersionId(latestVersion.getId());
        for (VersionUpdateStrategy one : versionUpdateStrategy) {
            String min = one.getMinVersion();
            String max = one.getMaxVersion();
            if (VersionComparator.INSTANCE.compare(currentVersion, min) >= 0 && VersionComparator.INSTANCE.compare(currentVersion, max) <= 0) {
                result.setShouldUpdate(true);
                result.setLatestVersion(latestVersion.getSoftVersion());
                result.setDownloadUrl(latestVersion.getDownloadUrl());
                result.setTitle(latestVersion.getTitle());
                result.setContent(latestVersion.getContent());
                result.setCancelText(latestVersion.getCancelText());
                result.setConfirmText(latestVersion.getConfirmText());
                result.setUpgradeMethod(latestVersion.getUpgradeMethod());
                return result;
            }
        }
        return result;
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void add(VersionAddFO input) {
        Version version = versionDAO.getByTerminalTypeAndOsAndSoftVersionAndAvailableIsTrue(input.getTerminalType(), input.getOs(), input.getSoftVersion());
        if (Objects.nonNull(version)) {
            throw new BusinessAssertException("版本号不能重复");
        }
        Version latestVersion = versionDAO.findFirstByTerminalTypeAndOsAndApplyTimeLessThanAndAvailableIsTrueOrderByApplyTimeDesc(input.getTerminalType(), input.getOs(), LocalDateTime.now());
        if (Objects.nonNull(latestVersion) && VersionComparator.INSTANCE.compare(input.getSoftVersion(), latestVersion.getSoftVersion()) <= 0) {
            throw new BusinessAssertException("版本号必须大于最新版本号");
        }
        version = new Version();
        input.convertTo(version);
        version.setAvailable(true);
        versionDAO.save(version);
        //处理更新策略
        processUpdateStrategy(input.getUpgradeStrategy(), version.getId());
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void edit(VersionEditFO input) {
        Version version = versionDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("版本不存在"));
        input.convertTo(version);
        versionDAO.save(version);
        processUpdateStrategy(input.getUpgradeStrategy(), version.getId());
    }


    private void processUpdateStrategy(List<VersionStrategyAddFO> updateStrategy, String versionId) {
        if (CollectionUtil.isNotEmpty(updateStrategy)) {
            List<VersionUpdateStrategy> exist = versionUpdateStrategyDAO.findByVersionId(versionId);
            versionUpdateStrategyDAO.deleteAll(exist);
            List<VersionUpdateStrategy> list = Lists.newArrayList();
            for (VersionStrategyAddFO input : updateStrategy) {
                VersionUpdateStrategy one = new VersionUpdateStrategy();
                one.setMinVersion(input.getMinVersion());
                one.setMaxVersion(input.getMaxVersion());
                one.setVersionId(versionId);
                list.add(one);
            }
            versionUpdateStrategyDAO.saveAll(list);
        }

    }

}
