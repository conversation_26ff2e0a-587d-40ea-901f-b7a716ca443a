package com.endovas.cps.service.resource;

import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.entity.resource.Dicom;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/6
 * Time: 15:40
 */
public interface DicomParseService {
    Dicom process(Long zipFileSize, String dicomPath, Attachment attachment) throws ExecutionException, InterruptedException, IOException;
}
