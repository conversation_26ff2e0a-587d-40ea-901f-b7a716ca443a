package com.endovas.cps.service.measurement;

import com.endovas.cps.pojo.fo.measurement.MeasurementResultAddFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskResultFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultByPatientIdVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultVO;
import io.daige.starter.common.security.LoginUser;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/25
 * @Time: 14:38
 */
public interface MeasurementResultService {

    /**
     * 上传结果
     * @param input
     * @param loginUser
     */
    void upload(MeasurementResultAddFO input, LoginUser loginUser);

    /**
     * 获取测量结果
     * @param input
     * @param loginUser
     * @return
     */
    MeasurementTaskResultVO getResult(MeasurementTaskResultFO input, LoginUser loginUser);

    /**
     * 获取结果列表
     * @param input
     * @param loginUser
     * @return
     */
    List<MeasurementTaskResultListVO> getResultList(MeasurementTaskResultFO input, LoginUser loginUser);

    List<MeasurementTaskResultByPatientIdVO> listResultByPatientId(String patientId, LoginUser loginUser);

    /**
     * 删除测量结果
     * @param input
     * @param loginUser
     */
    void delete(MeasurementTaskResultFO input, LoginUser loginUser);

    /**
     * 保存测量结果
     * @param input
     * @param loginUser
     */
    void saveResult(MeasurementTaskResultFO input, LoginUser loginUser);
}
