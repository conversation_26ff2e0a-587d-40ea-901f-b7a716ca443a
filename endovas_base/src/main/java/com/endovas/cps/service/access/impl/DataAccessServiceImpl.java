package com.endovas.cps.service.access.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.endovas.cps.dao.OrganizationDAO;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.entity.Organization;
import com.endovas.cps.service.access.DataAccessService;
import com.google.common.collect.Lists;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.database.mybatis.conditions.query.QueryWrap;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.security.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/8
 * Time: 20:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataAccessServiceImpl implements DataAccessService {
    private final PlatformUserDAO platformUserDAO;
    private final OrganizationDAO organizationDAO;
    public final static String MED_AGENT_ID = "medAgentId";
    public final static String CREATOR_BELONG = "creatorBelong";
    public static final String COL_MED_AGENT_ID = "med_agent_id";
    public static final String COL_CREATOR_BELONG = "creator_belong";


    @Override
    public List<Predicate> filterDataAuth(Root root, CriteriaBuilder criteriaBuilder, LoginUser loginUser) {
        // 用户数据范围
        List<Predicate> list = Lists.newArrayList();
        if (BelongEnum.MED_AGENT_USER.getCode().equals(loginUser.getBelong().getCode()) && BizCommonConst.ROLE_DATA_TYPE_ALL.equals(loginUser.getDataType())) {
            Predicate p1 = criteriaBuilder.equal(root.get(MED_AGENT_ID), loginUser.getMedAgentId());
            Predicate p2 = criteriaBuilder.equal(root.get(CREATOR_BELONG), loginUser.getBelong().getCode());
            list.add(p1);
            list.add(p2);
        }

        if (BelongEnum.PLATFORM_USER.getCode().equals(loginUser.getBelong().getCode()) && BizCommonConst.ROLE_DATA_TYPE_SAME.equals((loginUser.getDataType()))) {
            List<String> userIds = platformUserDAO.findByOrganizationId(loginUser.getOrgId()).stream().map(MysqlBase::getId).collect(Collectors.toList());
            Predicate p1 = criteriaBuilder.in(root.get(MysqlBase.CREATOR_ID)).value(userIds);
            list.add(p1);
        }

        if (BelongEnum.PLATFORM_USER.getCode().equals(loginUser.getBelong().getCode()) && BizCommonConst.ROLE_DATA_TYPE_UNDER.equals((loginUser.getDataType()))) {
            List<Organization> organizations = organizationDAO.findByCodeStartsWithAndActiveIsTrue(loginUser.getOrgId());
            if (CollectionUtil.isNotEmpty(organizations)) {
                List<String> userIds = platformUserDAO.findByOrganizationIdIn(organizations.stream().map(MysqlBase::getId).collect(Collectors.toList())).stream().map(MysqlBase::getId).collect(Collectors.toList());
                Predicate p1 = criteriaBuilder.in(root.get(MysqlBase.CREATOR_ID)).value(userIds);
                list.add(p1);
            }
        }

        if (BizCommonConst.ROLE_DATA_TYPE_SELF.equals(loginUser.getDataType())) {
            Predicate p1 = criteriaBuilder.equal(root.get(MysqlBase.CREATOR_ID), loginUser.getId());
            list.add(p1);
        }


        return list;
    }

    @Override
    public QueryWrap filterDataAuth(QueryWrap query, LoginUser loginUser) {

        // 用户数据范围
        if (BelongEnum.MED_AGENT_USER.getCode().equals(loginUser.getBelong().getCode()) && BizCommonConst.ROLE_DATA_TYPE_ALL.equals(loginUser.getDataType())) {
            query.eq(COL_MED_AGENT_ID, loginUser.getMedAgentId());
            query.eq(COL_CREATOR_BELONG, loginUser.getBelong().getCode());
        }

        if (BelongEnum.PLATFORM_USER.getCode().equals(loginUser.getBelong().getCode()) && BizCommonConst.ROLE_DATA_TYPE_SAME.equals((loginUser.getDataType()))) {
            List<String> userIds = platformUserDAO.findByOrganizationId(loginUser.getOrgId()).stream().map(MysqlBase::getId).collect(Collectors.toList());
            query.in(MysqlBase.COL_CREATOR_ID, userIds);
        }

        if (BelongEnum.PLATFORM_USER.getCode().equals(loginUser.getBelong().getCode()) && BizCommonConst.ROLE_DATA_TYPE_UNDER.equals((loginUser.getDataType()))) {
            List<Organization> organizations = organizationDAO.findByCodeStartsWithAndActiveIsTrue(loginUser.getOrgId());
            if (CollectionUtil.isNotEmpty(organizations)) {
                List<String> userIds = platformUserDAO.findByOrganizationIdIn(organizations.stream().map(MysqlBase::getId).collect(Collectors.toList())).stream().map(MysqlBase::getId).collect(Collectors.toList());
                query.in(MysqlBase.COL_CREATOR_ID, userIds);
            }


        }

        if (BizCommonConst.ROLE_DATA_TYPE_SELF.equals(loginUser.getDataType())) {
            query.eq(MysqlBase.COL_CREATOR_ID, loginUser.getId());
        }
        return query;
    }
}
