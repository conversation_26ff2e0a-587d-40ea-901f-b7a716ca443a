package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 09:46
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum EquipmentStatusEnum implements BaseEnum {

    IDLE("空闲"),
    INUSE("使用中"),
    ;
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static EquipmentStatusEnum match(String val, EquipmentStatusEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static EquipmentStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(EquipmentStatusEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
