package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 09:43
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LabelDataRangeEnum implements BaseEnum {

    PUBLIC("公共池"),
    INTERNAL("内部池"),
    EXPERT("专家池子"),
    ;
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static LabelDataRangeEnum match(String val, LabelDataRangeEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static LabelDataRangeEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(LabelDataRangeEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
