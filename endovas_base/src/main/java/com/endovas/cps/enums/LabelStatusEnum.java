package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 09:46
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LabelStatusEnum implements BaseEnum {

    TASKSTATUS_UNCLAIMED("未领取"),
    TASKSTATUS_ALREADYCLAIMED("已领取"),
    TASKSTATUS_COMPLETED("完成标注"),
    TASKSTATUS_CLOSED("任务已关闭"),


    ;
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static LabelStatusEnum match(String val, LabelStatusEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static LabelStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(LabelStatusEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
