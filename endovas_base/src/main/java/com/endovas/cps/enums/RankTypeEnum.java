package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 终端类型
 * </p>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum RankTypeEnum implements BaseEnum {
    HOSPITAL("医院"),
    MEASURE_USER("测量人")
    ;

    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static RankTypeEnum match(String val, RankTypeEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static RankTypeEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(RankTypeEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
