package com.endovas.cps.enums;

import cn.hutool.core.util.ObjectUtil;
import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DicomStatusEnum implements BaseEnum {
    UPLOADING("上传中"),
    PROCESSING("解析中"),
    FINISH("处理完成"),
    UPLOAD_FAIL("上传失败"),


    // 临时文件状态
    TEMPFILE_DOWNLOADING("下载中"),
    TEMPFILE_UNPACKING("解压中"),
    TEMPFILE_READY("准备就绪"),
    TEMPFILE_FAIL("处理失败"),
    TEMPFILE_CLEANEDUP("已被清理"),

    ;

    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static DicomStatusEnum match(String val, DicomStatusEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static DicomStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(DicomStatusEnum val) {
        return ObjectUtil.isNotNull(val) && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
