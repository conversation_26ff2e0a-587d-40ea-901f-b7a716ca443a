package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 终端类型
 * </p>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PatientAgeStatEnum implements BaseEnum {
    ONE(0, 29, "30岁以下"),
    THREE(30, 40, "30-40岁"),
    FOUR(41, 50, "41-50岁"),
    FIVE(51, 60, "51-60岁"),
    SIX(61, 70, "61-70岁"),
    SEVEN(71, 80, "71-80岁"),
    EIGHT(81, 200, "81岁以上"),
    ;

    private Integer start;
    private Integer end;
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static PatientAgeStatEnum match(String val, PatientAgeStatEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static PatientAgeStatEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(PatientAgeStatEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
