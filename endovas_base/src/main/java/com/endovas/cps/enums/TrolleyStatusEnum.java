package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TrolleyStatusEnum implements BaseEnum {
    SPOTS("库存现货"),
    BOUGHT("已采购"),
    INSTALLED("已安装"),

    ;
    private String desc;


    public static Boolean isValid(String val) {
        return Stream.of(values()).parallel().anyMatch(item -> item.desc.equalsIgnoreCase(val));
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static TrolleyStatusEnum match(String val, TrolleyStatusEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static TrolleyStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(TrolleyStatusEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
