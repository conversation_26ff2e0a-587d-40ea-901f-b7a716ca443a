package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SurgeryStageEnum implements BaseEnum {
    PREOP("术前"),
    INTRAOP("术中"),
    POSTOP("术后"),
    ;
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static SurgeryStageEnum match(String val, SurgeryStageEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static SurgeryStageEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(SurgeryStageEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}