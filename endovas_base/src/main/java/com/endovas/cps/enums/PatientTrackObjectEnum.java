package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PatientTrackObjectEnum implements BaseEnum {
    SELF("患者本人"),
    FAMILY("患者家属"),
    DOCTOR("主治医生"),
    ;
    private String desc;


    public static Boolean isValid(String val) {
        return Stream.of(values()).parallel().anyMatch(item -> item.desc.equalsIgnoreCase(val));
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static PatientTrackObjectEnum match(String val, PatientTrackObjectEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static PatientTrackObjectEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(PatientTrackObjectEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
