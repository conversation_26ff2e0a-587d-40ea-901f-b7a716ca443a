package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/16
 * Time: 11:24
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TrolleyUseStatusEnum implements BaseEnum {

    NOT_BIND("未绑定"), //供应导管室状态使用
    OFFLINE("离线"),
    ONLINE("开机"),
    MEETING("会议中"), //通过逻辑处理
    ;

    private String desc;
    public static Boolean isValid(String val) {
        return Stream.of(values()).parallel().anyMatch(item -> item.desc.equalsIgnoreCase(val));
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static TrolleyUseStatusEnum match(String val, TrolleyUseStatusEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static TrolleyUseStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(TrolleyUseStatusEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
