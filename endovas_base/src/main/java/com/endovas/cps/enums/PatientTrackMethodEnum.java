package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PatientTrackMethodEnum implements BaseEnum {
    FACE("当面随访"),
    TEL("电话随访"),
    TEXT("文本信息随访"),
   ;
    private String desc;


    public static Boolean isValid(String val) {
        return Stream.of(values()).parallel().anyMatch(item -> item.desc.equalsIgnoreCase(val));
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static PatientTrackMethodEnum match(String val, PatientTrackMethodEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static PatientTrackMethodEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(PatientTrackMethodEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
