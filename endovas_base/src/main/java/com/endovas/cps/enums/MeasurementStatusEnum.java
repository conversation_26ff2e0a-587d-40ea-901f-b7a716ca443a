package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 09:46
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MeasurementStatusEnum implements BaseEnum {

    TASKSTATUS_UNCLAIMED("未领取"),
    TASKSTATUS_ALREADYCLAIMED("已领取"),
    TASKSTATUS_COMPLETED("完成测量"),
    TASKSTATUS_CLOSED("任务已关闭"),

    ENVPREPARESTATUS_NOTSTARTED("未开始"),
    ENVPREPARESTATUS_ALLOCATE_DEVICE("分配测量机器"), // 25%
    ENVPREPARESTATUS_INIT("始化测量环境"), // 50%
    RESULTSYNCSTATUS_UPLOAD_DATA("同步测量影像数据"), // 75%
    ENVPREPARESTATUS_COMPLETED("初始化完成"), // 100%
    ENVPREPARESTATUS_FAIL("初始化失败"),

    RESULTSYNCSTATUS_NOTSTARTED("未开始"),
    RESULTSYNCSTATUS_NOTSYNC("未同步"),
    RESULTSYNCSTATUS_SYNC("同步中"),
    RESULTSYNCSTATUS_COMPLETED("同步成功"),
    RESULTSYNCSTATUS_FAIL("同步失败"),
    ;
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static MeasurementStatusEnum match(String val, MeasurementStatusEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static MeasurementStatusEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(MeasurementStatusEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
