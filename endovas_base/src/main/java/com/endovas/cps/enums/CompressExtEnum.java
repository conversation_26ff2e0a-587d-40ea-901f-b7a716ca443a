package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CompressExtEnum implements BaseEnum {
    RAR("rar"),
    ZIP("zip"),
   ;
    private String desc;


    public static Boolean isValid(String val) {
        return Stream.of(values()).parallel().anyMatch(item -> item.desc.equalsIgnoreCase(val));
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static CompressExtEnum match(String val, CompressExtEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static CompressExtEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(CompressExtEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
