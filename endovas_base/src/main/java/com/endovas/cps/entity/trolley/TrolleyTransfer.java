package com.endovas.cps.entity.trolley;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:48
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_trolley_transfer")
@TableName("t_trolley_transfer")
public class TrolleyTransfer extends MysqlBase {
    private String trolleyId;
    private String transferFromHospitalId;
    private String transferToHospitalId;
    private String installedDate;
    private String transferDate;


}
