package com.endovas.cps.entity.version;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/4/3
 * Time: 09:25
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_version")
@TableName("t_version")
public class Version extends MysqlBase {
    private String terminalType;//终端类型
    private String os;//操作系统
    private String softVersion;//版本号
    private String title;//弹窗标题
    private String content;//弹窗内容
    private String cancelText;//取消文本内容
    private String confirmText;//确认文本内容
    private String remark;//备注
    private LocalDateTime applyTime;//生效时间

    private String downloadUrl;
    private Boolean available;
    /**
     * @see com.endovas.cps.enums.VersionUpgradeMethodEnum
     */
    private String upgradeMethod;  //升级方式

    /**
     * @see com.endovas.cps.enums.VersionUpgradeScoreEnum
     */
    private String upgradeScope;//升级范围
}
