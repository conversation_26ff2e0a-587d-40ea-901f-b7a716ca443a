package com.endovas.cps.entity.announcement;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:24
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_announcement")
@TableName("t_announcement")
public class Announcement extends MysqlBase {
    private String title;
    private String content;
}
