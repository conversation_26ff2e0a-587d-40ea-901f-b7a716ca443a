package com.endovas.cps.entity.measurement;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 测量操作记录（每次分配设备进行测量会进行记录）
 * @author: wk
 * @Date: 2024/11/18
 * @Time: 18:25
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_measurement_operation_record")
@TableName("t_measurement_operation_record")
public class MeasurementOperationRecord extends MysqlBase {

    /**
     * 关联信息
     */
    private String measurementId; // 测量任务id;
    private String equipmentId; // 分配的测量设备Id;
    private String dicomId; // 影像文件id
    private String screenRecordFileName; // 屏幕录像文件名称

    private Boolean isUsingEquip; // 是否正在使用设备
    private Boolean isFinished; // 远程连接是否完成，并非测量业务完成，断开连接视为完成

    private String creatorBelong; //   发起人类型
}
