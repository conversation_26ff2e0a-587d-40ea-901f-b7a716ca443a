package com.endovas.cps.entity.patient;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:18
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_patient")
@TableName("t_patient")
public class Patient extends MysqlBase {
    public final static String SURGERY_TYPE_ID="surgeryTypeId";
    public final static String HOSPITAL_ID="hospitalId";
    public final static String NAME="name";

    private String name;
    private String pid;
    private Integer age;
    private String gender;
    //体重
    private Integer weight;
    //身高
    private Integer height;
    private String surgeryTypeId;
    private String hospitalId;

    private String remark;

    //创建人归属
    private String creatorBelong;

    private String medAgentId;


    @Column(columnDefinition = "text COMMENT '病患主诉'")
    private String chiefComplaint;
    @Column(columnDefinition = "text COMMENT '现病史'")
    private String presentIllnessHistory;
    @Column(columnDefinition = "text COMMENT '既往史'")
    private String pastMedicalHistory;


}
