package com.endovas.cps.entity.mailattach;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @author: wk
 * @Date: 2025/2/14
 * @Time: 18:37
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_mail_attach")
@TableName("t_mail_attach")
public class MailAttach extends MysqlBase {
    private String mailAccount;
    private String mailId;
    private Integer extractStatus; // 0-初始化, 1-成功, 2-失败
    @Column(columnDefinition="blob COMMENT '邮件内容'")
    private String mailContent;
    private String errorMsg; // 错误信息
}
