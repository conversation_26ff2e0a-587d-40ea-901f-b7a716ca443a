package com.endovas.cps.entity.patient;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/4
 * Time: 14:08
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_patient_track")
@TableName("t_patient_track")
public class PatientTrack extends MysqlBase {
    public final static String PATIENT_ID = "patientId";
    public final static String TRACK_OBJECT = "trackObject";
    public final static String TRACK_METHOD = "trackMethod";
    public final static String CREATOR_NAME = "creatorName";
    public final static String TRACK_TIME = "trackTime";


    private String patientId;
    private LocalDateTime trackTime;
    //随访对象
    private String trackObject;
    //随访方式
    private String trackMethod;
    private String content;


    private String creatorName;
    //创建人归属
    private String creatorBelong;


}
