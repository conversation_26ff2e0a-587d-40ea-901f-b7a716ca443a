package com.endovas.cps.entity.hospital;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *  导管室和台车绑定关系
 * 1对1
 * @author: bin.yu
 * Date: 2024/8/14
 * Time: 14:11
 */

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_cath_room_trolley")
@TableName("t_cath_room_trolley")
public class CathRoomTrolley extends MysqlBase {
    private String hospitalId;
    private String cathRoomId;
    private String trolleyId;




}