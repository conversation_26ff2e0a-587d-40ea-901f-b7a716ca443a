package com.endovas.cps.entity.label;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:18
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_label_task_record")
@TableName("t_label_task_record")
public class LabelTaskRecord extends MysqlBase {

    private String labelTaskId; //  测量任务ID
    private String content; // 操作内容

    private String beforeTaskStatus; // 操作前状态
    private String afterTaskStatus; // 操作后状态

    private String operaterId; //  操作员
    private String operaterBelong; //  人员类型

}
