package com.endovas.cps.entity.trolley;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:14
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_trolley")
@TableName("t_trolley")
public class Trolley extends MysqlBase {

    public static final String PRODUCT_NAME = "productName";
    public static final String SOFT_VER = "softVer";
    public static final String STATUS = "status";
    public static final String SPEC_NAME = "specName";
    public static final String MED_AGENT_ID="medAgentId";




    private String productName;
    private String udi;
    private String modelName;
    private String specName;
    private String sysVer;
    private String softVer;
    private String serverVer;
    private String prodDate;
    private String installedDate;
    private String remark;
    private String status;

    private String hospitalId;
    private String medAgentId;

    private String useStatus;


}
