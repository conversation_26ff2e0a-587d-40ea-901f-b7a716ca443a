package com.endovas.cps.entity.measurement;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 测量任务状态流转记录
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:18
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_measurement_task_record")
@TableName("t_measurement_task_record")
public class MeasurementTaskRecord extends MysqlBase {

    private String measurementId; //  测量任务ID
    private String content; // 操作内容

    private String beforeTaskStatus; // 操作前状态
    private String afterTaskStatus; // 操作后状态
    private String beforeResultSyncStatus; // 操作前测量结果同步状态
    private String afterResultSyncStatus; // 操作后测量结果同步状态
    private String beforeEnvPrepareStatus; // 操作前环境准备状态
    private String afterEnvPrepareStatus; // 操作后环境准备状态

    private String operaterId; //  操作员
    private String operaterBelong; //  人员类型

}
