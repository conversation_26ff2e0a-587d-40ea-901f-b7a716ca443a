package com.endovas.cps.entity.surgery;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/4/3
 * Time: 09:25
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_surgery_type")
@TableName("t_surgery_type")
public class SurgeryType extends MysqlBase {
    private String nameWithParent;//父级名称+当前名称
    private String name;
    private Integer serialNumber;//序号
    private Boolean active;//是否可用
    private String parentId;//父级ID
}
