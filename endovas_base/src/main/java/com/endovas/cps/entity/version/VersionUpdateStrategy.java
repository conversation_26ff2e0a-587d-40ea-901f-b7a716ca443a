package com.endovas.cps.entity.version;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 针对某个客户端版本的升级策略
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午12:24
 */
@Getter
@Setter
@Entity
@Table(name = "t_version_update_strategy")
@TableName("t_version_update_strategy")
public class VersionUpdateStrategy extends MysqlBase {

    private String versionId;
    private String minVersion;
    private String maxVersion;


}
