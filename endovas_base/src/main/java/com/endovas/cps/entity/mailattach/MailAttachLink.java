package com.endovas.cps.entity.mailattach;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_mail_attach_link")
@TableName("t_mail_attach_link")
public class MailAttachLink extends MysqlBase {
    private String mailAttachId;
    @Column(columnDefinition="longtext COMMENT '提取的大附件链接'")
    private String downloadLink;
    private String fileName;
    private String receiverAccount;
    private String mailType; // QQ, NETEASE_163, NETEASE_126
    private Integer status; // 0-初始化, 1-处理中, 2-成功, 3-失败
    private String ossFilePath;
    @Column(columnDefinition="longtext COMMENT '提取的大附件链接'")
    private String errorMsg;
}