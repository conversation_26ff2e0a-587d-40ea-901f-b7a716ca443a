package com.endovas.cps.entity.measurement;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 测量任务（记录需要测量的任务内容）
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:16
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_measurement_task")
@TableName("t_measurement_task")
public class MeasurementTask extends MysqlBase {

    public final static String MEASUREMENT_TASK_ID="measurementTaskId";
    public final static String SURGERY_TYPE_ID="surgeryTypeId";
    public final static String TASK_STATUS="taskStatus";
    public final static String HOSPITAL_ID="hospitalId";
    public final static String PATIENT_NAME="patientName";
    public final static String PATIENT_ID="patientId";
    public final static String DICOM_NAME="dicomName";
    public final static String MEASURER_ID="measurerId";


    private String measurementTaskId; //  任务id
    private String dicomId; //  影像id
    private String patientId; //  患者id
    private String hospitalId; //  医院id
    private String surgeryTypeId; //  疾病类型id
    // private String createId   任务发起人
    private String measurerId; //   任务领取人
    private String equipId; //  测量设备id

    private String dataRangeType; //     public  internal  expert   任务类型
    private String creatorBelong; //   发起人类型
    private String measurerBelong; //  领取人类型
    private String taskStatus; //   unclaimed  AlreadyClaimed  Completed  closed
    private String resultSyncStatus; //   notStarted notSync  sync  Completed  fail
    private String envPrepareStatus; //   notStarted  init Completed  fail

    private LocalDateTime taskCompletedTime; // 任务完成时间

    private String patientName;
    private String dicomName;
    private String dicomContentDate; // 文件内容日期
    private String modality;  // 影像类型
    private String SurgeryTypeName;  // 疾病类型
    private String remark; // 备注

    private String medAgentId;

}
