package com.endovas.cps.entity.meeting;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/11
 * Time: 12:38
 */
@Setter
@Getter
@Entity
@Table(name = "t_meeting_room_chapter")
@TableName("t_meeting_room_chapter")
public class VideoChapter extends MysqlBase {
    private String deviceId;
    private String time;
    private String remark;
}
