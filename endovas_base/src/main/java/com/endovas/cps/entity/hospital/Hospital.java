package com.endovas.cps.entity.hospital;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/11/30
 * Time: 18:56
 */

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_hospital")
@TableName("t_hospital")
public class Hospital extends MysqlBase {
    public final static String NAME = "name";

    private String name;
    private String levelCode;
    private String countryCode;
    private String provinceCode;
    private String address;
    private String contactInfo;
    private String remark;

}
