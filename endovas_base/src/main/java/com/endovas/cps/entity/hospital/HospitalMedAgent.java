package com.endovas.cps.entity.hospital;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 * <p>
 * 一个医院可以有一个代理商
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:35
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_hospital_med_agent")
@TableName("t_hospital_med_agent")
public class HospitalMedAgent extends MysqlBase {
    private String hospitalId;
    private String medAgentId;
}
