package com.endovas.cps.entity.attachment;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;



/**
 * Created by IntelliJ IDEA.
 * 用于附件更新操作的临时表
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午12:00
 */

@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_attachment_temp")
@TableName("t_attachment_temp")
public class AttachmentTemp extends MysqlBase {
    private String tmpUrl;
    private String tmpContentType;
    private String tmpName;
    private String tmpType;
    private Integer tmpOrderCol;
}
