package com.endovas.cps.entity.resource;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/30
 * Time: 11:41
 */

@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_dicom_sensitive_field")
@TableName("t_dicom_sensitive_field")
public class DicomSensitive<PERSON>ield extends MysqlBase {
    private String dicomId;
    private String code;
}
