package com.endovas.cps.entity.attachment;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;



/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午12:00
 */

@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "t_attachment")
@TableName("t_attachment")
public class Attachment extends MysqlBase {

    private String type;
    private Integer orderCol;
    private String targetId;
    private String url;
    private String contentType;
    private String name;
    private String attachmentTempId;
}
