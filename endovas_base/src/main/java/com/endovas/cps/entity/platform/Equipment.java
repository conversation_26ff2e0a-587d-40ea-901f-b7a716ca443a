package com.endovas.cps.entity.platform;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:14
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_equipment")
@TableName("t_equipment")
public class Equipment extends MysqlBase {
    public static final String NAME = "name";
    public static final String SOFT_NAME = "softName";
    public static final String STATUS = "status";
    public static final String ORGANIZATION_ID = "organizationId";
    public static final String OS = "os";

    private String name;
    private String softName;
    private String softVer;
    private String os;
    private String osVer;
    private String serverUrl;
    private String organizationId;

    private String status;

    private String protocol;
    private String hostname;
    private String port;
    private String username;
    private String password;


}
