package com.endovas.cps.util;

import com.endovas.cps.config.properties.HttpClientProperties;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Set;

@Component
public class HttpRequestUtils {
    private static Logger log = LoggerFactory.getLogger(HttpRequestUtils.class);

    private static final Charset charset = Charset.forName("UTF-8");

    @Autowired
    HttpClientProperties httpClientProperties;

    public String get(String url) {
        CloseableHttpResponse httpResponse = null;
        CloseableHttpClient client = null;
        try {
            HttpGet get = new HttpGet(url);
            client = getClient();
            httpResponse = client.execute((HttpUriRequest)get);
            HttpEntity entity = httpResponse.getEntity();
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200){
                return EntityUtils.toString(entity, charset);
            }
        } catch (IOException e) {
            log.error("httpclient请求失败", e);
        } finally {
            if (httpResponse != null){
                try {
                    EntityUtils.consume(httpResponse.getEntity());
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("关闭response失败", e);
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    log.error("关闭httpClient失败", e);
                }
            }
        }
        return null;
    }


    /**
     * 发送get请求，参数为json
     * @param url
     * @param headerMap
     * @param paramMap
     * @return
     * @throws Exception
     */
    public String get(String url, Map<String, String> headerMap, Map<String, String> paramMap) {
        CloseableHttpResponse httpResponse = null;
        CloseableHttpClient client = null;
        try {

            StringBuilder uriBuilder = new StringBuilder(url).append("?");
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                uriBuilder.append(entry.getKey());
                uriBuilder.append("=");
                uriBuilder.append(entry.getValue());
                uriBuilder.append("&");
            }
            uriBuilder.deleteCharAt(uriBuilder.length()-1);

            HttpGet get = new HttpGet(uriBuilder.toString());
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                get.setHeader(entry.getKey(), entry.getValue());
            }
            client = getClient();
            httpResponse = client.execute(get);
            HttpEntity entity = httpResponse.getEntity();
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200){
                return EntityUtils.toString(entity, charset);
            } else {
                log.error("httpclient请求失败");
                log.error(httpResponse.getStatusLine().getReasonPhrase());
            }
        } catch (Exception e) {
            log.error("httpclient请求失败", e);
        } finally {
            if (httpResponse != null){
                try {
                    EntityUtils.consume(httpResponse.getEntity());
                    httpResponse.close();
                } catch (Exception e) {
                    log.error("关闭response失败", e);
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (Exception e) {
                    log.error("关闭httpClient失败", e);
                }
            }
        }
        return null;
    }


    public String post(String url, String str) {
        return postNormal(url, str);
    }

    private String postNormal(String url, String str) {
        CloseableHttpResponse httpResponse = null;
        CloseableHttpClient client = null;
        try {
            HttpPost post = new HttpPost(url);
            StringEntity myEntity = new StringEntity(str, ContentType.APPLICATION_JSON);
            post.setEntity(myEntity);
            client = getClient();
            httpResponse = client.execute((HttpUriRequest)post);
            HttpEntity entity = httpResponse.getEntity();
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200){
                String result = EntityUtils.toString(entity, charset);
                log.info("request:{},result{}", url, result);
                return result;
            }
        } catch (IOException e) {
            log.error("httpclient请求失败", e);
        } finally {
            if (httpResponse != null){
                try {
                    EntityUtils.consume(httpResponse.getEntity());
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("关闭response失败", e);
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    log.error("关闭httpClient失败", e);
                }
            }
        }
        return null;
    }

    public String postFile(String postUrl, File postFile, Map<String,String> postParam) {
        CloseableHttpResponse httpResponse = null;
        CloseableHttpClient client = null;
        try{
            HttpPost httpPost = new HttpPost(postUrl);

            // 组装文件参数
            FileBody fundFileBin = new FileBody(postFile);
            MultipartEntityBuilder multipartEntity = MultipartEntityBuilder.create();
            multipartEntity.addPart("file", fundFileBin); // 相当于<input type="file" name="media"/>

            //组装文件以外的参数
            Set<String> keySet = postParam.keySet();
            for (String key : keySet) {
                //相当于<input type="text" name="name" value=name>
                multipartEntity.addPart(key, new StringBody(postParam.get(key), ContentType.create("text/plain", Consts.UTF_8)));
            }

            HttpEntity reqEntity = multipartEntity.build();
            httpPost.setEntity(reqEntity);

            // log.info("发起请求的页面地址:{}", httpPost.getRequestLine());
            //发起请求   并返回请求的响应
            client = getClient();
            httpResponse = client.execute(httpPost);

            //打印响应状态
            //log.info(response.getStatusLine());
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            log.info("请求url:{}_返回状态:{}", postUrl, httpResponse.getStatusLine().getStatusCode());
            if (statusCode == 200){
                HttpEntity entity = httpResponse.getEntity();
                return EntityUtils.toString(entity, charset);
            }

            return null;
        } catch (ClientProtocolException e1) {
            log.error("httpclient请求失败", e1);
        } catch (IOException e1) {
            log.error("httpclient请求失败", e1);
        } finally{
            if (httpResponse != null){
                try {
                    EntityUtils.consume(httpResponse.getEntity());
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("关闭response失败", e);
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    log.error("关闭httpClient失败", e);
                }
            }
        }

        return null;
    }


    private CloseableHttpClient getClient() {

        SSLConnectionSocketFactory sSLConnectionSocketFactory = null;
        try {
            sSLConnectionSocketFactory = new SSLConnectionSocketFactory(SSLContext.getDefault());
        } catch (NoSuchAlgorithmException e) {
            log.error("创建SSL连接失败");
        }

        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("https", sSLConnectionSocketFactory)
                .register("http", new PlainConnectionSocketFactory())
                .build();

        // 超时配置
        RequestConfig defaultConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(httpClientProperties.getConnectionRequestTimeout())
                .setConnectTimeout(httpClientProperties.getConnectTimeout())
                .setSocketTimeout(httpClientProperties.getSocketTimeout())
                .build();

        final PoolingHttpClientConnectionManager httpClientConnectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        httpClientConnectionManager.setMaxTotal(httpClientProperties.getMaxTotal());
        httpClientConnectionManager.setDefaultMaxPerRoute(httpClientProperties.getDefaultMaxPerRoute());

        final HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setConnectionManager(httpClientConnectionManager);
        httpClientBuilder.setDefaultRequestConfig(defaultConfig);

        return httpClientBuilder.build();

    }


//    public static void main(String[] args) {
//        String str = "<?xml version=\"1.0\" encoding=\"GBK\"?><stream><action>DLSBALQR</action><userName>hegangzl</userName><accountNo>8111801013500468705</accountNo><subAccNo>3111810043674051642</subAccNo></stream>";
//        System.out.println(post("http://220.194.242.39:6789", str));
//    }
}
