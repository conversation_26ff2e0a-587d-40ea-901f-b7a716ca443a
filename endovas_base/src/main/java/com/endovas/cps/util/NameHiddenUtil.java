package com.endovas.cps.util;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 16:28
 */
public class NameHiddenUtil {

    public static String hidden(String name){
        if(Validator.hasChinese(name)){
            return  DesensitizedUtil.desensitized(name, DesensitizedUtil.DesensitizedType.CHINESE_NAME);
        }
        return StrUtil.isBlank(name) ? "" : StrUtil.hide(name, 3, name.length());
    }
}
