package com.endovas.cps.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.endovas.cps.enums.AttachmentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.sevenzipjbinding.IInArchive;
import net.sf.sevenzipjbinding.SevenZip;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;
import net.sf.sevenzipjbinding.impl.RandomAccessFileOutStream;
import net.sf.sevenzipjbinding.simple.ISimpleInArchive;
import net.sf.sevenzipjbinding.simple.ISimpleInArchiveItem;
import org.apache.commons.lang3.StringUtils;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.io.DicomInputStream;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

@Slf4j
public class FileExtractUtils {

    static final String DICOM_FILE_EXTENSION_NAME = "dcm";
    static final String DICOM_FILE_EXTENSION = ".dcm";
    static final String DICOM_HEADER = "DICM";


    /**
     * 使用7z解压rar文件（兼容rar5和rar4）
     *
     * @param rarPath
     * @param descDir
     * @param rarFileBizType
     */
    public static void unRarFileWith7z(String rarPath, String descDir, String rarFileBizType) {
        try {
            RandomAccessFile randomAccessFile = null;
            IInArchive inArchive = null;

            File pathFile = new File(descDir);
            if (pathFile.exists()) {
                FileUtil.del(pathFile);
            }
            pathFile.mkdirs();

            // 第一个参数是需要解压的压缩包路径，第二个参数参考JdkAPI文档的RandomAccessFile
            randomAccessFile = new RandomAccessFile(rarPath, "r");
            inArchive = SevenZip.openInArchive(null, new RandomAccessFileInStream(randomAccessFile));

            ISimpleInArchive simpleInArchive = inArchive.getSimpleInterface();

            for (ISimpleInArchiveItem item : simpleInArchive.getArchiveItems()) {
                String filePath = descDir + File.separator + item.getPath();
                File targetFile = new File(filePath);
                targetFile.getParentFile().mkdirs();

                if (!item.isFolder()) {
                    simpleInArchive.getArchiveItem(item.getItemIndex()).extractSlow(new RandomAccessFileOutStream(new RandomAccessFile(targetFile, "rw")));

                    if (rarFileBizType.equals(AttachmentTypeEnum.DICOM.getCode())) {
                        delNonDicomAndAddExt(targetFile);
                    }
                }
            }

            inArchive.close();

        } catch (Exception e) {
            log.error("使用7z解压rar异常", e);
        }
    }


    /**
     * 解压zip压缩文件到指定目录
     *
     * @param zipPath        zip压缩文件绝对路径
     * @param descDir        指定的解压目录
     * @param zipFileBizType zip文件业务类型，可以为null
     */
    public static void unzipFile(String zipPath, String descDir, String zipFileBizType) throws IOException {
        try {
            File zipFile = new File(zipPath);
            if (!zipFile.exists()) {
                log.error("要解压的压缩文件不存在");
                return;
            }
            File pathFile = new File(descDir);
            if (pathFile.exists()) {
                FileUtil.del(pathFile);
            }
            pathFile.mkdirs();
            unzipWithStream(zipPath, descDir, zipFileBizType, "GBK");
        } catch (Exception e) {
            try {
                unzipWithStream(zipPath, descDir, zipFileBizType, "UTF8");
            } catch (Exception ex) {
                log.error("解压异常", e);
            }
        }
    }

    /**
     * 解压
     *
     * @param zipPath
     * @param descDir
     * @param zipFileBizType
     * @param charset
     */
    public static void unzipWithStream(String zipPath, String descDir, String zipFileBizType, String charset) {
        if (!descDir.endsWith(File.separator)) {
            descDir = descDir + File.separator;
        }

        ZipUtil.unzip(zipPath, descDir, Charset.forName(charset));
        List<File> fileList = FileUtil.loopFiles(descDir);

        if (zipFileBizType.equals(AttachmentTypeEnum.DICOM.getCode())) {
            for (File file : fileList) {
                delNonDicomAndAddExt(file);
            }
        }
    }

    /**
     * 判断是否是dicom文件
     *
     * @param filePath
     * @return
     */
    public static boolean isDicomFile(String filePath) {
        File file = new File(filePath);
        if (file.isDirectory() || !file.exists()) {
            return false;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            // 跳过前128字节
            fis.skip(128);

            // 读取接下来的4个字节
            byte[] dicomHeader = new byte[4];
            fis.read(dicomHeader);

            // 检查是否包含“DICM”标识符
            String header = new String(dicomHeader);
            if (DICOM_HEADER.equals(header)) {
                // DICOM文件可以表示目录，不包含影像文件，所以需要排除
                try (DicomInputStream dis = new DicomInputStream(fis)) {
                    Attributes attr = dis.readDataset(-1, -1);
                    String modality = attr.getString(Tag.Modality);
                    if (StringUtils.isNotBlank(modality)) {
                        return true;
                    }
                } catch (Exception e) {
                    log.error("读取dcm文件异常：{}", filePath);
                    log.error("读取dcm文件异常", e);
                    return false;
                }
            }
        } catch (IOException e) {
            log.error("非dicom文件:{}", filePath);
            log.error("非dicom文件", e);
        }

        return false;
    }

    /**
     * 删除非dicom文件，并添加dcm后缀
     * @param file
     */
    public static void delNonDicomAndAddExt(File file) {
        // 直接删除非dcm结尾的文件(尽可能)
        String extName = FileUtil.extName(file);
        if (Objects.nonNull(extName) && !extName.equalsIgnoreCase(DICOM_FILE_EXTENSION_NAME) && extName.length()==3) {
            FileUtil.del(file.getAbsolutePath());
            return;
        }

        Boolean isDicom = isDicomFile(file.getAbsolutePath());
        if (!isDicom) { // 如果不是dicom文件，直接删除
            FileUtil.del(file.getAbsolutePath());
        } else {
            // 给dicom文件补充后缀
            if (!file.getAbsolutePath().toLowerCase(Locale.ROOT).endsWith(DICOM_FILE_EXTENSION)) {
                String newOutPath = file.getAbsolutePath() + DICOM_FILE_EXTENSION;
                FileUtil.rename(new File(file.getAbsolutePath()), newOutPath, true);
            }
        }
    }

    //测试方法
    public static void main(String[] args) throws IOException {

        // 解压文件
        String zipPath = "/Users/<USER>/Dicom-zhangself.rar";
        String descDir = "/Users/<USER>/file/tempfile";
////        unzipFile(zipPath, descDir, "DICOM");
        unRarFileWith7z(zipPath, descDir, "DICOM");

        // 检查目录是否是dicom文件
        /*File[] files = FileUtil.ls("/tmp/DICOM/4249302653954965556/extract/武强/SYNGO_FV/");
        for (File file : files) {
            if(file.isFile()){
                System.out.println(file.getName());
                System.out.println(isDicomFile(file.getPath()));
            }

        }*/
    }

}
