package com.endovas.cps.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @author: wk
 * @Date: 2024/11/22
 * @Time: 11:46
 */
public class FilePathUtils {

    // endovas/DICOM2025-03-05/4561361096350703621/DICOM_5abf5857ed604622b1e4a1bad17f7ab3.rar
    public static String genOssFileFullPath(String fileType, String targetId, String originalFilename) {
        StringBuilder fullFilePath = new StringBuilder("endovas");
        fullFilePath.append(StrUtil.SLASH);
        fullFilePath.append(fileType);
        fullFilePath.append(DateUtil.format(new Date(), "yyyy-MM-dd"));
        fullFilePath.append(StrUtil.SLASH);
        fullFilePath.append(targetId);
        fullFilePath.append(StrUtil.SLASH);
        fullFilePath.append(fileType);
        fullFilePath.append(StrUtil.UNDERLINE);
        fullFilePath.append(IdUtil.fastSimpleUUID());


        String extName = FileUtil.extName(originalFilename);
        if (StringUtils.isNotBlank(extName)) {
            fullFilePath.append(StrUtil.DOT);
            fullFilePath.append(extName);
        }
        return fullFilePath.toString();
    }
}
