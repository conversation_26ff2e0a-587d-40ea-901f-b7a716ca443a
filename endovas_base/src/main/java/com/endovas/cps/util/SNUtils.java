package com.endovas.cps.util;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

public class SNUtils {
    private static Integer count;
    private static Object lock;
    private static final SimpleDateFormat sdf;
    
    public static synchronized String getEbankSn() {
        return new StringBuffer(SNUtils.sdf.format(new Date())).append(getCount()).toString();
    }
    
    public static String getCrccfcSn() {
        return getEbankSn();
    }
    
    private static synchronized String getCount() {
        synchronized (SNUtils.lock) {
            if (SNUtils.count == 999) {
                SNUtils.count = 0;
            }
            final Integer count = SNUtils.count;
            ++SNUtils.count;
            return StringUtils.leftPad(SNUtils.count.toString(), 3, "0");
        }
    }
    
    public static String getFixLenthString(final int strLength) {
        final Random rm = new Random();
        final double pross = (1.0 + rm.nextDouble()) * Math.pow(10.0, strLength);
        final String fixLenthString = String.valueOf(pross);
        return fixLenthString.substring(2, strLength + 2);
    }
    
    public static void main(final String[] args) {
        for (int i = 0; i < 10; ++i) {
            System.out.println(getEbankSn());
        }
    }
    
    static {
        SNUtils.count = 0;
        SNUtils.lock = new Object();
        sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    }
}
