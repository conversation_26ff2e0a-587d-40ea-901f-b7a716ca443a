package com.endovas.cps.util;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2025/7/31
 * Time: 11:09
 */
public class PdfExtractUtil {

    /**
     * 提取第一个评论的内容
     * 从"评论 :"开始到"► 介入方案"结束
     */
    public static String extractFirstComment(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 查找第一个"评论 :"的位置
        int commentStart = text.indexOf("评论 :");
        if (commentStart == -1) {
            return "";
        }

        // 从评论标记后开始提取
        int startIndex = commentStart + "评论 :".length();

        // 跳过换行符和空格
        while (startIndex < text.length() && Character.isWhitespace(text.charAt(startIndex))) {
            startIndex++;
        }

        // 查找结束位置（到"► 介入方案"）
        int endIndex = text.indexOf("► 介入方案", startIndex);
        if (endIndex == -1) {
            // 如果没找到介入方案，就找到第二个"评论"或文档结束
            int secondComment = text.indexOf("评论 :", startIndex);
            if (secondComment != -1) {
                endIndex = secondComment;
            } else {
                endIndex = text.length();
            }
        }

        if (startIndex >= endIndex) {
            return "";
        }

        return text.substring(startIndex, endIndex).trim();
    }

    /**
     * 提取介入方案的内容
     * 从"► 介入方案"开始到第二个"评论 :"结束
     */
    public static String extractInterventionPlan(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 查找"► 介入方案"的位置
        int planStart = text.indexOf("► 介入方案");
        if (planStart == -1) {
            return "";
        }

        // 从介入方案标记后开始提取
        int startIndex = planStart + "► 介入方案".length();

        // 跳过换行符和空格
        while (startIndex < text.length() && Character.isWhitespace(text.charAt(startIndex))) {
            startIndex++;
        }

        // 查找结束位置（到第二个"评论 :"或文档结束）
        int firstCommentEnd = text.indexOf("► 介入方案");
        int secondComment = text.indexOf("评论 :", firstCommentEnd + "► 介入方案".length());

        int endIndex;
        if (secondComment != -1) {
            endIndex = secondComment;
        } else {
            endIndex = text.length();
        }

        if (startIndex >= endIndex) {
            return "";
        }

        return text.substring(startIndex, endIndex).trim().replace("No device selected","");
    }
}
