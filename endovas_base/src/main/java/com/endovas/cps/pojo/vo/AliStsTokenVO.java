package com.endovas.cps.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2023/8/30
 * @Time: 17:58
 */
@Getter
@Setter
@ApiModel("阿里sts token对象")
public class AliStsTokenVO {
    @ApiModelProperty(value = "AccessKeyId")
    public String accessKeyId;
    @ApiModelProperty("AccessKeySecret")
    public String accessKeySecret;
    @ApiModelProperty("SecurityToken")
    public String securityToken;
    private String bucket;
}
