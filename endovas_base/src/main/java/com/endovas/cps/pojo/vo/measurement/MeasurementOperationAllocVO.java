package com.endovas.cps.pojo.vo.measurement;

import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/19
 * @Time: 17:48
 */
@Getter
@Setter
public class MeasurementOperationAllocVO extends BaseVO {

    @ApiModelProperty(value = "是否需要踢别人")
    private Boolean needKickOther;

    @ApiModelProperty(value = "当前使用人")
    private String currentUser;
    @ApiModelProperty(value = "使用人所属部门/组织")
    private String currentUserOrg;
    @ApiModelProperty(value = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(value = "设备id")
    private String equipId;

}
