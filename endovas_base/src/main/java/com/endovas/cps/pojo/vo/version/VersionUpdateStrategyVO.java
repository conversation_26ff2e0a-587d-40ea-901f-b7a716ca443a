package com.endovas.cps.pojo.vo.version;

import com.endovas.cps.entity.version.VersionUpdateStrategy;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/29
 * Time: 下午2:36
 */
@Setter
@Getter
@ApiModel
public class VersionUpdateStrategyVO extends BaseVO implements BeanConvert<VersionUpdateStrategyVO, VersionUpdateStrategy> {
    @ApiModelProperty(value = "唯一标识")
    private String id;
    @ApiModelProperty(value = "最小版本")
    private String minVersion;
    @ApiModelProperty(value = "最大版本")
    private String maxVersion;


}
