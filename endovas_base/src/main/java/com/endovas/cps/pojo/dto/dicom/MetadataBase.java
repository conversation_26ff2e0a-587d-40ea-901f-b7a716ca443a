package com.endovas.cps.pojo.dto.dicom;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/7
 * @Time: 22:17
 */@Getter
@Setter
@Accessors(chain = true)
public class MetadataBase {
    public String manufacture;
    public Integer Columns;
    public Integer Rows;
    public Integer InstanceNumber;
    public String SOPClassUID;
    public Integer AcquisitionNumber;
    public String PhotometricInterpretation;
    public Integer BitsAllocated;
    public Integer BitsStored;
    public Integer PixelRepresentation;
    public Integer SamplesPerPixel;
    public List<Double> PixelSpacing;
    public Integer HighBit;
    public List<Double> ImageOrientationPatient;
    public List<Double> ImagePositionPatient;
    public String FrameOfReferenceUID;
    public List<String> ImageType;
    public String Modality;
    public String SOPInstanceUID;
    public String SeriesInstanceUID;
    public String StudyInstanceUID;
    public Integer RescaleIntercept;
    public Integer RescaleSlope;
    public String SeriesDate;
    public String AcquisitionDate;
    public String AcquisitionTime;

    public String StudyDate;
    public String SeriesTime;
    public String StudyTime;
    public String SliceThickness;
    public String PatientName;
    public String PatientID;
    public String PatientAge;
    public String PatientSex;
    public String AccessionNumber;
    public String SeriesDescription;
}
