package com.endovas.cps.pojo.vo.dashboard;

import com.endovas.cps.pojo.dto.dashboard.DicomMonthStats;
import com.endovas.cps.pojo.dto.dashboard.SurgeryTypeStats;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/8
 * Time: 16:48
 */
@Setter
@Getter
public class DicomSelfStatsVO extends BaseVO {
    @ApiModelProperty(value = "术前影像")
    private Long dicomAmt;
    @ApiModelProperty(value = "提交标注任务数")
    private Long submitLabelAmt;
    @ApiModelProperty(value = "术前影像大小[字节]")
    private Long dicomTotalSize;
    @ApiModelProperty(value = "病患档案")
    private Long patientAmt;
    @ApiModelProperty(value = "手术类型数量")
    private List<SurgeryTypeStats> surgeryTypeStats;
    @ApiModelProperty(value = "每月影像数量和任务数量")
    private List<DicomMonthStats> months;


}
