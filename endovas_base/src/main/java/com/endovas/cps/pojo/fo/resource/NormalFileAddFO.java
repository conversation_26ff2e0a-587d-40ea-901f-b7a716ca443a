package com.endovas.cps.pojo.fo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 13:59
 */
@Getter
@Setter
public class NormalFileAddFO {
    private String fileName;
    private Long fileSize;

    @ApiModelProperty(value = "病患ID")
    @NotBlank(message = "病患ID不能为空")
    private String patientId;
    @ApiModelProperty(value = "影像说明")
    @NotBlank(message = "影像说明不能为空")
    private String remark;

    @ApiModelProperty(value = "附件ID")
    @NotBlank(message = "附件ID不能为空")
    private String attachmentId;


}
