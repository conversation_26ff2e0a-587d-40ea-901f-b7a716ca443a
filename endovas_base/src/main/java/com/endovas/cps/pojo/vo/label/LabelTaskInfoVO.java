package com.endovas.cps.pojo.vo.label;

import com.endovas.cps.entity.label.LabelTask;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 16:40
 */
@Getter
@Setter
public class LabelTaskInfoVO extends BaseVO implements BeanConvert<LabelTaskInfoVO, LabelTask> {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "任务id")
    private String labelTaskId;
    @ApiModelProperty(value = "发起人")
    private String creator;
    @ApiModelProperty(value = "发起人类型")
    private String creatorType;
    @ApiModelProperty(value = "任务发起时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "领取人")
    private String labeler;
    @ApiModelProperty(value = "领取人类型")
    private String labelerType;
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;
    @ApiModelProperty(value = "任务完成时间")
    private LocalDateTime taskCompletedTime;
    @ApiModelProperty(value = "测量结果状态")
    private String resultSyncStatus;
    @ApiModelProperty(value = "影像id")
    private String dicomId;
    @ApiModelProperty(value = "影像名称")
    private String dicomName;
    @ApiModelProperty(value = "疾病类型")
    private String SurgeryTypeName;
    @ApiModelProperty(value = "影像类型")
    private String modality;
    @ApiModelProperty(value = "影像文件内容日期")
    private String dicomContentDate;
    @ApiModelProperty(value = "影像文件上传人")
    private String dicomUploader;
    @ApiModelProperty(value = "影像文件上传人类型")
    private String dicomUploaderType;
    @ApiModelProperty(value = "影像文件上传时间")
    private LocalDateTime dicomUploadDateTime;
    @ApiModelProperty(value = "任务类型")
    private String dataRangeType;
    @ApiModelProperty(value = "影像资料预览地址")
    private String viewJsonUrl;
}
