package com.endovas.cps.pojo.vo.patient;

import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2025/7/28
 * Time: 14:34
 */
@Setter
@Getter
public class LabTestReportVO extends BaseVO {
    @ApiModelProperty(value = "血常规")
    private AttachmentVO cbc;
    @ApiModelProperty(value = "尿常规")
    private AttachmentVO ua;
    @ApiModelProperty(value = "生化全套")
    private AttachmentVO bmp;
    @ApiModelProperty(value = "血气分析")
    private AttachmentVO abg;
    @ApiModelProperty(value = "传染病筛查")
    private AttachmentVO is;
    @ApiModelProperty(value = "心肌酶及心肌标志物")
    private AttachmentVO ck;
    @ApiModelProperty(value = "肌红蛋白")
    private AttachmentVO mb;
    @ApiModelProperty(value = "凝血功能")
    private AttachmentVO coag;
    @ApiModelProperty(value = "D-二聚体")
    private AttachmentVO d_dimer;
    @ApiModelProperty(value = "C-反应蛋白")
    private AttachmentVO crp;

}
