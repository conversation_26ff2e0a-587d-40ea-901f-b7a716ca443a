package com.endovas.cps.pojo.vo.dashboard;

import com.endovas.cps.pojo.dto.dashboard.DicomMonthStats;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/12/30
 * @Time: 15:10
 */
@Getter
@Setter
public class MedImageMonthStatVO extends BaseVO {
    @ApiModelProperty(value = "每月影像数量和任务数量")
    private List<DicomMonthStats> months;
}
