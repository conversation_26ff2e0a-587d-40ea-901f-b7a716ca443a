package com.endovas.cps.pojo.vo.platform.hospital;

import com.endovas.cps.pojo.vo.AttachmentVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 11:23
 */


@Getter
@Setter
@ToString
@NoArgsConstructor
public class HospitalSelectVO {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("医院名称")
    private String name;

    @ApiModelProperty("医院logo")
    private AttachmentVO logo;

}
