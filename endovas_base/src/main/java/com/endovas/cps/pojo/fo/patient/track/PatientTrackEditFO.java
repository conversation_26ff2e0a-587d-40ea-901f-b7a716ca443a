package com.endovas.cps.pojo.fo.patient.track;

import com.endovas.cps.entity.patient.PatientTrack;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/4
 * Time: 14:14
 */
@Setter
@Getter
public class PatientTrackEditFO implements BeanConvert<PatientTrackEditFO, PatientTrack> {
    @ApiModelProperty(value = "ID", required = true)
    @NotEmpty(message = "ID不能为空")
    private String id;
    @ApiModelProperty(value = "患者", required = true)
    @NotEmpty(message = "患者不能为空")
    private String patientId;
    @ApiModelProperty(value = "随访时间", required = true)
    @NotNull(message = "随访时间不能为空")
    private LocalDateTime trackTime;

    @ApiModelProperty(value = "随访对象", required = true)
    @NotEmpty(message = "随访对象不能为空")
    //随访对象
    private String trackObject;
    @ApiModelProperty(value = "随访方式", required = true)
    @NotEmpty(message = "随访方式不能为空")
    private String trackMethod;

    @ApiModelProperty(value = "随访内容", required = true)
    @NotEmpty(message = "随访内容不能为空")
    private String content;

    @ApiModelProperty(value = "附件", required = true)
    private String attachmentId;
}
