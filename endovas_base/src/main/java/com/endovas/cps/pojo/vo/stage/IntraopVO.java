package com.endovas.cps.pojo.vo.stage;

import com.endovas.cps.pojo.dto.meeting.VideoDeviceDTO;
import com.endovas.cps.pojo.dto.product.ProductListDTO;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/6
 * Time: 14:28
 */
@Getter
@Setter
public class IntraopVO extends BaseVO {
    @ApiModelProperty(value = "手术日期")
    private String surgeryDate;
    @ApiModelProperty(value = "手术开始时间")
    private LocalDateTime surgeryStartTime;
    @ApiModelProperty(value = " 手术结束时间")
    private LocalDateTime surgeryEndTime;
    @ApiModelProperty(value = "该疾病该医生场次")
    private Long sameSurgeryTypeSameHospitalUserAmt;

    @ApiModelProperty(value = "当前医生该疾病耗时(秒)")
    private Long sameSurgeryTypeSameHospitalUserUsedTime;
    @ApiModelProperty(value = "当前医院该疾病耗时(秒)")
    private Long sameSurgeryTypeSameHospitalUsedTime;
    @ApiModelProperty(value = "全国该疾病耗时(秒)")
    private Long sameSurgeryTypeUsedTime;

    @ApiModelProperty(value = "该场手术核心产品 ")
    private List<ProductListDTO> products;
    @ApiModelProperty(value = "术中影像")
    private List<VideoDeviceDTO> videos;

}
