package com.endovas.cps.pojo.vo.dashboard;

import com.endovas.cps.pojo.dto.dashboard.PatientTrackStats;
import com.endovas.cps.pojo.dto.dashboard.SurgeryTypeStats;
import com.endovas.cps.pojo.dto.dashboard.SurgeryTypeStatsMonth;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/6
 * Time: 15:26
 */
@Getter
@Setter
@ApiModel("平台仪表盘")
public class DashBoardVO extends BaseVO {

    @ApiModelProperty(value = "术前影像总数")
    private Long dicomTotalAmt;
    @ApiModelProperty(value = "术前测量方案总数")
    private Long measureTotalAmt;
    @ApiModelProperty(value = "术前影像大小[字节]")
    private Long dicomTotalSize;
    @ApiModelProperty(value = "患者档案总数")
    private Long patientTotalAmt;
    @ApiModelProperty(value = "随访总数")
    private Long patientTrackTotalAmt;
    @ApiModelProperty(value = "远程测量设备数")
    private Long equipmentTotalAmt;
    @ApiModelProperty(value = "远程测量设备空闲数")
    private Long equipmentIdleAmt;

    @ApiModelProperty(value = "手术量")
    private Long surgeryTotalAmt;

    @ApiModelProperty(value = "台车总数")
    private Long trolleyTotalAmt;
    @ApiModelProperty(value = "台车当前手术中总数")
    private Long trolleyMeetingAmt;

    @ApiModelProperty(value = "手术类型数量")
    private List<SurgeryTypeStats> dicomTotalSurgeryTypeStats;
    @ApiModelProperty(value = "每月手术类型数量")
    private List<SurgeryTypeStatsMonth> dicomMonthSurgeryTypeStats;

    @ApiModelProperty(value = "术后随访统计")
    private List<PatientTrackStats> patientTrackStats;


}
