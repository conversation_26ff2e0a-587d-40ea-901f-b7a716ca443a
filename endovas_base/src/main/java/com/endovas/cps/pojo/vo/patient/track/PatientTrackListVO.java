package com.endovas.cps.pojo.vo.patient.track;

import com.endovas.cps.entity.patient.PatientTrack;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/4
 * Time: 14:30
 */
@Setter
@Getter
public class PatientTrackListVO extends BaseVO implements BeanConvert<PatientTrackListVO, PatientTrack> {

    private String id;
    @ApiModelProperty(value = "随访时间")
    private LocalDateTime trackTime;

    @ApiModelProperty(value = "随访人")
    private String creator;
    @ApiModelProperty(value = "随访类型")
    private String creatorType;
    @ApiModelProperty(value = "疾病类型")
    private String surgeryTypeName;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    private String patientId;

    @ApiModelProperty(value = "手术时间")
    private String surgeryDate;

    @ApiModelProperty(value = "随访对象")
    private String trackObject;

    @ApiModelProperty(value = "随访方式")
    private String trackMethod;


    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


    private AttachmentVO attachment;

}
