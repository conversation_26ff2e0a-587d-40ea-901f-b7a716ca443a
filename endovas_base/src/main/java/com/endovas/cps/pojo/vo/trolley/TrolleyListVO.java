package com.endovas.cps.pojo.vo.trolley;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.trolley.Trolley;
import com.endovas.cps.enums.TrolleyStatusEnum;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:36
 */
@Setter
@Getter
@ApiModel
public class TrolleyListVO extends BaseVO implements BeanConvert<TrolleyListVO, Trolley> {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String id;
    @ApiModelProperty(value = "产品名称", required = true)
    private String productName;
    @ApiModelProperty(value = "UDI", required = true)
    private String udi;
    @ApiModelProperty(value =  "产品型号", required = true)
    private String modelName;
    @ApiModelProperty(value =  "规格名称", required = true)
    private String specName;
    @ApiModelProperty(value =  "系统版本")
    private String sysVer;
    @ApiModelProperty(value =  "跟台版本")
    private String softVer;
    @ApiModelProperty(value =  "服务版本")
    private String serverVer;
    @ApiModelProperty(value =  "生产日期")
    private String prodDate;
    @ApiModelProperty(value =  "备注")
    private String remark;

    @ApiModelProperty(value =  "状态")
    private String status;
    @ApiModelProperty(value =  "最后一次修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value =  "绑定医院名称")
    private String bindHospitalName;
    @ApiModelProperty(value =  "是否有移机日志")
    private boolean hasTransferLog;
    @ApiModelProperty(value =  "使用状态")
    private String useStatus;
    @ApiModelProperty(value =  "导管室名称")
    private String cathRoomName;


    @Override
    public TrolleyListVO convertFrom(Trolley input) {
        BeanUtil.copyProperties(input, this);
        input.setStatus(TrolleyStatusEnum.get(input.getStatus()).getDesc());
        return this;
    }
}
