package com.endovas.cps.pojo.fo.measurement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 16:31
 */
@Data
@ApiModel(value = "编辑测量任务")
public class MeasurementTaskEditFO {
    @ApiModelProperty(value = "id", required = true)
    @NotEmpty(message = "id不能为空")
    private String id;
    @ApiModelProperty(value = "任务类型")
    @NotEmpty(message = "任务类型不能为空")
    private String dataRangeType;
}
