package com.endovas.cps.pojo.vo.dashboard;

import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2024/12/30
 * @Time: 15:38
 */
@Getter
@Setter
public class TodoListStatVO extends BaseVO {
    @ApiModelProperty(value = "未完结的测量任务")
    private Long unCompletedMeasurementAmt;
    @ApiModelProperty(value = "可领取的测量任务")
    private Long unclaimedMeasurementAmt;
    @ApiModelProperty(value = "未创建患者档案")
    private Long unCreatedPatientAmt;
    @ApiModelProperty(value = "未上传术后影像")
    private Long unUploadedPostOpDicomAmt;
    @ApiModelProperty(value = "未随访患者病情")
    private Long unCreatedPatientTrackAmt;
}
