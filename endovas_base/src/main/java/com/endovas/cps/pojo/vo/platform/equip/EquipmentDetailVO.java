package com.endovas.cps.pojo.vo.platform.equip;

import com.endovas.cps.entity.platform.Equipment;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2024/11/18
 * @Time: 17:57
 */
@Setter
@Getter
@ApiModel
public class EquipmentDetailVO extends BaseVO implements BeanConvert<EquipmentDetailVO, Equipment> {

    private String id;
    private String name;
    private String softName;
    private String softVer;
    private String os;
    private String osVer;
    private String organizationId;

    private String status;

    private String protocol;
    private String hostname;
    private String port;
    private String username;
    private String password;
}
