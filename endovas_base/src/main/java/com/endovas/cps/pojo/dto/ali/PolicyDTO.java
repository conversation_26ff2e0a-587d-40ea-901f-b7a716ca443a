package com.endovas.cps.pojo.dto.ali;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wk
 * @Date: 2023/8/30
 * @Time: 17:28
 */
@Getter
@Setter
public class PolicyDTO {
    private String Version = "1";
    private List<StatementDTO> Statement;

    public static PolicyDTO createPolicyDTO(String... resource) {

        List<String> resDataList = new ArrayList<>();
        for (String s : resource) {
            resDataList.add("acs:oss:*:*:" + s);
        }
        PolicyDTO dto = new PolicyDTO();
        List<StatementDTO> statList = new ArrayList<>();
        StatementDTO stat = new StatementDTO();
        stat.setResource(resDataList);
        statList.add(stat);

        dto.setStatement(statList);
        return dto;
    }
}
