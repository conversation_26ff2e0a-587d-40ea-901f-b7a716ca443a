package com.endovas.cps.pojo.fo.measurement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2024/11/18
 * @Time: 17:30
 */
@Getter
@Setter
public class MeasurementOperationParamFO {
    @ApiModelProperty(value = "测量任务id", required = true)
    private String measurementId;
    @ApiModelProperty(value = "使用软件", required = true)
    private String softName;

    @ApiModelProperty(value = "设备id", required = true)
    private String equipId;
    @ApiModelProperty(value = "测量记录id", required = true)
    private String measureOpRecordId;
}
