package com.endovas.cps.pojo.fo.resource.dicom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wk
 * @Date: 2023/8/30
 * @Time: 18:02
 */
@Getter
@Setter
public class DicomAddFO {
    @ApiModelProperty(value = "文件名称")
    private String fileName;
    @ApiModelProperty(value = "疾病类型")
    @NotEmpty(message = "疾病类型不能为空")
    private String surgeryTypeId;
    @ApiModelProperty(value = "医院名称")
    @NotEmpty(message = "医院名称不能为空")
    private String hospitalId;

    private String password;
    @ApiModelProperty(value = "关联患者")
    private String patientId;
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "脱敏字段")
    private List<String> sensitiveFields;

    private String surgeryStage;


}
