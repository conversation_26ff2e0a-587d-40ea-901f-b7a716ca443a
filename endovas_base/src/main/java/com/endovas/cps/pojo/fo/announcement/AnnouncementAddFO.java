package com.endovas.cps.pojo.fo.announcement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:28
 */
@Data
@ApiModel("新建公告")
public class AnnouncementAddFO {
    @NotBlank(message = "请输入标题")
    @ApiModelProperty("名字")
    private String title;
    @Size(max = 255, min = 1, message = "内容长度限制在1-255")
    @NotBlank(message = "请输入内容")
    @ApiModelProperty("内容")
    private String content;

}
