package com.endovas.cps.pojo.vo.patient;

import com.endovas.cps.entity.patient.Patient;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:51
 */
@Setter
@Getter
public class PatientListVO extends BaseVO implements BeanConvert<PatientListVO, Patient> {
    private String id;
    private String name;
    private String surgeryTypeId;
    private String surgeryTypeName;

    private String pid;
    private String hospitalId;
    private String hospitalName;

    private String age;

    @ApiModelProperty(value = "是否有术前影像")
    private Boolean hasPreopImg;

    @ApiModelProperty(value = "术前测量方案")
    private Boolean hasPreopMPlan;

    @ApiModelProperty(value = "手术时间")
    private String surgeryDate;

    @ApiModelProperty(value = "是否有术中影像")
    private Boolean hasIntraopImg;
    @ApiModelProperty(value = "是否有术后影像")
    private Boolean hasPostopImg;

    @ApiModelProperty(value = "是否有术后随访")
    private Boolean hasTrack;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人类型")
    private String creatorType;

    @ApiModelProperty(value = "主治医生")
    private String hospitalUserName;

    //体重
    private Integer weight;
    //身高
    private Integer height;


    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

}
