package com.endovas.cps.pojo.dto.measurement;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 测量任务结果（记录需要测量的任务内容）
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:16
 */
@Getter
@Setter
public class MeasurementTaskResultDTO {

    private String id;
    private String measurementId; //  测量任务ID
    private String sourceType; // MeasureSourceTypeEnum.UPLOAD  MeasureSourceTypeEnum.LOCAL
    private String resultFileName;
    private LocalDateTime updateTime;

    public MeasurementTaskResultDTO() {
    }

    public MeasurementTaskResultDTO(String id, String measurementId, String sourceType, String resultFileName, LocalDateTime updateTime) {
        this.id = id;
        this.measurementId = measurementId;
        this.sourceType = sourceType;
        this.resultFileName = resultFileName;
        this.updateTime = updateTime;
    }
}
