package com.endovas.cps.pojo.fo.trolley;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:32
 */
@Setter
@Getter
@ApiModel
public class TrolleySearchFO {
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "软件版本")
    private String softVer;
    @ApiModelProperty(value = "状态",notes = "已安装:INSTALLED、库存现货:SPOTS")
    private String status;

    @Column(columnDefinition = "COMMENT '规格'")
    private String specName;
}
