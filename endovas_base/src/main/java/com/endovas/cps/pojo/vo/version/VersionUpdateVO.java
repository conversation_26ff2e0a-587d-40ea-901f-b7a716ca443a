package com.endovas.cps.pojo.vo.version;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/4/3
 * Time: 11:53
 */
@Setter
@Getter
public class VersionUpdateVO {
    @ApiModelProperty("是否需要更新")
    private Boolean shouldUpdate;
    @ApiModelProperty("最新版本")
    private String latestVersion;

    @ApiModelProperty("下载地址")
    private String downloadUrl;

    @ApiModelProperty("弹窗标题")
    private String title;
    @ApiModelProperty("弹窗内容")
    private String content;


    @ApiModelProperty("取消文本内容")
    private String cancelText;
    @ApiModelProperty("确认文本内容")
    private String confirmText;
    @ApiModelProperty("升级方式")
    private String upgradeMethod;


}
