package com.endovas.cps.pojo.vo.platform.hospital;

import com.endovas.cps.entity.hospital.Hospital;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:41
 */
@Getter
@Setter
@ApiModel
public class HospitalListVO extends BaseVO implements BeanConvert<HospitalListVO, Hospital> {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String id;
    @ApiModelProperty(value = "医院名称", required = true)
    private String name;
    @ApiModelProperty(value = "医院等级")
    private String level;
    @ApiModelProperty(value = "所属国家")
    private String country;
    @ApiModelProperty(value = "所属省份")
    private String province;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "联系方式")
    private String contactInfo;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty("最后一次修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("台车部署数量")
      private long trolleyAmount;
    @ApiModelProperty("代理商")
    private String medAgentName;
    @ApiModelProperty("总账号数")
    private long hospitalUserAmount;

    private AttachmentVO logo;

}
