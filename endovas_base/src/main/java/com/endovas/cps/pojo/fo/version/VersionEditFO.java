package com.endovas.cps.pojo.fo.version;

import com.endovas.cps.entity.version.Version;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/4/3
 * Time: 10:26
 */
@Setter
@Getter
public class VersionEditFO implements BeanConvert<VersionEditFO, Version> {
    @ApiModelProperty(value = "唯一ID", required = true)
    @NotEmpty(message = "唯一ID不能为空")
    private String id;

    @ApiModelProperty(value = "应用类型", required = true)
    @NotEmpty(message = "应用类型不能为空")
    private String terminalType;

    @ApiModelProperty(value = "终端系统", required = true)
    @NotEmpty(message = "终端系统不能为空")
    private String os;

    @ApiModelProperty(value = "版本名称", required = true)
    @NotEmpty(message = "版本名称不能为空")
    private String softVersion;


    @ApiModelProperty(value = "弹窗标题", required = true)
    @NotEmpty(message = "弹窗标题不能为空")
    private String title;

    @ApiModelProperty(value = "弹窗内容", required = true)
    @NotEmpty(message = "弹窗内容不能为空")
    private String content;

    @ApiModelProperty(value = "取消文本内容", required = true)
    @NotEmpty(message = "取消文本内容不能为空")
    private String cancelText;

    @ApiModelProperty(value = "确认文本内容", required = true)
    @NotEmpty(message = "确认文本内容不能为空")
    private String confirmText;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "下载地址")
    private String downloadUrl;

    @ApiModelProperty(value = "策略生效时间", required = true)
    @NotNull(message = "策略生效时间不能为空")
    private LocalDateTime applyTime;


    @ApiModelProperty(value = "升级方式", required = true)
    @NotEmpty(message = "升级方式不能为空")
    private String upgradeMethod;

    @ApiModelProperty(value = "升级范围", required = true)
    @NotEmpty(message = "升级范围不能为空")
    private String upgradeScope;

    @ApiModelProperty(value = "版本更新策略")
    private List<VersionStrategyAddFO> upgradeStrategy;

}
