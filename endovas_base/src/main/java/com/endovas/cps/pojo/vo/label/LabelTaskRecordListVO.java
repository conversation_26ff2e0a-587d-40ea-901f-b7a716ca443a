package com.endovas.cps.pojo.vo.label;

import com.endovas.cps.entity.label.LabelTaskRecord;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:50
 */
@Getter
@Setter
public class LabelTaskRecordListVO extends BaseVO implements BeanConvert<LabelTaskRecordListVO, LabelTaskRecord> {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "操作内容")
    private String content;
    @ApiModelProperty(value = "操作人")
    private String operater;
    @ApiModelProperty(value = "操作人类型")
    private String operaterType;
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime createTime;
}
