package com.endovas.cps.pojo.fo.trolley;

import com.endovas.cps.entity.trolley.Trolley;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:44
 */
@Data
@ApiModel(value = "添加台车")
public class TrolleyAddFO implements BeanConvert<TrolleyAddFO, Trolley> {
    @ApiModelProperty(value = "产品名称", required = true)
    @NotEmpty(message = "产品名称不能为空")
    private String productName;
    @ApiModelProperty(value = "UDI", required = true)
    @NotEmpty(message = "UDI不能为空")
    private String udi;
    @ApiModelProperty(value = "产品型号", required = true)
    @NotEmpty(message = "产品型号不能为空")
    private String modelName;
    @ApiModelProperty(value = "规格名称", required = true)
    @NotEmpty(message = "规格名称不能为空")
    private String specName;
    @ApiModelProperty(value = "系统版本")
    private String sysVer;
    @ApiModelProperty(value = "跟台版本")
    private String softVer;
    @ApiModelProperty(value = "服务版本")
    private String serverVer;
    @ApiModelProperty(value = "生产日期")
    private String prodDate;
    @ApiModelProperty(value = "备注")
    private String remark;
}
