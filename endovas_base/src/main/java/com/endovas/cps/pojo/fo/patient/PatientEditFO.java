package com.endovas.cps.pojo.fo.patient;

import com.endovas.cps.entity.patient.Patient;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:43
 */
@Setter
@Getter
public class PatientEditFO implements BeanConvert<PatientEditFO, Patient> {

    @ApiModelProperty(value = "唯一ID", required = true)
    @NotEmpty(message = "唯一ID不能为空")
    private String id;
    @ApiModelProperty(value = "患者姓名", required = true)
    @NotEmpty(message = "患者姓名不能为空")
    private String name;
    private String pid;
    @ApiModelProperty(value = "患者年龄", required = true)
    @NotNull(message = "患者年龄不能为空")
    private Integer age;
    private String gender;
    private Integer weight;
    private Integer height;

    @ApiModelProperty(value = "疾病类型", required = true)
    @NotEmpty(message = "疾病类型不能为空")
    private String surgeryTypeId;

    @ApiModelProperty(value = "医院名称", required = true)
    @NotEmpty(message = "医院名称不能为空")
    private String hospitalId;
    private String remark;
}
