package com.endovas.cps.pojo.vo.resource;

import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/5
 * Time: 14:25
 */
@Getter
@Setter
@ApiModel("普通文件列表")
public class NormalFileListVO extends BaseVO {
    private String id;
    private String name;
    private String remark;

    @ApiModelProperty(value = "上传人")
    private String creator;
    @ApiModelProperty(value = "上传人类型")
    private String creatorType;
    @ApiModelProperty(value = "上传时间")
    private LocalDateTime createTime;
    private AttachmentVO attachment;
}
