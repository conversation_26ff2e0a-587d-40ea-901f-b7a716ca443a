package com.endovas.cps.pojo.fo.label;

import com.endovas.cps.entity.label.LabelTask;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:52
 */
@Data
@ApiModel(value = "新建测量任务")
public class LabelTaskAddFO implements BeanConvert<LabelTaskAddFO, LabelTask> {

    @ApiModelProperty(value = "影像id", required = true)
    @NotEmpty(message = "影像id不能为空")
    private String dicomId;
    @ApiModelProperty(value = "任务类型", required = true)
    @NotEmpty(message = "任务类型不能为空")
    private String dataRangeType;
    @ApiModelProperty(value = "患者id", required = true)
    @NotEmpty(message = "患者id不能为空")
    private String patientId;
    @ApiModelProperty(value = "疾病类型id", required = true)
    @NotEmpty(message = "疾病类型id不能为空")
    private String surgeryTypeId;
    @ApiModelProperty(value = "医院id", required = true)
    @NotEmpty(message = "医院id不能为空")
    private String hospitalId;



}
