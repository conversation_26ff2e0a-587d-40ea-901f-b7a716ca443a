package com.endovas.cps.pojo.fo.equipment;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.platform.Equipment;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:44
 */
@Data
@ApiModel(value = "添加台车")
public class EquipmentEditFO implements BeanConvert<EquipmentEditFO, Equipment> {
    @ApiModelProperty(value = "唯一ID", required = true)
    @NotEmpty(message = "唯一ID不能为空")
    private String id;
    @ApiModelProperty(value = "设备名称", required = true)
    @NotEmpty(message = "设备名称不能为空")
    private String name;
    @ApiModelProperty(value = "医疗软件", required = true)
    @NotEmpty(message = "医疗软件不能为空")
    private String softName;
    @ApiModelProperty(value = "版本", required = true)
    @NotEmpty(message = "版本不能为空")
    private String softVer;
    private String os;
    private String osVer;
    private String serverUrl;
    private String organizationId;

    @Override
    public EquipmentEditFO convertFrom(Equipment input) {
        BeanUtil.copyProperties(input, this, MysqlBase.FIELD_ID);
        return this;
    }
}
