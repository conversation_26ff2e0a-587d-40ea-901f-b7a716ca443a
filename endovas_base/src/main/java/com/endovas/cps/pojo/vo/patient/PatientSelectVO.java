package com.endovas.cps.pojo.vo.patient;

import com.endovas.cps.entity.patient.Patient;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:51
 */
@Setter
@Getter
public class PatientSelectVO extends BaseVO implements BeanConvert<PatientSelectVO, Patient> {
    private String id;
    private String name;

}
