package com.endovas.cps.pojo.fo.agent;

import com.endovas.cps.entity.agent.MedAgent;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 15:04
 */
@Data
@ApiModel(value = "添加医疗器械公司")
public class MedAgentAddFO implements BeanConvert<MedAgentAddFO, MedAgent> {
    @ApiModelProperty(value = "代理商名称", required = true)
    @NotEmpty(message = "代理商名称不能为空")
    private String name;

    @ApiModelProperty(value = "所属国家", required = true)
    @NotEmpty(message = "所属国家不能为空")
    private String countryCode;

    @ApiModelProperty(value = "省份", required = true)
    @NotEmpty(message = "省份不能为空")
    private String provinceCode;

    @ApiModelProperty(value = "联系方式")
    private String contactInfo;
    @ApiModelProperty(value = "备注")
    private String remark;



}
