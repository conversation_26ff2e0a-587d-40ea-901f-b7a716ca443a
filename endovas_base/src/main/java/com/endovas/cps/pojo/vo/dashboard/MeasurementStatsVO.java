package com.endovas.cps.pojo.vo.dashboard;

import com.endovas.cps.pojo.dto.dashboard.SurgeryTypeStats;
import com.endovas.cps.pojo.dto.dashboard.measurement.MeasurementStatsMonth;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/18
 * Time: 16:14
 */
@Setter
@Getter
public class MeasurementStatsVO extends BaseVO {

    @ApiModelProperty(value = "可领取任务总计")
    private Long unclaimedMeasurementTotalAmt;
    @ApiModelProperty(value = "完成测量总计")
    private Long completedMeasurementTotalAmt;
    @ApiModelProperty(value = "放弃任务总计")
    private Long giveUpMeasurementTotalAmt;
    @ApiModelProperty(value = "领取任务总计")
    private Long alreadyClaimedMeasurementTotalAmt;

    @ApiModelProperty(value = "手术类型数量")
    private List<SurgeryTypeStats> surgeryTypeStats;

    @ApiModelProperty(value = "测量趋势数量")
    private List<MeasurementStatsMonth> measurementStatsMonths;

    @ApiModelProperty(value = "完成测量任务影像来源医院排行")
    private List<RankVO> ranks;

    @ApiModelProperty(value = "完成的测量任务患者用户画像")
    private PatientStatVO patientStat;

}
