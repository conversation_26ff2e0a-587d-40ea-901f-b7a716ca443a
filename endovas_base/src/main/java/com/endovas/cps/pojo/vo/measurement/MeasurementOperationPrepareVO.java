package com.endovas.cps.pojo.vo.measurement;

import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2024/11/18
 * @Time: 18:41
 */
@Getter
@Setter
public class MeasurementOperationPrepareVO extends BaseVO {
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "测量操作记录id")
    private String measureOpRecordId;
    @ApiModelProperty(value = "连接measureOpToken")
    private String measureOpToken;
}
