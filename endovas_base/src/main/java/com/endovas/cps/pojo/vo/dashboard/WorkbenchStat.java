package com.endovas.cps.pojo.vo.dashboard;

import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/12/30
 * @Time: 14:51
 */
@Getter
@Setter
public class WorkbenchStat extends BaseVO {
    @ApiModelProperty(value = "术前影像")
    private Long dicomAmt;
    @ApiModelProperty(value = "术前影像大小[字节]")
    private Long dicomTotalSize;
    @ApiModelProperty(value = "测量影像任务数")
    private Long measurementCompletedAmt;
    @ApiModelProperty(value = "创建病患档案数量")
    private Long patientAmt;

    @ApiModelProperty(value = "疾病类型占比")
    private List<DiseaseTypeVO> diseaseTypeRatio;
}
