package com.endovas.cps.pojo.vo.version;

import com.endovas.cps.entity.version.Version;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:16
 */
@Setter
@Getter
@ApiModel
public class VersionListVO extends BaseVO implements BeanConvert<VersionListVO, Version> {

    @ApiModelProperty(value = "唯一ID")
    private String id;
    @ApiModelProperty(value = "终端类型")
    private String terminalType;
    @ApiModelProperty(value = "版本号")
    private String softVersion;
    @ApiModelProperty(value = "操作系统")
    private String os;
    @ApiModelProperty(value = "升级策略")
    private String upgradeScope;
    @ApiModelProperty(value = "升级方式")
    private String upgradeMethod;

    @ApiModelProperty(value = "生效时间")
    private LocalDateTime applyTime;

    private String title;//弹窗标题
    private String content;//弹窗内容
    private String cancelText;//取消文本内容
    private String confirmText;//确认文本内容
    private String remark;//备注
    private String downloadUrl;
    private String attachmentName;

    @ApiModelProperty(value = "生效状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "是否可用")
    private Boolean available;


}
