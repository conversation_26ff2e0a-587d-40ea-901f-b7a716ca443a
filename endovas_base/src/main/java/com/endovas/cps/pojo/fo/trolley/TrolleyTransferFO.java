package com.endovas.cps.pojo.fo.trolley;

import com.endovas.cps.entity.trolley.TrolleyTransfer;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 11:45
 */
@Data
@ApiModel(value = "移机")
public class TrolleyTransferFO implements BeanConvert<TrolleyTransferFO, TrolleyTransfer> {
    @ApiModelProperty(value = "台车ID", required = true)
    @NotEmpty(message = "台车不能为空")
    private String trolleyId;
    @ApiModelProperty(value = "当前医院", required = true)
    @NotEmpty(message = "当前医院不能为空")
    private String currentHospitalId;
    @ApiModelProperty(value = "转移至医院", required = true)
    @NotEmpty(message = "转移至医院不能为空")
    private String toHospitalId;

    @ApiModelProperty(value = "转移至所属导管室")
    private String toCathRoomId;


    private String installedDate;
    private String transferDate;
}
