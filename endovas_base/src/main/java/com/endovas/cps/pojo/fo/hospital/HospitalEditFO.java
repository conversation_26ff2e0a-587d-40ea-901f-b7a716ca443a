package com.endovas.cps.pojo.fo.hospital;

import com.endovas.cps.entity.hospital.Hospital;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 15:04
 */
@Data
@ApiModel(value = "添加医院公司")
public class HospitalEditFO implements BeanConvert<HospitalEditFO, Hospital> {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String id;
    @ApiModelProperty(value = "医院名称", required = true)
    @NotEmpty(message = "医院名称不能为空")
    private String name;
    @ApiModelProperty(value = "医院等级")
    private String levelCode;

    @ApiModelProperty(value = "所属国家", required = true)
    @NotEmpty(message = "所属国家不能为空")
    private String countryCode;

    @ApiModelProperty(value = "省份", required = true)
    @NotEmpty(message = "省份不能为空")
    private String provinceCode;


    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "联系方式")
    private String contactInfo;
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "代理商名称ID")
    private String medAgentId;


    @ApiModelProperty(value = "Logo", required = true)
    private String logoAttachmentId;

}
