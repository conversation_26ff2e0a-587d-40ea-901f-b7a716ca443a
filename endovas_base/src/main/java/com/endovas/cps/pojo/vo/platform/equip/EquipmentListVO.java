package com.endovas.cps.pojo.vo.platform.equip;

import cn.hutool.core.bean.BeanUtil;
import com.endovas.cps.entity.platform.Equipment;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:36
 */
@Setter
@Getter
@ApiModel
public class EquipmentListVO extends BaseVO implements BeanConvert<EquipmentListVO, Equipment> {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String id;
    @ApiModelProperty(value = "设备名称", required = true)
    private String name;
    @ApiModelProperty(value = "医疗软件", required = true)
    private String softName;
    @ApiModelProperty(value = "版本", required = true)
    private String softVer;
    @ApiModelProperty(value = "设备系统")
    private String os;
    @ApiModelProperty(value = "系统版本")
    private String osVer;
    private String serverUrl;
    @ApiModelProperty(value = "归属组织")
    private String organizationName;
    @ApiModelProperty(value = "资产创建时间", required = true)
    private LocalDateTime createTime;


    @Override
    public EquipmentListVO convertFrom(Equipment input) {
        BeanUtil.copyProperties(input, this);
        return this;
    }
}
