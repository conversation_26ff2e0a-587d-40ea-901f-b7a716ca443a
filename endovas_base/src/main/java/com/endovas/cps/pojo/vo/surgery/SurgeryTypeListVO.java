package com.endovas.cps.pojo.vo.surgery;

import com.endovas.cps.entity.surgery.SurgeryType;
import com.google.common.collect.Lists;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/7/15
 * Time: 15:22
 */

@Getter
@Setter
@ApiModel("疾病分类列表")
public class SurgeryTypeListVO extends BaseVO implements BeanConvert<SurgeryTypeListVO, SurgeryType> {
    private String id;
    private String parentId;
    private String name;
    private Integer serialNumber;
    private String nameWithParent;
    private Boolean active;
    private List<SurgeryTypeListVO> children = Lists.newArrayList();

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
