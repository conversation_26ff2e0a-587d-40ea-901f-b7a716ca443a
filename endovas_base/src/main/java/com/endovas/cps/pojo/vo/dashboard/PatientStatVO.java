package com.endovas.cps.pojo.vo.dashboard;

import com.endovas.cps.pojo.dto.dashboard.PatientAgeStats;
import com.endovas.cps.pojo.dto.dashboard.PatientLocationStats;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/11
 * Time: 15:37
 */
@Setter
@Getter
public class PatientStatVO extends BaseVO {
    @ApiModelProperty(value = "男性总数")
    private Long maleTotalAmt;
    @ApiModelProperty(value = "女性总数")
    private Long femaleTotalAmt;

    private List<PatientAgeStats> ageStats;

    private List<PatientLocationStats> locationStats;
}
