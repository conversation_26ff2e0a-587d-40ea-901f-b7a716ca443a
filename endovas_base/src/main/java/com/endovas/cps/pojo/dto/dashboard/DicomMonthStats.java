package com.endovas.cps.pojo.dto.dashboard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/8
 * Time: 16:54
 */
@Setter
@Getter
public class DicomMonthStats {
    @ApiModelProperty(value = "影像数量")
    private Long dicomAmt;
    @ApiModelProperty(value = "发布的AI标注任务数量")
    private Long submitMeasureAmt;

    @ApiModelProperty(value = "胸主动脉影像数量")
    private Long thoracicAortaAmt;
    @ApiModelProperty(value = "腹主动脉影像数量")
    private Long abdominalAortaAmt;
    @ApiModelProperty(value = "月份")
    private Long month;
}
