package com.endovas.cps.pojo.vo.measurement;

import com.endovas.cps.entity.measurement.MeasurementOperationRecord;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2024/11/20
 * @Time: 18:07
 */
@Getter
@Setter
public class MeasurementOperationDetailVO extends BaseVO implements BeanConvert<MeasurementOperationDetailVO, MeasurementOperationRecord> {
    private String id;
    private String screenRecordFileName; // 屏幕录像文件名称
}
