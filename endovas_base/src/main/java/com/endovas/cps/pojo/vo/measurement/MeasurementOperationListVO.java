package com.endovas.cps.pojo.vo.measurement;

import com.endovas.cps.entity.measurement.MeasurementOperationRecord;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/19
 * @Time: 14:45
 */
@Getter
@Setter
public class MeasurementOperationListVO extends BaseVO implements BeanConvert<MeasurementOperationListVO, MeasurementOperationRecord> {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "测量时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "测量人")
    private String measurer;
    @ApiModelProperty(value = "测量人类型")
    private String measurerType;

}
