package com.endovas.cps.pojo.vo.platform.hospital;

import com.endovas.cps.entity.hospital.CathRoom;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/8/14
 * Time: 14:30
 */
@Getter
@Setter
@ApiModel
public class CathRoomListVO extends BaseVO implements BeanConvert<CathRoomListVO, CathRoom> {

    private String id;
    private String name;
    @ApiModelProperty(value = "位置", required = true)
    private String location;
    @ApiModelProperty(value = "dsa设备")
    private String dsaDevice;
    @ApiModelProperty(value = "启用/废弃")
    private Boolean available;
    @ApiModelProperty(value = "状态", required = true)
    private String status;

    private List<AttachmentVO> attachments;
}
