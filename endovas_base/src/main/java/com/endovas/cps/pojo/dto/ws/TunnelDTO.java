package com.endovas.cps.pojo.dto.ws;

import lombok.Getter;
import lombok.Setter;

import javax.websocket.Session;

/**
 * @author: wk
 * @Date: 2025/1/2
 * @Time: 16:20
 */
@Getter
@Setter
public class TunnelDTO {
    // 用户id
    private String userId;
    private Session session;

    public TunnelDTO() {
    }

    public TunnelDTO(String userId, Session session) {
        this.userId = userId;
        this.session = session;
    }
}
