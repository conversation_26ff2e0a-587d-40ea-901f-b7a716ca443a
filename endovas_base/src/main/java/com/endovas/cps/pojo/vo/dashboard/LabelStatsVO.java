package com.endovas.cps.pojo.vo.dashboard;

import com.endovas.cps.pojo.dto.dashboard.label.LabelStatsMonth;
import com.endovas.cps.pojo.dto.dashboard.SurgeryTypeStats;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/12
 * Time: 19:58
 */
@Setter
@Getter
public class LabelStatsVO extends BaseVO {
    @ApiModelProperty(value = "可领取任务总计")
    private Long unclaimedLabelTotalAmt;
    @ApiModelProperty(value = "完成标注总计")
    private Long completedLabelTotalAmt;
    @ApiModelProperty(value = "放弃任务总计")
    private Long giveUpLabelTotalAmt;
    @ApiModelProperty(value = "已领取任务总计")
    private Long alreadyClaimedLabelTotalAmt;

    @ApiModelProperty(value = "手术类型数量")
    private List<SurgeryTypeStats> surgeryTypeStats;

    @ApiModelProperty(value = "标注趋势数量")
    private List<LabelStatsMonth> labelStatsMonths;


}
