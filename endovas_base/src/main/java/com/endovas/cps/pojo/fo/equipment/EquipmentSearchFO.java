package com.endovas.cps.pojo.fo.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:32
 */
@Setter
@Getter
@ApiModel
public class EquipmentSearchFO {
    @ApiModelProperty(value = "设备名称")
    private String name;
    @ApiModelProperty(value = "医疗软件")
    private String softName;
    @ApiModelProperty(value = "设备状态")
    private String status;
    @ApiModelProperty(value = "设备系统")
    private String os;
    @ApiModelProperty(value = "归属组织")
    private String organizationId;

}
