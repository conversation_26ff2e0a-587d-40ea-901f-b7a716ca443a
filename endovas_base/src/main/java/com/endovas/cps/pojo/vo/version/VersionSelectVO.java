package com.endovas.cps.pojo.vo.version;

import com.endovas.cps.entity.version.Version;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:16
 */
@Setter
@Getter
@ApiModel
public class VersionSelectVO extends BaseVO implements BeanConvert<VersionSelectVO, Version> {
    @ApiModelProperty(value = "版本号")
    private String softVersion;
}
