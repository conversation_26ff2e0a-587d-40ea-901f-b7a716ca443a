package com.endovas.cps.pojo.vo.platform.agent;

import com.endovas.cps.entity.agent.MedAgent;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 17:27
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class MedAgentListVO extends BaseVO implements BeanConvert<MedAgentListVO, MedAgent> {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("代理商名称")
    private String name;
    @ApiModelProperty("联系方式")
    private String contactInfo;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("台车分配数量")
    private long trolleyAmount;
    @ApiModelProperty("最后一次修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("国家")
    private String country;
    @ApiModelProperty("省份")
    private String province;


}
