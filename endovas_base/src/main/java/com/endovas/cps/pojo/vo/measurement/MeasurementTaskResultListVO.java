package com.endovas.cps.pojo.vo.measurement;

import com.endovas.cps.pojo.dto.measurement.MeasurementTaskResultDTO;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/20
 * @Time: 15:47
 */
@Getter
@Setter
public class MeasurementTaskResultListVO extends BaseVO implements BeanConvert<MeasurementTaskResultListVO, MeasurementTaskResultDTO> {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "测量任务ID")
    private String measurementId;
    @ApiModelProperty(value = "测量任务结果")
    private String result;
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty(value = "结果来源")
    private String sourceType;
    @ApiModelProperty(value = "报告名称")
    private String resultFileName;

    @ApiModelProperty(value = "报告文件")
    private AttachmentVO attachment;


}
