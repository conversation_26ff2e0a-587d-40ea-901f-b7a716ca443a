package com.endovas.cps.pojo.vo.patient;

import com.endovas.cps.entity.patient.Patient;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/10/29
 * Time: 14:51
 */
@Setter
@Getter
public class PatientDetailVO extends BaseVO implements BeanConvert<PatientDetailVO, Patient> {
    private String id;
    private String name;
    private String pid;
    private String age;
    private String gender;
    //体重
    private Integer weight;
    //身高
    private Integer height;
    private String surgeryTypeName;
    private String surgeryTypeId;
    private String hospitalName;
    private String hospitalId;

    @ApiModelProperty(value = "主治医生")
    private String hospitalUserName;

    @ApiModelProperty(value = "创建人")
    private String creator;
    @ApiModelProperty(value = "手术时间")
    private String surgeryDate;
    @ApiModelProperty(value = "创建人类型")
    private String creatorType;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    private String remark;

    @ApiModelProperty(value = "患者主诉")
    private String chiefComplaint;
    @ApiModelProperty(value = "现病史")
    private String presentIllnessHistory;
    @ApiModelProperty(value = "既往史")
    private String pastMedicalHistory;

}
