package com.endovas.cps.pojo.fo.measurement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 14:14
 */
@Data
@ApiModel(value = "新建测量任务")
public class MeasurementTaskSearchFO {

    @ApiModelProperty(value = "任务id")
    private String measurementTaskId;
    @ApiModelProperty(value = "疾病类型id")
    private String surgeryTypeId;
    @ApiModelProperty(value = "发起人")
    private String creator;
    @ApiModelProperty(value = "领取人")
    private String measurer;
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;
    @ApiModelProperty(value = "医院Id")
    private String hospitalId;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "患者Id")
    private String patientId;
    @ApiModelProperty(value = "影像名称")
    private String dicomName;

    @ApiModelProperty(value = "起始日期yyyy-MM-dd")
    private String createStartDate;
    @ApiModelProperty(value = "结束日期yyyy-MM-dd")
    private String createEndDate;

}
