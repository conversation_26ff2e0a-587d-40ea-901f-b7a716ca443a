package com.endovas.cps.pojo.fo.surgery;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/7/15
 * Time: 15:11
 */
@Setter
@Getter
public class SurgeryTypeAddFO {
    @NotEmpty(message = "名称不能为空")
    @ApiModelProperty(value = "名称")
    private String name;

    @NotEmpty(message = "联动拼接名称不能为空")
    @ApiModelProperty(value = "联动拼接名称")
    private String nameWithParent;

    private String parentId;

    @NotNull(message = "排序序号不能为空")
    @ApiModelProperty(value = "排序序号")
    private Integer serialNumber;//序号

}
