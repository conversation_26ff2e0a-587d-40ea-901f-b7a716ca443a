package com.endovas.cps.pojo.fo.measurement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @author: wk
 * @Date: 2024/11/25
 * @Time: 14:42
 */
@Getter
@Setter
public class MeasurementResultAddFO {
    @ApiModelProperty(value = "测量任务id", required = true)
    @NotBlank(message = "测量任务id不能为空")
    private String measurementId;

    @ApiModelProperty(value = "附件ID")
    @NotBlank(message = "附件ID不能为空")
    private String attachmentId;

    private String fileHash;




}
