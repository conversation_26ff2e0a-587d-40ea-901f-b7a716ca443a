package com.endovas.cps.pojo.vo;

import com.endovas.cps.entity.announcement.Announcement;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/1/8
 * Time: 14:20
 */
@Getter
@Setter
@ApiModel("公告信息")
public class AnnouncementVO extends BaseVO implements BeanConvert<AnnouncementVO, Announcement> {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("公告标题")
    private String title;
    @ApiModelProperty("公告内容")
    private String content;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}
