package com.endovas.cps.pojo.fo.resource.dicom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: wk
 * @Date: 2023/7/27
 * @Time: 19:48
 */
@Getter
@Setter
public class DicomSearchFO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "影像名称")
    private String name;
    @ApiModelProperty(value = "疾病类型")
    private String surgeryTypeId;
    @ApiModelProperty(value = "影像描述")
    private String remark;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;
    @ApiModelProperty(value = "上传人")
    private String creator;

}
