package com.endovas.cps.pojo.fo.hospital;

import com.endovas.cps.entity.hospital.CathRoom;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/8/14
 * Time: 14:22
 */
@Data
@ApiModel(value = "新建导管室")
public class CathRoomAddFO implements BeanConvert<CathRoomAddFO, CathRoom> {

    @ApiModelProperty(value = "所属医院ID", required = true)
    @NotEmpty(message = "所属医院ID不能为空")
    private String hospitalId;
    @ApiModelProperty(value = "导管室名称", required = true)
    @NotEmpty(message = "导管室名称不能为空")
    private String name;
    @ApiModelProperty(value = "位置", required = true)
    @NotEmpty(message = "位置不能为空")
    private String location;
    @ApiModelProperty(value = "dsa设备")
    private String dsaDevice;

    private List<String> attachmentIds;
}
