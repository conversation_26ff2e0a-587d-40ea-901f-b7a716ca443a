package com.endovas.cps.pojo.fo.measurement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 16:59
 */
@Data
public class MeasurementTaskResultFO {
    @ApiModelProperty(value = "测量结果id")
    private String id;
    @ApiModelProperty(value = "measurementId")
    private String measurementId;
    @ApiModelProperty(value = "result")
    private String result;
}
