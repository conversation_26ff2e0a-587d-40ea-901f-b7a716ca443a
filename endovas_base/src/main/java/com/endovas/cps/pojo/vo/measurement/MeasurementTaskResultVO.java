package com.endovas.cps.pojo.vo.measurement;

import com.endovas.cps.entity.measurement.MeasurementTaskResult;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/20
 * @Time: 15:47
 */
@Getter
@Setter
public class MeasurementTaskResultVO extends BaseVO implements BeanConvert<MeasurementTaskResultVO, MeasurementTaskResult> {
    @ApiModelProperty(value = "测量任务ID")
    private String measurementId;
    @ApiModelProperty(value = "测量任务结果")
    private String result;
    @ApiModelProperty(value = "上一次保存时间")
    private LocalDateTime updateTime;
    @ApiModelProperty(value = "是否测量完成")
    private Boolean isFinished;

    private String patientName;
    private String gender;
    private String age;
}
