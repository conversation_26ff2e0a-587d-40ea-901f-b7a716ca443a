package com.endovas.cps.pojo.fo.patient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2025/7/28
 * Time: 16:49
 */
@Setter
@Getter
public class MedicalHisEditFO {
    @ApiModelProperty(value = "病患ID")
    @NotEmpty(message = "病患ID不能为空")
    private String patientId;
    private String chiefComplaint;
    private String presentIllnessHistory;
    private String pastMedicalHistory;

}
