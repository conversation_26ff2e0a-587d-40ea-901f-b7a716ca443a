package com.endovas.cps.pojo.fo.hospital;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:51
 */
@Setter
@Getter
@ApiModel
public class HospitalSearchFO {
    @ApiModelProperty(value = "医院名称")
    private String name;
    @ApiModelProperty(value = "udi")
     private String udi;
     @ApiModelProperty(value = "系统版本")
     private String sysVer;
     @ApiModelProperty(value = "软件版本")
     private String softVer;


    @ApiModelProperty(value = "代理商")
    private String medAgentName;

}
