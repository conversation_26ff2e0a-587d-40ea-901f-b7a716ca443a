package com.endovas.cps.pojo.vo.platform.hospital;

import com.endovas.cps.entity.hospital.Hospital;
import com.endovas.cps.pojo.vo.AttachmentVO;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 19:41
 */
@Getter
@Setter
@ApiModel
public class HospitalDetailVO extends BaseVO implements BeanConvert<HospitalDetailVO, Hospital> {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String id;
    @ApiModelProperty(value = "医院名称", required = true)
    private String name;
    private AttachmentVO logo;

}
