package com.endovas.cps.pojo.fo.equipment;

import com.endovas.cps.entity.platform.Equipment;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:44
 */
@Data
@ApiModel(value = "添加设备资产")
public class EquipmentAddFO implements BeanConvert<EquipmentAddFO, Equipment> {
    @ApiModelProperty(value = "设备名称", required = true)
    @NotEmpty(message = "设备名称不能为空")
    private String name;
    @ApiModelProperty(value = "医疗软件", required = true)
    @NotEmpty(message = "医疗软件不能为空")
    private String softName;
    @ApiModelProperty(value = "版本", required = true)
    @NotEmpty(message = "版本不能为空")
    private String softVer;
    private String os;
    private String osVer;
    private String serverUrl;
    private String organizationId;

}
