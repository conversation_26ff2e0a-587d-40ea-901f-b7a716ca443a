package com.endovas.cps.pojo.fo.measurement;

import com.endovas.cps.entity.measurement.MeasurementTask;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:52
 */
@Data
@ApiModel(value = "新建测量任务")
public class MeasurementTaskAddFO implements BeanConvert<MeasurementTaskAddFO, MeasurementTask> {

    @ApiModelProperty(value = "影像id", required = true)
    @NotEmpty(message = "影像id不能为空")
    private String dicomId;
    @ApiModelProperty(value = "是否指派给自己", required = true)
    @NotNull(message = "任务类型不能为空")
    private Boolean assignToSelf;
    @ApiModelProperty(value = "任务类型")
    private String dataRangeType;
    @ApiModelProperty(value = "指定测量人Id")
    private String measurerId;
    @ApiModelProperty(value = "患者id", required = true)
    @NotEmpty(message = "患者id不能为空")
    private String patientId;
    @ApiModelProperty(value = "疾病类型id", required = true)
    @NotEmpty(message = "疾病类型id不能为空")
    private String surgeryTypeId;
    @ApiModelProperty(value = "医院id", required = true)
    @NotEmpty(message = "医院id不能为空")
    private String hospitalId;



}
