package com.endovas.cps.pojo.vo.trolley;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 11:53
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class TrolleyTransferVO {
    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("操作时间")
    private LocalDateTime createTime;
    @ApiModelProperty("真实装机日期")
    private String installedDate;
    @ApiModelProperty("真实转移日期")
    private String transferDate;
}
