package com.endovas.cps.pojo.fo.resource.dicom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @author: wk
 * @Date: 2023/7/27
 * @Time: 19:52
 */
@Getter
@Setter
public class DicomEditFO {

    @ApiModelProperty(value = "id")
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "文件名称")
    @NotBlank(message = "文件名称不能为空")
    private String name;

    @ApiModelProperty(value = "疾病类型")
    private String surgeryTypeId;
    @ApiModelProperty(value = "医院名称")
    private String hospitalId;
}
