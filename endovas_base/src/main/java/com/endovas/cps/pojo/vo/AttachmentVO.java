package com.endovas.cps.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class AttachmentVO {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("链接")
    private String url;
    @ApiModelProperty("资料类型")
    private String type;
    @ApiModelProperty("文件名称")
    private String name;
    @ApiModelProperty("排序")
    private Integer orderCol;
    @ApiModelProperty("文件扩展名")
    private String contentType;
    @ApiModelProperty("上传人")
    private String creator;
}
