package com.endovas.cps.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.endovas.cps.config.properties.AliStsProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @author: wk
 * @Date: 2023/8/30
 * @Time: 16:22
 */
@Configuration
public class AliSTSConfig {
    @Resource
    private AliStsProperties aliSTSProperties;

    @Bean
    public DefaultAcsClient acsClient() {
        IClientProfile profile = DefaultProfile.getProfile("cn-shanghai", aliSTSProperties.getAccessKey(), aliSTSProperties.getAccessKeySecret());
        return new DefaultAcsClient(profile);
    }
}
