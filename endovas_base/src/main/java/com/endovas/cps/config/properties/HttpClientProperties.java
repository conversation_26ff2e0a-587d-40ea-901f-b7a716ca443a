package com.endovas.cps.config.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "http")
public class HttpClientProperties {

    @Value("${maxTotal:100}")
    private Integer maxTotal;
    @Value("${defaultMaxPerRoute:20}")
    private Integer defaultMaxPerRoute;
    @Value("${connectTimeout:5000}")
    private Integer connectTimeout;
    @Value("${connectionRequestTimeout:5000}")
    private Integer connectionRequestTimeout;
    @Value("${socketTimeout:3000000}")
    private Integer socketTimeout;
    @Value("${staleConnectionCheckEnabled:true}")
    private boolean staleConnectionCheckEnabled;

}
