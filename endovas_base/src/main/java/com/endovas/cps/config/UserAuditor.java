package com.endovas.cps.config;

import io.daige.starter.common.utils.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 审计记录创建或修改用户
 *
 */
@Component
@Slf4j
public class UserAuditor implements AuditorAware<String> {

    @Override
    public Optional<String> getCurrentAuditor() {

        try {
            return Optional.ofNullable(ContextUtil.getUserId());
        }catch (Exception e){
            return Optional.empty();
        }
    }
}
