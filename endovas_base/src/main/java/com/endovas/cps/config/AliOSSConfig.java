package com.endovas.cps.config;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.endovas.cps.config.properties.AliOSSProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/17
 * Time: 下午4:15
 */
@Configuration
public class AliOSSConfig {
    @Resource
    private AliOSSProperties aliOSSProperties;

    @Bean
    public OSSClient ossClient() {
        OSSClient ossClient = new OSSClient(aliOSSProperties.getApiEndPoint(),
                new DefaultCredentialProvider(aliOSSProperties.getAccessKey(), aliOSSProperties.getAccessKeySecret()),
                new ClientConfiguration());
        return ossClient;
    }




}
