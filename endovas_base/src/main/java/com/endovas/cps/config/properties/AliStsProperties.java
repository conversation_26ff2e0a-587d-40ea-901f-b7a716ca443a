package com.endovas.cps.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/17
 * Time: 下午4:14
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file.sts")
public class AliStsProperties {
    private String endPoint;
    private String accessKey;
    private String accessKeySecret;
    private String roleArn;
    private String roleSessionName;

}
