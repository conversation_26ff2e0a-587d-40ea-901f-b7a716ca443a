package com.endovas.cps.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/6
 * Time: 16:49
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "guacamole")
public class GuacamoleProperties {
    private String host;
    private int port;
    private String screenSaveDir;

}
