package com.endovas.cps.controller.web.platform.hospital;

import com.endovas.cps.pojo.fo.hospital.CathRoomAddFO;
import com.endovas.cps.pojo.fo.hospital.CathRoomEditFO;
import com.endovas.cps.pojo.vo.platform.hospital.CathRoomListVO;
import com.endovas.cps.service.hospital.CathRoomService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/8/14
 * Time: 14:32
 */
@Slf4j
@RestController
@Api(value = "导管室管理", tags = "导管室管理接口")
@RequestMapping(Project.PLATFORM + "/hospital/cathRoom")
@RequiredArgsConstructor
public class CathRoomController {
    private final CathRoomService cathRoomService;

    @ApiOperation(value = "导管室列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<List<CathRoomListVO>> list(String hospitalId) {
        BizAssert.notEmpty(hospitalId, "医院ID不能为空");
        return Render.success(cathRoomService.list(hospitalId));
    }


    @ApiOperation(value = "添加导管室", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated CathRoomAddFO input) {
        cathRoomService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑导管室", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated CathRoomEditFO input) {
        cathRoomService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "启用/废弃导管室", notes = "")
    @PostMapping(path = "/switch")
    public String switchAvailable(String id) {
        BizAssert.notEmpty(id, "导管室ID不能为空");
        cathRoomService.switchAvailable(id);
        return RenderJson.success();
    }


}
