package com.endovas.cps.controller.web.platform.organization;

import cn.hutool.core.util.StrUtil;
import com.endovas.cps.pojo.fo.platform.PlatformUserAddFO;
import com.endovas.cps.pojo.fo.platform.PlatformUserEditFO;
import com.endovas.cps.pojo.vo.platform.PlatformUserListVO;
import com.endovas.cps.service.platform.PlatformUserService;
import io.daige.starter.common.Project;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.component.redis.service.RedisHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 20:23
 */
@Slf4j
@RestController
@Api(value = "组织管理", tags = "用户接口")
@RequestMapping(Project.PLATFORM + "/org/user")
@RequiredArgsConstructor
public class PlatformUserController {
    private final PlatformUserService platformUserService;
    private final RedisHelper redisHelper;


    @ApiOperation(value = "添加", notes = "")
    @PostMapping(path = "/add")
    public BaseResult<String> add(@ApiIgnore @CurrentUser LoginUser loginUser, @Validated PlatformUserAddFO input) {
        platformUserService.add(input);
        return Render.success();
    }


    @ApiOperation(value = "编辑", notes = "")
    @PostMapping(path = "/edit")
    public String update(@ApiIgnore @CurrentUser LoginUser loginUser, @Validated PlatformUserEditFO input) {
        platformUserService.edit(input);
        redisHelper.delKey(RedisCacheKeys.getMenuKey(input.getId()));
        return RenderJson.success("操作成功");
    }


    @ApiOperation(value = "开启关闭账号", notes = "")
    @PostMapping(path = "/switchStatus")
    public String switchStatus(String id, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty("用户id不能为空");
        if (StrUtil.equals(loginUser.getId(), id)) {
            return RenderJson.fail("不能操作本用户");
        }
        platformUserService.closeOpenAccount(id);
        return RenderJson.success();
    }


    @ApiOperation(value = "初始化密码", notes = "")
    @PostMapping(path = "/initPass")
    public String initPass(@ApiIgnore @CurrentUser LoginUser loginUser, String id) {
        BizAssert.notEmpty(id, "用户id不能为空");
        platformUserService.initPass(id);
        return RenderJson.success();
    }


    @GetMapping(value = "/current/list")
    @ApiOperation(value = "获取当前登录用户所在的组织下所有用户接口")
    public BaseResult<List<PlatformUserListVO>> getCurrentOrgUserList(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(platformUserService.list(loginUser.getOrgId()));
    }



}
