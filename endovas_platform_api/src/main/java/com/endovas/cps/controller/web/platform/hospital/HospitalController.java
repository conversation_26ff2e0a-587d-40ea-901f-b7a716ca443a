package com.endovas.cps.controller.web.platform.hospital;

import com.endovas.cps.pojo.fo.hospital.HospitalAddFO;
import com.endovas.cps.pojo.fo.hospital.HospitalEditFO;
import com.endovas.cps.pojo.fo.hospital.HospitalSearchFO;
import com.endovas.cps.pojo.fo.trolley.TrolleyTransferFO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentSelectVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalListVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalSelectVO;
import com.endovas.cps.pojo.vo.trolley.TrolleyListVO;
import com.endovas.cps.pojo.vo.trolley.TrolleySelectVO;
import com.endovas.cps.pojo.vo.trolley.TrolleyTransferVO;
import com.endovas.cps.service.agent.MedAgentService;
import com.endovas.cps.service.hospital.CathRoomService;
import com.endovas.cps.service.hospital.HospitalService;
import com.endovas.cps.service.trolley.TrolleyService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:36
 */
@Slf4j
@RestController("PlatformHospitalController")
@Api(value = "医院管理", tags = "医院管理接口")
@RequestMapping(Project.PLATFORM + "/hospital")
@RequiredArgsConstructor
public class HospitalController {
    private final HospitalService hospitalService;
    private final MedAgentService medAgentService;
    private final TrolleyService trolleyService;
    private final CathRoomService cathRoomService;


    @ApiOperation(value = "代理商下拉列表", notes = "")
    @GetMapping(path = "/agent/select")
    public BaseResult<List<MedAgentSelectVO>> agentSelect() {
        return Render.success(medAgentService.select());
    }

    @ApiOperation(value = "医院下绑定的台车列表", notes = "")
    @GetMapping(path = "/trolley/list")
    public BaseResult<List<TrolleyListVO>> trolleyList(String hospitalId) {
        BizAssert.notEmpty(hospitalId, "医院ID不能为空");
        return Render.success(trolleyService.listByHospitalId(hospitalId));
    }

    @ApiOperation(value = "当前医院下可绑定的台车", notes = "")
    @GetMapping(path = "/trolley/select")
    public BaseResult<List<TrolleySelectVO>> trolleySelect(String hospitalId) {
        BizAssert.notEmpty(hospitalId, "医院ID不能为空");
        return Render.success(trolleyService.canBindTrolleyByHospitalId(hospitalId));
    }

    @ApiOperation(value = "所属导管室下拉列表", notes = "")
    @GetMapping(path = "/cathRoom/select")
    public BaseResult<List<TrolleySelectVO>> cathRoomSelect(String hospitalId) {
        BizAssert.notEmpty(hospitalId, "医院ID不能为空");
        return Render.success((cathRoomService.canBindSelect(hospitalId)));
    }


    @ApiOperation(value = "转移日志列表", notes = "")
    @GetMapping(path = "/trolley/transfer/log")
    public BaseResult<TrolleyTransferVO> transferLog(String trolleyId, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(trolleyId, "台车不能为空");
        return Render.success(trolleyService.transferLog(trolleyId, loginUser.getBelong()));
    }

    @ApiOperation(value = "可转移医院列表", notes = "")
    @GetMapping(path = "/transfer/select")
    public BaseResult<List<HospitalSelectVO>> transferSelect(String trolleyId) {
        BizAssert.notEmpty(trolleyId, "台车ID不能为空");
        return Render.success(hospitalService.selectByTrolleyId(trolleyId));
    }

    @ApiOperation(value = "处理转移", notes = "")
    @PostMapping(path = "/trolley/transfer")
    public BaseResult<List<HospitalSelectVO>> doTransfer(@Validated TrolleyTransferFO input) {
        trolleyService.transfer(input);
        return Render.success();
    }


    @ApiOperation(value = "绑定台车", notes = "")
    @PostMapping(path = "/trolley/bind")
    public BaseResult<String> trolleyList(String hospitalId, String udi, String cathRoomId, String installedDate) {
        BizAssert.notEmpty(hospitalId, "医院ID不能为空");
        BizAssert.notEmpty(udi, "UDI不能为空");
        trolleyService.bindHospital(hospitalId, udi, cathRoomId, installedDate);
        return Render.success();
    }


    @ApiOperation(value = "医院列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<HospitalListVO>> list(HospitalSearchFO searchFO, PageFO pageFO) {
        return Render.success(hospitalService.list(searchFO, pageFO));
    }

    @ApiOperation(value = "添加医院", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated HospitalAddFO input) {
        hospitalService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑医院", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated HospitalEditFO input) {
        hospitalService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "删除医院", notes = "")
    @PostMapping(path = "/del")
    public String del(String id) {
        BizAssert.notEmpty(id, "医院ID不能为空");
        hospitalService.del(id);
        return RenderJson.success();
    }


}
