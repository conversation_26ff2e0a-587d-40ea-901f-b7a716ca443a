package com.endovas.cps.controller.web.platform.hospital;

import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserAddByPlatformFO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserEditByPlatformFO;
import com.endovas.cps.pojo.fo.hospital.HospitalUserSearchByPlatformFO;
import com.endovas.cps.pojo.vo.hospital.HospitalUserListVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalSelectVO;
import com.endovas.cps.service.hospital.HospitalUserService;
import com.google.common.collect.Lists;
import io.daige.starter.common.Project;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:36
 */
@Slf4j
@RestController
@Api(value = "医院账号管理", tags = "医院账号管理接口")
@RequestMapping(Project.PLATFORM + "/hospital/user")
@RequiredArgsConstructor
public class HospitalUserController {
    private final HospitalUserService hospitalUserService;
    private final HospitalDAO hospitalDAO;

    @ApiOperation(value = "可选医院列表", notes = "")
    @GetMapping(path = "/hospitalSelect")
    public BaseResult<List<HospitalSelectVO>> select(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(hospitalDAO.findAll().stream().map(x -> {
            HospitalSelectVO one = new HospitalSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList()));
    }

    @ApiOperation(value = "医院账户列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<HospitalUserListVO>> list(@ApiIgnore @CurrentUser LoginUser loginUser, HospitalUserSearchByPlatformFO searchFO, PageFO pageFO) {

        List<String> hospitalIds = Lists.newArrayList();
        if (StrUtil.isNotEmpty(searchFO.getHospitalName())) {
            hospitalIds = hospitalDAO.findByNameContaining(searchFO.getHospitalName()).stream().map(MysqlBase::getId).collect(Collectors.toList());
        }

        PageVO<HospitalUserListVO> result = hospitalUserService.list(searchFO, hospitalIds, pageFO);
        List<HospitalUserListVO> contents = result.getContent();
        for (HospitalUserListVO content : contents) {
            hospitalDAO.findById(content.getHospitalId()).ifPresent(x -> {
                content.setHospitalName(x.getName());
            });
        }
        result.setContent(contents);
        return Render.success(result);
    }

    @ApiOperation(value = "添加医院账户", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated HospitalUserAddByPlatformFO input) {
        hospitalUserService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑医院账户", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated HospitalUserEditByPlatformFO input) {
        hospitalUserService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "初始化密码", notes = "")
    @PostMapping(path = "/initPass")
    public String initPass(String id) {
        BizAssert.notEmpty(id, "ID不能为空");
        hospitalUserService.initPass(id);
        return RenderJson.success();
    }

    @ApiOperation(value = "开启关闭账号", notes = "")
    @PostMapping(path = "/switchStatus")
    public String switchStatus(String id) {
        BizAssert.notEmpty(id, "ID不能为空");
        hospitalUserService.closeOpenAccount(id);
        return RenderJson.success();
    }

}
