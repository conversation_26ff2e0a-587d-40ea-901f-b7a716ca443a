package com.endovas.cps.controller.web.platform.dashboard;

import cn.hutool.core.collection.ListUtil;
import com.endovas.cps.pojo.vo.dashboard.*;
import com.endovas.cps.service.dashboard.DashboardService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 12:43
 */
@Slf4j
@RestController
@Api(value = "dashBoard", tags = "仪表板接口")
@RequestMapping(Project.PLATFORM + "/dashboard")
@RequiredArgsConstructor
public class DashBoardController {
    private final DashboardService dashboardService;

    @GetMapping(value = "/index")
    @ApiOperation(value = "首页基础统计")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DashBoardVO.class)})
    public String index(String year, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(year, "统计年份不能为空");
        return RenderJson.success(dashboardService.index(year, loginUser));
    }

    @ApiOperation(value = "排名", notes = "")
    @GetMapping(path = "/rank")
    public BaseResult<List<RankVO>> rank(String rankType, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(dashboardService.rank(rankType, loginUser));
    }

    @ApiOperation(value = "患者画像", notes = "")
    @GetMapping(path = "/patients")
    public BaseResult<PatientStatVO> patients(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(dashboardService.patientsStats(ListUtil.empty(),true));
    }

    @ApiOperation(value = "医院分布", notes = "")
    @GetMapping(path = "/hospitals")
    public BaseResult<List<HospitalGPSVO>> hospitals(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(dashboardService.hospitalsLocationStats(loginUser));
    }


    @GetMapping(value = "/dicom/self")
    @ApiOperation(value = "我的影像统计")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DicomSelfStatsVO.class)})
    public String dicomSelf(String year, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(year, "统计年份不能为空");
        return RenderJson.success(dashboardService.dicomSelfStats(year, loginUser));
    }

    @GetMapping(value = "/label/self")
    @ApiOperation(value = "我的标注统计")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DicomSelfStatsVO.class)})
    public String labelSelf(String year, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(year, "统计年份不能为空");
        return RenderJson.success(dashboardService.labelSelfStats(year, loginUser));
    }

    @GetMapping(value = "/measurement/self")
    @ApiOperation(value = "我的测量统计")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DicomSelfStatsVO.class)})
    public String measurementSelf(String year, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(year, "统计年份不能为空");
        return RenderJson.success(dashboardService.measurementSelfStats(year, loginUser));
    }
}
