package com.endovas.cps.controller.web.platform.dashboard;

import com.endovas.cps.pojo.vo.dashboard.DicomSelfStatsVO;
import com.endovas.cps.service.dashboard.WorkbenchesService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @author: wk
 * @Date: 2024/12/30
 * @Time: 14:39
 */
@Slf4j
@RestController
@Api(value = "workbench", tags = "工作台接口")
@RequestMapping(Project.PLATFORM + "/workbench")
@RequiredArgsConstructor
public class WorkbenchController {

    private final WorkbenchesService workbenchesService;

    @GetMapping(value = "/stat")
    @ApiOperation(value = "数据统计")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DicomSelfStatsVO.class)})
    public String stat(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return RenderJson.success(workbenchesService.stat(loginUser));
    }

    @GetMapping(value = "/medImageMonthStat")
    @ApiOperation(value = "影像上传趋势图")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DicomSelfStatsVO.class)})
    public String medImageMonthStat(String year, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(year, "统计年份不能为空");
        return RenderJson.success(workbenchesService.medImageMonthStat(year, loginUser));
    }

    @GetMapping(value = "/todoListStat")
    @ApiOperation(value = "待办任务统计")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = DicomSelfStatsVO.class)})
    public String todoListStat(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return RenderJson.success(workbenchesService.todoListStat(loginUser));
    }
}
