
package com.endovas.cps.controller.web.platform.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.RoleDAO;
import com.endovas.cps.dao.RolePermissionDAO;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.RolePermission;
import com.endovas.cps.entity.UserRole;
import com.endovas.cps.enums.DefaultRoleCodeEnum;
import com.endovas.cps.pojo.fo.RoleAddFO;
import com.endovas.cps.pojo.fo.RoleEditFO;
import com.endovas.cps.pojo.fo.RoleSearchFO;
import com.endovas.cps.pojo.vo.RoleListVO;
import com.endovas.cps.pojo.vo.RolePermissionVO;
import com.endovas.cps.pojo.vo.RoleSelectVO;
import com.endovas.cps.service.role.PermissionService;
import com.endovas.cps.service.role.RolePermissionService;
import com.endovas.cps.service.role.UserRoleService;
import com.google.common.collect.Lists;
import io.daige.starter.common.Project;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.common.utils.PageUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@RestController
@Api(value = "用户授权认证", tags = "角色管理接口")
@RequestMapping(Project.PLATFORM + "/role")
public class PlatformRoleController {

    @Autowired
    private RoleDAO roleDAO;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RolePermissionService rolePermissionService;
    @Autowired
    private RolePermissionDAO rolePermissionDAO;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private RedisHelper redisHelper;


    @ApiOperation(value = "获取所有角色（下拉框）")
    @GetMapping(path = "/select")
    public BaseResult<List<RoleSelectVO>> listRole(@ApiIgnore @CurrentUser LoginUser loginUser) {
        List<RoleSelectVO> result = roleDAO.findByBelongAndBelongId(loginUser.getBelong().getCode(), null).stream()
                //过滤掉系统内置角色
                .filter(x -> !DefaultRoleCodeEnum.getSysRoleNameList().contains(x.getCode()))
                .map(x -> {
                    RoleSelectVO z = new RoleSelectVO();
                    z.setId(x.getId());
                    z.setName(x.getName());
                    return z;
                }).collect(Collectors.toList());
        return Render.success(result);
    }


    @GetMapping(value = "/all")
    @ApiOperation(value = "所有角色")
    public BaseResult<RoleListVO> all(@ApiIgnore @CurrentUser LoginUser loginUser) {
        List<RoleListVO> list = roleDAO.findByBelongAndBelongId(loginUser.getBelong().getCode(), null).stream().map(x -> new RoleListVO().convertFrom(x)).collect(Collectors.toList());
        return Render.success(list);
    }

    @GetMapping(value = "/getAllByPage")
    @ApiOperation(value = "分页获取角色")
    public BaseResult<PageVO<RoleListVO>> getRoleByPage(RoleSearchFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        Specification<Role> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get(Role.BELONG), loginUser.getBelong().getCode()));
            list.add(criteriaBuilder.isNull(root.get(Role.BELONG_ID)));

            if (StrUtil.isNotEmpty(searchFO.getName())) {
                Predicate p1 = criteriaBuilder.like(root.get(Role.NAME), "%" + searchFO.getName() + "%");
                list.add(p1);
            }

            if (Objects.nonNull(searchFO.getActiveStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Role.ACTIVE_STATUS), "1".equals(searchFO.getActiveStatus()) ? true : false);
                list.add(p1);
            }

            if (Objects.nonNull(searchFO.getDataType())) {
                Predicate p1 = criteriaBuilder.equal(root.get(Role.DATA_TYPE), searchFO.getDataType());
                list.add(p1);
            }

            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };
        Page<RoleListVO> list = roleDAO.findAll(specification, PageUtil.initJPAPage(pageFO)).map(x -> new RoleListVO().convertFrom(x));
        for (RoleListVO role : list.getContent()) {
            // 角色拥有权限
            List<RolePermissionVO> permissions = rolePermissionService.findByRoleId(role.getId()).stream().map(x -> new RolePermissionVO().convertFrom(x)).collect(Collectors.toList());
            role.setPermissions(permissions);
            // 角色包含的用户数量
            role.setAccountAmount(userRoleService.countByRoleId(role.getId()));

        }
        return Render.success(PageUtil.convert(list));
    }


    @PostMapping(value = "/editRolePerm")
    @ApiOperation(value = "编辑角色分配菜单权限")
    public BaseResult<String> editRolePerm(@RequestParam String roleId,
                                           @RequestParam(required = false) String[] permIds) {

        //删除其关联权限
        rolePermissionService.deleteByRoleId(roleId);

        Permission permissionsZero = permissionService.getRoot();
        RolePermission rolePermissionZero = new RolePermission();
        rolePermissionZero.setRoleId(roleId);
        rolePermissionZero.setPermissionId(permissionsZero.getId());
        rolePermissionDAO.save(rolePermissionZero);

        //分配新权限
        for (String permId : permIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permId);
            rolePermissionDAO.save(rolePermission);
        }

        //清空缓存菜单
        List<UserRole> userRoles = userRoleService.findByRoleId(roleId);
        for (UserRole userRole : userRoles) {
            redisHelper.delKey(RedisCacheKeys.getMenuKey(userRole.getUserId()));
        }
        return Render.success("操作成功");
    }


    @PostMapping(value = "/save")
    @ApiOperation(value = "保存数据")
    public BaseResult<String> save(@Validated RoleAddFO fo, @RequestParam(required = false) String[] permIds, @ApiIgnore @CurrentUser LoginUser loginUser) {

        Role role = new Role();
        //如果不传入,默认不脱敏
        if (Objects.isNull(fo.getDesensitize())) {
            fo.setDesensitize(false);
        }
        BeanUtil.copyProperties(fo, role, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties(MysqlBase.FIELD_ID));
        String code = "ROLE_" + fo.getName();

        Role existRole = roleDAO.getByCodeAndBelongAndBelongId(code, loginUser.getBelong().getCode(), null);
        if (Objects.nonNull(existRole)) {
            return Render.fail("角色名称不能重复");
        }
        role.setCode(code);
        role.setBelong(loginUser.getBelong().getCode());
        role.setActiveStatus(true);
        Role r = roleDAO.save(role);

        Permission listZero = permissionService.getRoot();
        RolePermission rolePermissionZero = new RolePermission();
        rolePermissionZero.setRoleId(r.getId());
        rolePermissionZero.setPermissionId(listZero.getId());
        rolePermissionDAO.save(rolePermissionZero);

        //分配新权限
        for (String permId : permIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(r.getId());
            rolePermission.setPermissionId(permId);
            rolePermissionDAO.save(rolePermission);
        }
        return Render.success("保存数据成功");
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "更新数据")
    public BaseResult<String> edit(@Validated RoleEditFO roleEditFO, @RequestParam(required = false) String[] permIds) {
        Role role = roleDAO.findById(roleEditFO.getId()).orElseThrow(() -> new BusinessAssertException("角色不正确"));
        //如果不传入,默认不脱敏
        if (Objects.isNull(roleEditFO.getDesensitize())) {
            roleEditFO.setDesensitize(false);
        }

        BeanUtil.copyProperties(roleEditFO, role, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties(MysqlBase.FIELD_ID));
        Role r = roleDAO.save(role);
        //删除其关联权限
        rolePermissionService.deleteByRoleId(r.getId());


        Permission permissionZero = permissionService.getRoot();
        RolePermission rolePermissionZero = new RolePermission();
        rolePermissionZero.setRoleId(r.getId());
        rolePermissionZero.setPermissionId(permissionZero.getId());
        rolePermissionDAO.save(rolePermissionZero);


        //分配新权限
        for (String permId : permIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(r.getId());
            rolePermission.setPermissionId(permId);
            rolePermissionDAO.save(rolePermission);
        }
        //清空缓存菜单
        List<UserRole> userRoles = userRoleService.findByRoleId(roleEditFO.getId());
        for (UserRole userRole : userRoles) {
            redisHelper.delKey(RedisCacheKeys.getMenuKey(userRole.getUserId()));
        }
        return Render.success("更新数据成功");
    }

    @PostMapping(value = "/delAllByIds/{ids}")
    @ApiOperation(value = "批量通过ids删除")
    public BaseResult<String> delByIds(@PathVariable String[] ids) {

        for (String id : ids) {
            List<UserRole> list = userRoleService.findByRoleId(id);
            if (list != null && list.size() > 0) {
                return Render.error("删除失败，包含正被用户使用关联的角色");
            }
        }
        List<String> roleCodes = roleDAO.findByIdIn(Lists.newArrayList(ids)).stream().map(Role::getCode).collect(Collectors.toList());
        for (String roleCode : roleCodes) {
            if (DefaultRoleCodeEnum.getSysRoleNameList().contains(roleCode)) {
                throw new BusinessAssertException("内置角色,无法删除");
            }
        }
        for (String id : ids) {


            roleDAO.deleteById(id);
            //删除关联菜单权限
            rolePermissionService.deleteByRoleId(id);
            //清空缓存菜单
            List<UserRole> userRoles = userRoleService.findByRoleId(id);
            for (UserRole userRole : userRoles) {
                redisHelper.delKey(RedisCacheKeys.getMenuKey(userRole.getUserId()));
            }
        }
        return Render.success("批量通过id删除数据成功");
    }

    @PostMapping(value = "/changeActiveStatus")
    @ApiOperation(value = "改变启用|禁用状态")
    public BaseResult<String> changeActiveStatus(@RequestParam(required = true) String id, @RequestParam(required = true) String status) {

        BizAssert.notEmpty(id, "角色id不能为空");
        BizAssert.notEmpty(status, "启用|禁用标志不能为空");
        BizAssert.isTrue("1".equals(status) || "0".equals(status), "启用|禁用标志格式不正确");
        List<UserRole> userRoles = userRoleService.findByRoleId(id);
        if (CollectionUtil.isNotEmpty(userRoles)) {
            return Render.fail("有绑定的用户,解除绑定后再进行禁用操作");
        }
        Optional<Role> result = roleDAO.findById(id);
        BizAssert.isTrue(result.isPresent(), "角色启用|禁用状态修改失败");
        Role po = result.get();
        if (DefaultRoleCodeEnum.getSysRoleNameList().contains(po.getCode())) {
            return Render.fail("内置角色不能被禁用");
        }

        po.setActiveStatus("1".equals(status));
        roleDAO.save(po);

        return Render.success();
    }


}

