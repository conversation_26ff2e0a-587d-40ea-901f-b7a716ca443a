
package com.endovas.cps.controller.web.platform.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.PermissionDAO;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.RolePermission;
import com.endovas.cps.pojo.fo.PermissionFO;
import com.endovas.cps.pojo.vo.ButtonVO;
import com.endovas.cps.pojo.vo.MenuTreeVO;
import com.endovas.cps.service.role.PermissionService;
import com.endovas.cps.service.role.RolePermissionService;
import io.daige.starter.common.Project;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.constant.BizCommonConst;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.component.redis.service.RedisHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@RestController
@Api(value = "菜单/权限管理接口", tags = "菜单管理接口")
@RequestMapping(Project.PLATFORM + "/permission")
public class PlatformPermissionController {

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PermissionDAO permissionDAO;

    @Autowired
    private RolePermissionService rolePermissionService;
    @Autowired
    private RedisHelper redisHelper;

    @GetMapping(value = "/getButtonList")
    @ApiOperation(value = "获取当前页面下的所有按钮")
    public BaseResult<List<ButtonVO>> findButtons(@ApiParam("当前页面id") String parentId) {
        List<ButtonVO> result = permissionDAO.findByTypeAndParentIdOrderBySortOrder(BizCommonConst.PERMISSION_BTN, parentId).stream().map(x -> new ButtonVO().convertFrom(x)).collect(Collectors.toList());
        return Render.success(result);
    }

    @GetMapping(value = "/getMenuAndBtn")
    @ApiOperation(value = "获取当前用户的所有页面菜单和按钮(平台管理员展示所有菜单)")
    public BaseResult<MenuTreeVO> findCurrentUserMenusAndBtns(@ApiIgnore @CurrentUser LoginUser loginUser, String belong) {
        BizAssert.notEmpty(belong, "请选择所属系统");
        return Render.success(permissionService.findCurrentUserMenusAndBtns(loginUser.getPermissions(), loginUser.getId(), BelongEnum.valueOf(belong)));
    }


    @PostMapping(value = "/add")
    @ApiOperation(value = "添加")
    public BaseResult<String> add(@Validated PermissionFO fo, @ApiIgnore @CurrentUser LoginUser loginUser) {
        if (BizCommonConst.PERMISSION_BTN.equals(fo.getType())) {
            if (StrUtil.isEmpty(fo.getParentId())) {
                return Render.error("添加按钮，上级菜单不能为空");
            }
            Permission parentPermission = permissionDAO.getPermissionById(fo.getParentId());
            if (BizCommonConst.PERMISSION_BTN.equals(parentPermission.getType())) {
                return Render.error("父级菜单不能为按钮");
            }
        }

        if (StrUtil.isEmpty(fo.getParentId())) {
            Permission root = permissionService.getRoot();
            fo.setParentId(root.getId());
        }


        // 判断拦截请求的操作权限按钮名是否已存在
        if (BizCommonConst.PERMISSION_BTN.equals(fo.getType())
                || BizCommonConst.PERMISSION_NAV.equals(fo.getType())) {
            List<Permission> list = permissionService.findByCodeAndBelong(fo.getCode(),fo.getBelong());
            if (list != null && list.size() > 0) {
                return Render.error("名称已存在");
            }
        }
        Permission permission = new Permission();
        BeanUtil.copyProperties(fo, permission, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties(MysqlBase.FIELD_ID));
        permission.setPath(fo.getPath());
        permissionDAO.save(permission);
        //重新加载权限
        permissionService.loadResourceDefine();
        //手动删除缓存
        redisHelper.delKey(RedisCacheKeys.getMenuKey(loginUser.getId()));
        return Render.success("添加成功");
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "编辑")
    public BaseResult<String> edit(@Validated PermissionFO fo) {
        if (BizCommonConst.PERMISSION_BTN.equals(fo.getType())) {
            if (StrUtil.isEmpty(fo.getParentId())) {
                return Render.error("添加按钮，上级菜单不能为空");
            }
            Permission parentPermission = permissionDAO.getPermissionById(fo.getParentId());
            if (BizCommonConst.PERMISSION_BTN.equals(parentPermission.getType())) {
                return Render.error("父级菜单不能为按钮");
            }
        }


        Permission p = permissionDAO.getPermissionById(fo.getId());


        if (Objects.isNull(fo.getParentId())) {
            Permission root = permissionService.getRoot();
            fo.setParentId(root.getId());
        }

        // 判断拦截请求的操作权限按钮名是否已存在
        if (BizCommonConst.PERMISSION_BTN.equals(fo.getType())
                || BizCommonConst.PERMISSION_NAV.equals(fo.getType())) {

            // 若名称修改
            if (!p.getCode().equals(fo.getCode())) {
                List<Permission> list = permissionService.findByCodeAndBelong(fo.getCode(),fo.getBelong());
                if (list != null && list.size() > 0) {
                    return Render.error("名称已存在");
                }
            }
        }

        Permission permission = permissionDAO.getPermissionById(fo.getId());
        BeanUtil.copyProperties(fo, permission, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties(MysqlBase.FIELD_ID));
        permission.setPath(fo.getPath());
        permissionDAO.save(permission);
        //重新加载权限
        permissionService.loadResourceDefine();
        return Render.success("编辑成功");
    }


    @PostMapping(value = "/delById")
    @ApiOperation(value = "通过id删除")
    public BaseResult<String> delById(String id) {

        // 选择父级，判断子集是否都选择
        List<Permission> permissionList = permissionService.findByParentIdIn(Arrays.asList(id));
        for (Permission p : permissionList) {
            if (!Arrays.asList(id).contains(p.getId())) {
                return Render.error("删除失败，菜单下面包含子页面或者操作");
            }
        }

        List<RolePermission> list = rolePermissionService.findByPermissionId(id);
        if (list != null && list.size() > 0) {
            return Render.error("删除失败，包含正被角色使用关联的菜单或权限");
        }

        permissionService.deleteById(id);

        //重新加载权限
        permissionService.loadResourceDefine();
        return Render.success("删除数据成功");
    }

}


