package com.endovas.cps.controller.web.platform.agent;

import com.endovas.cps.pojo.fo.agent.MedAgentAddFO;
import com.endovas.cps.pojo.fo.agent.MedAgentEditFO;
import com.endovas.cps.pojo.fo.agent.MedAgentSearchFO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentListVO;
import com.endovas.cps.pojo.vo.trolley.TrolleyListVO;
import com.endovas.cps.service.agent.MedAgentService;
import com.endovas.cps.service.trolley.TrolleyService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:36
 */
@Slf4j
@RestController
@Api(value = "代理商管理", tags = "代理商管理接口")
@RequestMapping(Project.PLATFORM + "/medAgent")
@RequiredArgsConstructor
public class MedAgentController {
    private final MedAgentService medAgentService;
    private final TrolleyService trolleyService;

    @ApiOperation(value = "代理商列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<MedAgentListVO>> list(@ApiIgnore @CurrentUser LoginUser loginUser, MedAgentSearchFO searchFO, PageFO pageFO) {
        return Render.success(medAgentService.list(searchFO, pageFO));
    }

    @ApiOperation(value = "添加代理商", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated MedAgentAddFO input) {
        medAgentService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑代理商", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated MedAgentEditFO input) {
        medAgentService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "删除代理商", notes = "")
    @PostMapping(path = "/del")
    public String del(String id) {
        BizAssert.notEmpty(id, "医疗器械公司ID不能为空");
        medAgentService.del(id);
        return RenderJson.success();
    }

    @ApiOperation(value = "代理商下的台车信息列表", notes = "")
    @GetMapping(path = "/trolley/list")
    public BaseResult<PageVO<TrolleyListVO>> trolleyList(String medAgentId, PageFO pageFO) {
        BizAssert.notEmpty(medAgentId, "代理商ID不能为空");
        return Render.success(trolleyService.listBuyTrolley(medAgentId, pageFO));
    }

    @ApiOperation(value = "分配台车", notes = "")
    @GetMapping(path = "/trolley/assign")
    public BaseResult<String> trolleyList(String medAgentId, String udi, String installedDate) {
        BizAssert.notEmpty(medAgentId, "代理商ID不能为空");
        BizAssert.notEmpty(udi, "UDI不能为空");
        trolleyService.assignTrolley(medAgentId, udi, installedDate);
        return Render.success();
    }


}
