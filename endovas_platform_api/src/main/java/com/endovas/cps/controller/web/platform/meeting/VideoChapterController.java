package com.endovas.cps.controller.web.platform.meeting;

import com.endovas.cps.pojo.vo.meeting.VideoChapterAddFO;
import com.endovas.cps.service.meeting.VideoChapterService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/18
 * Time: 14:44
 */
@Slf4j
@RestController
@Api(value = "术中录播章节", tags = "术中录播章节接口")
@RequestMapping(Project.PLATFORM + "/meeting/chapter")
@RequiredArgsConstructor
public class VideoChapterController {
    private final VideoChapterService videoChapterService;

    @ApiOperation(value = "增加章节", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated VideoChapterAddFO input) {
        videoChapterService.add(input);
        return RenderJson.success();
    }


    @ApiOperation(value = "章节详情", notes = "")
    @GetMapping(path = "/list")
    public String list(String deviceId) {
        BizAssert.notEmpty(deviceId, "设备ID不能为空");
        return RenderJson.success(videoChapterService.list(deviceId));
    }
}
