package com.endovas.cps.controller.web.platform.patient;

import com.endovas.cps.pojo.fo.patient.track.PatientTrackAddFO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackEditFO;
import com.endovas.cps.pojo.fo.patient.track.PatientTrackSearchFO;
import com.endovas.cps.service.patient.PatientTrackService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/4
 * Time: 14:25
 */
@Slf4j
@RestController
@Api(value = "患者随访管理", tags = "患者随访管理接口")
@RequestMapping(Project.PLATFORM + "/patientTrack")
@RequiredArgsConstructor
public class PatientTrackController {
    private final PatientTrackService patientTrackService;


    @ApiOperation(value = "随访列表", notes = "")
    @GetMapping(path = "/list")
    public String list(PatientTrackSearchFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return RenderJson.success(patientTrackService.list(searchFO, pageFO, loginUser));

    }

    @ApiOperation(value = "添加随访", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated PatientTrackAddFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        patientTrackService.add(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑随访", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated PatientTrackEditFO input) {
        patientTrackService.edit(input);
        return RenderJson.success();
    }
}
