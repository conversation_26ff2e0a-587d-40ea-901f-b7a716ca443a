package com.endovas.cps.controller.web;

import cn.hutool.json.JSONUtil;
import com.endovas.cps.pojo.dto.ws.TunnelDTO;
import com.endovas.cps.service.wsmsg.WebSocketMsgService;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.Objects;

/**
 * @author: wk
 * @Date: 2025/1/2
 * @Time: 15:41
 */
@Slf4j
@ServerEndpoint(value = "/remote/plt")
@Component
public class PltEndPoint {

    private static RedisHelper redisHelper;
    private static WebSocketMsgService webSocketMsgService;
    // 用户id
    private String userId;
    // 会话
    private Session session;

    @Autowired
    void setRedisHelper(RedisHelper redisHelper) {
        PltEndPoint.redisHelper = redisHelper;
    }
    @Autowired
    void setWebSocketMsgService(WebSocketMsgService webSocketMsgService) {
        this.webSocketMsgService = webSocketMsgService;
    }

    @OnOpen
    public void onOpen(Session session, EndpointConfig config){
        this.session = session;

        String token = session.getRequestParameterMap().get("accessToken").get(0);
        String currentUser = redisHelper.strGet(RedisCacheKeys.getTokenPrefixKey(token));
        if (Objects.nonNull(currentUser)) {
            LoginUser loginUser = JSONUtil.toBean(currentUser, LoginUser.class);
            webSocketMsgService.putTunnel(loginUser.getId(), new TunnelDTO(loginUser.getId(), this.session));
            this.userId = loginUser.getId();
        } else {
            this.onClose(session);
        }

        log.info("收到来自窗口的连接，userId={}", this.userId);
    }

    @OnMessage
    public void onMessage(String message, Session session){
        log.info("收到来自窗口[{}]的的信息: {}", this.userId, message);
    }

    @OnClose
    public void onClose(Session session){
        if (StringUtils.isNotBlank(this.userId)) {
            webSocketMsgService.removeTunnel(this.userId);
        }
        log.info("有一连接[{}]关闭！当前连接数为 {}", this.userId, webSocketMsgService.getTunnelSize());
    }

    @OnError
    public void onError(Session session, Throwable throwable){
        if (StringUtils.isNotBlank(this.userId)) {
            webSocketMsgService.removeTunnel(this.userId);
        }
        log.error("WebsocketServer 连接发生错误", throwable);
    }
}
