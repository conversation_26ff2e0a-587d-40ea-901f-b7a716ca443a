package com.endovas.cps.controller.web.platform.trolley;

import com.endovas.cps.pojo.fo.trolley.TrolleyAddFO;
import com.endovas.cps.pojo.fo.trolley.TrolleyEditFO;
import com.endovas.cps.pojo.fo.trolley.TrolleySearchFO;
import com.endovas.cps.pojo.vo.trolley.TrolleyListVO;
import com.endovas.cps.service.trolley.TrolleyService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:28
 */
@Slf4j
@RestController("PlatformTrolleyController")
@Api(value = "台车管理", tags = "台车管理接口")
@RequestMapping(Project.PLATFORM + "/trolley")
@RequiredArgsConstructor
public class TrolleyController {
    private final TrolleyService trolleyService;

    @ApiOperation(value = "台车列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<TrolleyListVO>> list(@ApiIgnore @CurrentUser LoginUser loginUser, TrolleySearchFO searchFO, PageFO pageFO) {
        return Render.success(trolleyService.list(searchFO, pageFO));
    }

    @ApiOperation(value = "添加台车", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated TrolleyAddFO input) {
        trolleyService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑台车", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated TrolleyEditFO input) {
        trolleyService.edit(input);
        return RenderJson.success();
    }

}
