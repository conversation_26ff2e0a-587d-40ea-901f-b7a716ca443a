package com.endovas.cps.controller.web.platform.patient;

import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.vo.patient.PatientListVO;
import com.endovas.cps.service.patient.PatientService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 12:43
 */
@Slf4j
@RestController
@Api(value = "实验室检查报告", tags = "实验室检查报告")
@RequestMapping(Project.PLATFORM + "/patient/labTestReport")
@RequiredArgsConstructor
public class LabTestReportController {
    private final PatientService patientService;

    @ApiOperation(value = "报告列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<PatientListVO>> list(String patientId) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        return Render.success(patientService.labTestReport(patientId));
    }

    @ApiOperation(value = "添加报告", notes = "")
    @PostMapping(path = "/add")
    public String add(String patientId, String type, String attachmentId) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        BizAssert.notEmpty(type, "报告类型不能为空");
        BizAssert.notEmpty(attachmentId, "附件ID不能为空");
        patientService.addLabTestReport(patientId, AttachmentTypeEnum.get(type), attachmentId);
        return RenderJson.success();
    }


}
