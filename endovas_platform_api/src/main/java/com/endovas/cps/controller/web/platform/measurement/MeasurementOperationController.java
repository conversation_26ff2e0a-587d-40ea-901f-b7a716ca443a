package com.endovas.cps.controller.web.platform.measurement;

import com.endovas.cps.pojo.fo.measurement.MeasurementOperationParamFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationAllocVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationPrepareVO;
import com.endovas.cps.service.measurement.MeasurementOperationService;
import com.endovas.cps.service.measurement.MeasurementTaskService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:30
 */
@Slf4j
@RestController
@Api(value = "测量操作", tags = "测量操作接口")
@RequestMapping(Project.PLATFORM + "/measureOp")
@RequiredArgsConstructor
public class MeasurementOperationController {

    private final MeasurementTaskService measurementTaskService;
    private final MeasurementOperationService measurementOperationService;

    @ApiOperation(value = "尝试分配设备", notes = "")
    @PostMapping(path = "/tryAllocateEquip")
    public String tryAllocateEquip(@Validated MeasurementOperationParamFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        MeasurementOperationAllocVO vo = measurementOperationService.tryAllocateEquip(input, loginUser);
        return RenderJson.success(vo);
    }

    @ApiOperation(value = "准备测量任务", notes = "")
    @PostMapping(path = "/prepare")
    public String add(@Validated MeasurementOperationParamFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        MeasurementOperationPrepareVO vo = measurementOperationService.prepare(input, loginUser);
        return RenderJson.success(vo);
    }

    @ApiOperation(value = "查询设备当前的用户", notes = "")
    @PostMapping(path = "/queryEquipCurrentUser")
    public String queryEquipCurrentUser(@Validated MeasurementOperationParamFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        MeasurementOperationAllocVO vo = measurementOperationService.queryEquipCurrentUser(input, loginUser);
        return RenderJson.success(vo);
    }


    @ApiOperation(value = "在线测量回放", notes = "")
    @GetMapping(path = "/listRecord")
    public BaseResult<List<MeasurementOperationListVO>> listRecord(MeasurementOperationParamFO searchFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementOperationService.listRecord(searchFO, loginUser));
    }


//    @ApiOperation(value = "我的测量", notes = "")
//    @GetMapping(path = "/listSelf")
//    public BaseResult<PageVO<MeasurementTaskListVO>> listSelf(MeasurementTaskSearchSelfFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
//        return Render.success(measurementTaskService.listSelfMissionPool(searchFO, pageFO, loginUser));
//    }
//
//    @ApiOperation(value = "添加测量任务", notes = "")
//    @PostMapping(path = "/add")
//    public String add(@Validated MeasurementTaskAddFO input,@ApiIgnore @CurrentUser LoginUser loginUser) {
//        measurementTaskService.add(input, loginUser);
//        return RenderJson.success();
//    }

}
