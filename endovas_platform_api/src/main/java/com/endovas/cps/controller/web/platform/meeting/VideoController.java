package com.endovas.cps.controller.web.platform.meeting;

import io.daige.starter.common.Project;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/18
 * Time: 14:44
 */
@Slf4j
@RestController("PlatformMeetingController")
@Api(value = "术中录播", tags = "术中录播接口")
@RequestMapping(Project.PLATFORM + "/meeting/device")
@RequiredArgsConstructor
public class VideoController {

    //todo 业务逻辑实现
    @ApiOperation(value = "重命名设备", notes = "")
    @PostMapping(path = "/modify")
    public String modify(String deviceId,String deviceName) {
        return RenderJson.success();
    }

    //todo 业务逻辑实现
    @ApiOperation(value = "删除录播", notes = "")
    @PostMapping(path = "/del")
    public String del(String deviceId) {
        BizAssert.notEmpty(deviceId, "deviceId不能为空");

        return RenderJson.success();
    }

}
