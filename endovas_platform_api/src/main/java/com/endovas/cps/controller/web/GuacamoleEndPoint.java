package com.endovas.cps.controller.web;

import com.endovas.cps.config.properties.GuacamoleProperties;
import com.endovas.cps.dao.measurement.MeasurementOperationDAO;
import com.endovas.cps.dao.platform.EquipmentDAO;
import com.endovas.cps.entity.measurement.MeasurementOperationRecord;
import com.endovas.cps.entity.platform.Equipment;
import com.endovas.cps.enums.EquipmentStatusEnum;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.GuacamoleSocket;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.InetGuacamoleSocket;
import org.apache.guacamole.net.SimpleGuacamoleTunnel;
import org.apache.guacamole.protocol.ConfiguredGuacamoleSocket;
import org.apache.guacamole.protocol.GuacamoleClientInformation;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.websocket.GuacamoleWebSocketTunnelEndpoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.CloseReason;
import javax.websocket.EndpointConfig;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/11/6
 * Time: 16:10
 */
@Slf4j
@ServerEndpoint(value = "/remote/ws", subprotocols = "guacamole")
@Component
public class GuacamoleEndPoint extends GuacamoleWebSocketTunnelEndpoint {
    private static GuacamoleProperties guacamoleProperties;
    private static EquipmentDAO equipmentDAO;
    private static MeasurementOperationDAO measurementOperationDAO;
    private static RedisHelper redisHelper;
    private static final String IGNORE_CERT = "true";
    private static final String SCREEN_RECORD_FILE_SUFFIX = ".guac";
    private static final String DOCKER_RECORD_PATH = "/tmp";
    private static Map<String, GuacamoleTunnel> tunnel_map = new ConcurrentHashMap<>();

    @Autowired
    private void setGuacamoleProperties(GuacamoleProperties guacamoleProperties) {
        GuacamoleEndPoint.guacamoleProperties = guacamoleProperties;
    }

    @Autowired
    void setEquipmentDAO(EquipmentDAO equipmentDAO) {
        GuacamoleEndPoint.equipmentDAO = equipmentDAO;
    }

    @Autowired
    void setMeasurementOperationDAO(MeasurementOperationDAO measurementOperationDAO) {
        GuacamoleEndPoint.measurementOperationDAO = measurementOperationDAO;
    }

    @Autowired
    void setRedisHelper(RedisHelper redisHelper) {
        GuacamoleEndPoint.redisHelper = redisHelper;
    }

    @Override
    protected GuacamoleTunnel createTunnel(Session session, EndpointConfig endpointConfig) throws GuacamoleException {
        String userId = session.getRequestParameterMap().get("userId").get(0);
        String measureOpRecordId = session.getRequestParameterMap().get("measureOpRecordId").get(0);
        String measureOpToken = session.getRequestParameterMap().get("measureOpToken").get(0);
        String height = session.getRequestParameterMap().get("height").get(0);
        String width = session.getRequestParameterMap().get("width").get(0);

        String menToken = redisHelper.strGet(RedisCacheKeys.getMeasurementOperationTokenKey(measureOpRecordId));
        log.info("发起远程连接 menToken:{}, measureOpToken:{}", menToken, measureOpToken);
        if (StringUtils.isBlank(menToken) || !menToken.equals(measureOpToken)) {
            log.info("token过期，无法连接");
            return null;
        }

        // 从数据库查询出测量操作记录和对应的设备信息
        MeasurementOperationRecord opRecord = measurementOperationDAO.findById(measureOpRecordId).orElseThrow(()->new BusinessAssertException("未查询到测量操作记录"));
        String fileName = getScreenRecordFileName(userId, measureOpRecordId);


        // 准备连接设备参数
        Equipment equip = equipmentDAO.findById(opRecord.getEquipmentId()).orElseThrow(()->new BusinessAssertException("未查询到设备信息"));
        GuacamoleConfiguration configuration = new GuacamoleConfiguration();
        configuration.setProtocol(equip.getProtocol());
        configuration.setParameter("hostname", equip.getHostname());
        configuration.setParameter("port", equip.getPort());
        configuration.setParameter("username", equip.getUsername());
        configuration.setParameter("password", equip.getPassword());
        configuration.setParameter("ignore-cert", IGNORE_CERT);
        configuration.setParameter("image-quality","100");
        configuration.setParameter("enable-audio","false");
        configuration.setParameter("disable-copyrect","true");
        configuration.setParameter("color-depth","24");
        configuration.setParameter("resize-method","display-update");
        GuacamoleClientInformation information = new GuacamoleClientInformation();
        information.setOptimalScreenHeight(Integer.valueOf(height));
        information.setOptimalScreenWidth(Integer.valueOf(width));

        // 设备操作录屏参数
        configuration.setParameter("recording-path", DOCKER_RECORD_PATH); // 这里不用配置文件中的保存地址，因为容器中的地址会映射出去
        configuration.setParameter("create-recording-path", "true");
        configuration.setParameter("recording-name", fileName);

        // 将设备id与用户id存进缓存
        redisHelper.strSet(RedisCacheKeys.getEquipInuseUserIdKey(equip.getId()), userId);
        // 移除旧的连接管道
        GuacamoleTunnel oldTunnel = tunnel_map.get(equip.getId());
        if (Objects.nonNull(oldTunnel)) {
            oldTunnel.close();
            tunnel_map.remove(equip.getId());
        }

        GuacamoleSocket socket = new ConfiguredGuacamoleSocket(
                new InetGuacamoleSocket(guacamoleProperties.getHost(), guacamoleProperties.getPort()),
                configuration,information
        );
        log.info("发起远程连接 measureOpRecordId:{}, host:{}, user:{}", measureOpRecordId, equip.getHostname(), equip.getUsername());
        GuacamoleTunnel tunnel = new SimpleGuacamoleTunnel(socket);

        // 存储新的连接管道
        tunnel_map.put(equip.getId(), tunnel);

        opRecord.setScreenRecordFileName(fileName);
        opRecord.setIsUsingEquip(true);
        measurementOperationDAO.save(opRecord);
        log.info("设置操作记录正在使用设备");

        equip = equipmentDAO.findById(opRecord.getEquipmentId()).orElseThrow(()->new BusinessAssertException("未查询到设备信息"));
        equip.setStatus(EquipmentStatusEnum.INUSE.getCode());
        equipmentDAO.save(equip);
        equipmentDAO.flush();
        log.info("标记设备正在使用:{}", equip.getId());

        return tunnel;
    }

    @Override
    public void onError(Session session, Throwable throwable) {
        super.onError(session, throwable);
        log.info("远程连接发生异常", throwable);
        String measureOpRecordId = session.getRequestParameterMap().get("measureOpRecordId").get(0);
        closeOperation(measureOpRecordId);
        log.info("关闭测量操作");
    }

    @Override
    public void onClose(Session session, CloseReason closeReason) {
        super.onClose(session, closeReason);
        log.info("远程连接关闭");
        String measureOpRecordId = session.getRequestParameterMap().get("measureOpRecordId").get(0);
        closeOperation(measureOpRecordId);
        log.info("关闭测量操作");
    }

    public void closeOperation(String measureOpRecordId) {
        // 将测量操作记录设置为完成
        MeasurementOperationRecord opRecord = measurementOperationDAO.findById(measureOpRecordId).orElseThrow(()->new BusinessAssertException("未查询到测量操作记录"));
        opRecord.setIsFinished(true);
        opRecord.setIsUsingEquip(false);
        measurementOperationDAO.save(opRecord);
        measurementOperationDAO.flush();
        log.info("--->将测量操作记录设置为完成");

        // 释放机器
        Equipment equip = equipmentDAO.findById(opRecord.getEquipmentId()).orElseThrow(()->new BusinessAssertException("微查询到设备信息"));
        equip.setStatus(EquipmentStatusEnum.IDLE.getCode());
        equipmentDAO.save(equip);
        equipmentDAO.flush();
        log.info("--->释放机器");

        // 清除token
        redisHelper.delKey(RedisCacheKeys.getMeasurementOperationTokenKey(measureOpRecordId));
        log.info("--->清除token");
    }

    //  /mnt/1234566777-453654756856785678.guac
    private String getScreenRecordFileName(String userId, String opRecordId) {
        String filePrefix = String.join("-", Arrays.asList(userId, opRecordId));
        return filePrefix + SCREEN_RECORD_FILE_SUFFIX;
    }
}
