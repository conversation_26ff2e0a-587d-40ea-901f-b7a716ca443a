package com.endovas.cps.controller.web.platform.agent;

import cn.hutool.core.util.StrUtil;
import com.endovas.cps.dao.agent.MedAgentDAO;
import com.endovas.cps.pojo.fo.agent.MedAgentUserAddByPlatformFO;
import com.endovas.cps.pojo.fo.agent.MedAgentUserEditByPlatformFO;
import com.endovas.cps.pojo.fo.agent.MedAgentUserSearchByPlatformFO;
import com.endovas.cps.pojo.vo.agent.MedAgentUserListByPlatformVO;
import com.endovas.cps.pojo.vo.platform.agent.MedAgentSelectVO;
import com.endovas.cps.service.agent.MedAgentUserService;
import com.google.common.collect.Lists;
import io.daige.starter.common.Project;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:36
 */
@Slf4j
@RestController("PlatformMedAgentUserController")
@Api(value = "代理商账号管理", tags = "代理商账号管理接口")
@RequestMapping(Project.PLATFORM + "/medAgent/user")
@RequiredArgsConstructor
public class MedAgentUserController {
    private final MedAgentUserService medAgentUserService;
    private final MedAgentDAO medAgentDAO;


    @ApiOperation(value = "可选代理商列表", notes = "")
    @GetMapping(path = "/medAgentSelect")
    public BaseResult<List<MedAgentSelectVO>> select(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(medAgentDAO.findAll().stream().map(x -> {
            MedAgentSelectVO one = new MedAgentSelectVO();
            one.setId(x.getId());
            one.setName(x.getName());
            return one;
        }).collect(Collectors.toList()));
    }

    @ApiOperation(value = "代理商账户列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<MedAgentUserListByPlatformVO>> list(@ApiIgnore @CurrentUser LoginUser loginUser, MedAgentUserSearchByPlatformFO searchFO, PageFO pageFO) {
        List<String> medAgentIds = Lists.newArrayList();
        if (StrUtil.isNotEmpty(searchFO.getMedAgentName())) {
            medAgentIds = medAgentDAO.findByNameContaining(searchFO.getMedAgentName()).stream().map(MysqlBase::getId).collect(Collectors.toList());
        }
        PageVO<MedAgentUserListByPlatformVO> result = medAgentUserService.list(searchFO, medAgentIds, pageFO);
        List<MedAgentUserListByPlatformVO> contents = result.getContent();
        for (MedAgentUserListByPlatformVO content : contents) {
            medAgentDAO.findById(content.getMedAgentId()).ifPresent(x->{
                content.setMedAgentName(x.getName());
            });
        }
        result.setContent(contents);
        return Render.success(result);
    }

    @ApiOperation(value = "添加代理商账户", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated MedAgentUserAddByPlatformFO input) {
        medAgentUserService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑代理商账户", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated MedAgentUserEditByPlatformFO input) {
        medAgentUserService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "初始化密码", notes = "")
    @PostMapping(path = "/initPass")
    public String initPass(String id) {
        BizAssert.notEmpty(id, "ID不能为空");
        medAgentUserService.initPass(id);
        return RenderJson.success();
    }

    @ApiOperation(value = "开启关闭账号", notes = "")
    @PostMapping(path = "/switchStatus")
    public String switchStatus(String id) {
        BizAssert.notEmpty(id, "ID不能为空");
        medAgentUserService.closeOpenAccount(id);
        return RenderJson.success();
    }

}
