package com.endovas.cps.controller.web.platform.organization;

import com.endovas.cps.pojo.fo.organization.OrganizationAddFO;
import com.endovas.cps.pojo.fo.organization.OrganizationUpdateFO;
import com.endovas.cps.pojo.fo.organization.UserSearchFO;
import com.endovas.cps.pojo.vo.OrganizationTreeVO;
import com.endovas.cps.pojo.vo.UserListVO;
import com.endovas.cps.pojo.vo.platform.PlatformUserVO;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.platform.PlatformUserService;
import io.daige.starter.common.Project;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * Created by IntelliJ IDEA.
 *
 * @author: gxh
 * Date: 2021/6/18
 * Time: 下午3:04
 */
@Slf4j
@RestController("PlatformOrganizationController")
@Api(value = "组织管理", tags = "组织管理接口")
@RequestMapping(Project.PLATFORM + "/org")
public class PlatformOrganizationController {
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PlatformUserService platformUserService;


    @ApiOperation(value = "组织下的用户列表", notes = "")
    @GetMapping(path = "/user/list")
    public String list(@ApiIgnore @CurrentUser LoginUser loginUser, UserSearchFO param, PageFO pageFO) {
        PlatformUserVO platformUser = platformUserService.getUser(loginUser.getId());
        if (StringUtils.isBlank(platformUser.getOrganizationId())) {
            param.setLoginUserOrgId(param.getOrganizationId());
        } else {
            param.setLoginUserOrgId(platformUser.getOrganizationId());
        }
        PageVO<UserListVO> userVOList = organizationService.listByOrganizationId(param, pageFO, loginUser.getBelong());
        return RenderJson.success(userVOList);

    }

    @ApiOperation(value = "添加", notes = "")
    @PostMapping(path = "/add")
    public String add(@ApiIgnore @CurrentUser LoginUser loginUser, OrganizationAddFO param) {
        BizAssert.isTrue(StringUtils.isNotBlank(param.getName()), "组织名称不能为空");
        organizationService.add(param, null, BelongEnum.PLATFORM_USER.getCode());
        return RenderJson.success("操作成功");
    }

    @ApiOperation(value = "更新", notes = "")
    @PostMapping(path = "/update")
    public String modify(@ApiIgnore @CurrentUser LoginUser loginUser, OrganizationUpdateFO param) {
        organizationService.modify(param);
        return RenderJson.success("操作成功");
    }

    @ApiOperation(value = "禁用启用", notes = "")
    @PostMapping(path = "/enable/{id}")
    public String enable(@ApiIgnore @CurrentUser LoginUser loginUser, @PathVariable String id) {
        organizationService.enable(id);
        return RenderJson.success("操作成功");
    }


    @ApiOperation(value = "列表", notes = "")
    @GetMapping(path = "/tree")
    public String tree(Boolean showAll, @ApiIgnore @CurrentUser LoginUser loginUser) {
        PlatformUserVO userVO = platformUserService.getUser(loginUser.getId());
        List<OrganizationTreeVO> results = organizationService.treeByOrganizationId(showAll, userVO.getOrganizationId(), null, loginUser.getBelong());
        return RenderJson.success(results);

    }
}
