package com.endovas.cps.controller.web.platform.dicom;

import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAddFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomEditFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomSearchFO;
import com.endovas.cps.pojo.vo.FileUploadKeyParam;
import com.endovas.cps.pojo.vo.OrganizationTreeVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalSelectVO;
import com.endovas.cps.pojo.vo.resource.DicomListVO;
import com.endovas.cps.pojo.vo.resource.DicomSelectVO;
import com.endovas.cps.pojo.vo.surgery.SurgeryTypeListVO;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.hospital.HospitalService;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.resource.DicomService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.google.common.collect.Maps;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/7
 * Time: 14:20
 */
@Slf4j
@RestController
@Api(value = "医疗影像", tags = "医疗影像接口")
@RequestMapping(Project.PLATFORM + "/dicom")
@RequiredArgsConstructor
public class DicomController {
    private final FileService fileService;
    private final DicomService dicomService;
    private final HospitalService hospitalService;
    private final SurgeryTypeService surgeryTypeService;
    private final OrganizationService organizationService;

    @ApiOperation(value = "影像文件下拉列表", notes = "")
    @GetMapping(path = "/preop/select")
    public BaseResult<List<DicomSelectVO>> dicomSelect(Boolean excludePatientId, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(dicomService.select(excludePatientId, SurgeryStageEnum.PREOP, loginUser));
    }

    @ApiOperation(value = "列表", notes = "")
    @GetMapping(path = "/org/tree")
    public String tree(@ApiIgnore @CurrentUser LoginUser loginUser) {
        List<OrganizationTreeVO> results = organizationService.treeFromRoot(null, loginUser.getBelong());
        return RenderJson.success(results);

    }


    @ApiOperation(value = "医院下拉列表", notes = "")
    @GetMapping(path = "/preop/hospital/select")
    public BaseResult<List<HospitalSelectVO>> hospitalSelect() {
        return Render.success(hospitalService.select());
    }


    @ApiOperation(value = "疾病类型下拉列表", notes = "")
    @GetMapping(path = "/preop/surgeryType/select")
    public BaseResult<List<SurgeryTypeListVO>> surgeryTypeSelect() {
        return Render.success(surgeryTypeService.select());
    }


    @ApiOperation(value = "获取影像文件列表")
    @GetMapping("/preop/list")
    public BaseResult<PageVO<DicomListVO>> list(@CurrentUser LoginUser loginUser, DicomSearchFO param, PageFO pageFO) {
        return Render.success(dicomService.list(param, SurgeryStageEnum.PREOP, pageFO, loginUser));
    }

    @ApiOperation(value = "添加影像文件")
    @PostMapping("/add")
    public BaseResult<FileUploadKeyParam> add(@Validated DicomAddFO param, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(dicomService.save(param, SurgeryStageEnum.valueOf(param.getSurgeryStage()), loginUser));
    }

    @ApiOperation(value = "更新影像文件状态为失败")
    @GetMapping("/updateStatusToFail")
    public BaseResult<String> updateStatusToFail(@Validated DicomSearchFO param, @ApiIgnore @CurrentUser LoginUser loginUser) {
        dicomService.updateStatusToFail(param, loginUser);
        return Render.success();
    }

    @ApiOperation(value = "刷新影像文件状态")
    @GetMapping("/refreshStatus")
    public BaseResult<List<DicomListVO>> refresh(@RequestParam List<String> ids, @ApiIgnore @CurrentUser LoginUser loginUser) {
        List<DicomListVO> result = dicomService.refreshStatus(ids, loginUser);
        return Render.success(result);
    }

    @ApiOperation(value = "获取影像文件预览地址")
    @GetMapping("/canView")
    public BaseResult<Map<String, Boolean>> canView(String dicomId) {
        BizAssert.notEmpty(dicomId, "影像文件ID不能为空");
        Map<String, Boolean> result = Maps.newHashMap();
        result.put("canView", fileService.dicomCanView(dicomId));
        return Render.success(result);
    }

    @ApiOperation(value = "编辑影像文件")
    @PostMapping("/edit")
    public BaseResult<String> edit(@ApiIgnore @CurrentUser LoginUser loginUser, @Validated DicomEditFO param) {
        dicomService.edit(param);
        return Render.success();
    }


    @ApiOperation(value = "删除影像文件")
    @PostMapping("/del")
    public BaseResult<String> del(@ApiIgnore @CurrentUser LoginUser loginUser, String id) {
        BizAssert.notEmpty(id, "影像ID不能为空");
        dicomService.del(id);
        return Render.success();
    }

}
