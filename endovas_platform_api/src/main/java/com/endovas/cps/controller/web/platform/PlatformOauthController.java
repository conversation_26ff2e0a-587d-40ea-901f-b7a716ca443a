package com.endovas.cps.controller.web.platform;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.config.properties.TokenProperties;
import com.endovas.cps.constant.DictConst;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.fo.ForgotPassWordFO;
import com.endovas.cps.pojo.fo.SendCaptchaSmsFO;
import com.endovas.cps.pojo.fo.platform.PlatformUserLoginFO;
import com.endovas.cps.pojo.vo.CurrentUserVO;
import com.endovas.cps.service.DictService;
import com.endovas.cps.service.UserService;
import com.endovas.cps.service.communication.sms.SMSApiRequest;
import com.endovas.cps.service.password.PasswordBuilder;
import com.endovas.cps.service.platform.PlatformUserService;
import com.endovas.cps.service.role.PermissionService;
import com.google.common.collect.Maps;
import io.daige.starter.common.Project;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.enums.LoginMode;
import io.daige.starter.common.enums.SmsBusinessScopeType;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.component.captcha.pojo.dto.ImageCaptchaDTO;
import io.daige.starter.component.captcha.pojo.dto.MessageCaptchaDTO;
import io.daige.starter.component.captcha.service.ImageCaptchaService;
import io.daige.starter.component.captcha.service.MessageCaptchaService;
import io.daige.starter.component.redis.service.RedisHelper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/22
 * Time: 下午4:21
 */
@Slf4j
@RestController
@RequestMapping(path = Project.PLATFORM)
@Api(value = "后台授权认证", tags = "登录接口")
@AllArgsConstructor
public class PlatformOauthController {
    private final PlatformUserService platformUserService;
    private final TokenProperties tokenProperties;
    private final ImageCaptchaService imageCaptchaService;
    private final MessageCaptchaService messageCaptchaService;
    private final RedisHelper redisHelper;
    private final PasswordBuilder passwordBuilder;
    private final PermissionService permissionService;
    private final SMSApiRequest smsApiRequest;
    private final UserService userService;

    private final DictService dictService;

    @ApiOperation(value = "当前账户是否需要图形验证码")
    @GetMapping(path = "/anon/needCaptcha")
    public String needCaptcha(@ApiParam(value = "账号") String account) {
        if (StrUtil.isEmpty(account)) {
            return RenderJson.fail("账户不能为空");
        }
        Map<String, Boolean> result = Maps.newHashMap();
        result.put("isSafe", this.accountIsSafe(account));
        return RenderJson.success(result);
    }

    /**
     * 登录
     *
     * @param platformUserLoginFO
     * @return
     */
    @ApiOperation(value = "登录")
    @PostMapping(path = "/anon/login")
    public String login(@Validated PlatformUserLoginFO platformUserLoginFO) {

        if (BooleanUtil.isFalse(accountIsSafe(platformUserLoginFO.getAccount()))) {
            if (StrUtil.isEmpty(platformUserLoginFO.getCaptchaKey()) || StrUtil.isEmpty(platformUserLoginFO.getCaptcha())) {
                return RenderJson.fail("图形验证码不能为空");
            }
            if (!imageCaptchaService.validate(platformUserLoginFO.getCaptchaKey(), platformUserLoginFO.getCaptcha(), true)) {
                return RenderJson.fail("图形验证码不正确,请刷新后重试");
            }
        }


        BizAssert.isTrue(platformUserService.check(platformUserLoginFO.getAccount()), "账号或密码错误");
        PlatformUser platformUser = platformUserService.getByAccount(platformUserLoginFO.getAccount());
        if (UserStatusEnum.CLOSE.getCode().equals(platformUser.getStatus())) {
            throw new BusinessAssertException("用户被禁止登录");
        }

        return loginByAccount(platformUserLoginFO, platformUser);
    }

    /**
     * 检测账户是否需要图形验证码
     *
     * @param account
     * @return
     */
    private Boolean accountIsSafe(String account) {
        //手机模式必须有图形验证码
        if (LoginMode.Tel.getCode().equals(tokenProperties.getMethod())) {
            return false;
        }
        String failedLogin = redisHelper.strGet(RedisCacheKeys.loginFailedCountCacheKey(account));
        return !Objects.nonNull(failedLogin) || Integer.valueOf(failedLogin) < tokenProperties.getShowCaptchaTimes();
    }

    private String loginByAccount(PlatformUserLoginFO platformUserLoginFO, PlatformUser platformUser) {
        if (StrUtil.isEmpty(platformUserLoginFO.getPassword())) {
            return RenderJson.fail("密码不能为空");
        }
        String inputPassWord = SecureUtil.sha256(platformUserLoginFO.getPassword() + platformUser.getSalt());
        if (!inputPassWord.equals(platformUser.getPassword())) {
            redisHelper.strIncrement(RedisCacheKeys.loginFailedCountCacheKey(platformUserLoginFO.getAccount()), 1L);
            return RenderJson.fail("账号或密码错误");
        }
        redisHelper.delKey(RedisCacheKeys.loginFailedCountCacheKey(platformUserLoginFO.getAccount()));
        return RenderJson.success(createTokenAndCheckAutoLogin(platformUserLoginFO, platformUser));
    }


    private String createTokenAndCheckAutoLogin(PlatformUserLoginFO platformUserLoginFO, PlatformUser platformUser) {
        if (tokenProperties.getLoginKick()) {
            String existToken = redisHelper.strGet(RedisCacheKeys.getTokenKey(platformUser.getId()));
            if (StrUtil.isNotEmpty(existToken)) {
                redisHelper.delKey(RedisCacheKeys.getTokenPrefixKey(existToken));
            }
            redisHelper.delKey(RedisCacheKeys.getTokenKey(platformUser.getId()));
        }


        // 创建token
        String token = UUID.randomUUID().toString().replace("-", "");
        List<String> permissions = new ArrayList<>();
        for (Permission p : platformUser.getPermissions()) {
            if (StrUtil.isNotBlank(p.getCode())
                    && StrUtil.isNotBlank(p.getPath())) {
                permissions.add(p.getCode());
            }
        }
        for (Role r : platformUser.getRoles()) {
            permissions.add(r.getCode());
        }

        LoginUser loginUser = new LoginUser();
        loginUser.setId(platformUser.getId());
        loginUser.setAccount(platformUser.getAccount());
        loginUser.setTelephone(platformUser.getTelephone());
        loginUser.setPermissions(permissions);
        loginUser.setBelong(BelongEnum.PLATFORM_USER);

        loginUser.setNickName(platformUser.getNickName());
        //规则,多个角色如果有一个不脱敏(false)就不脱敏
        Boolean desensitize = platformUser.getRoles().stream().filter(x -> BooleanUtil.isFalse(x.getDesensitize()) || Objects.isNull(x.getDesensitize())).count() <= 0;
        loginUser.setDesensitize(desensitize);
        loginUser.setSpecialFields(permissionService.findCurrentUserFields(loginUser.getPermissions(), loginUser.getId(), BelongEnum.PLATFORM_USER));
        loginUser.setOrgId(platformUser.getOrganizationId());
        String roleName = platformUser.getRoles().stream().map(Role::getName).collect(Collectors.joining(","));
        //获取用户的数据权限,取角色的最小值
        Integer dataType = platformUser.getRoles().stream().mapToInt(Role::getDataType).min().getAsInt();
        loginUser.setDataType(dataType);
        loginUser.setRoleName(roleName);


        loginUser.setSaveLogin(false);
        redisHelper.strSet(RedisCacheKeys.getTokenKey(platformUser.getId()), token, dictService.getSysOrDefault(DictConst.WEB_TOKEN_EXPIRE_TIME_MIN, tokenProperties.getTokenExpireTime()), TimeUnit.MINUTES);
        redisHelper.strSet(RedisCacheKeys.getTokenPrefixKey(token), JSONUtil.toJsonStr(loginUser), dictService.getSysOrDefault(DictConst.WEB_TOKEN_EXPIRE_TIME_MIN, tokenProperties.getTokenExpireTime()), TimeUnit.MINUTES);


        platformUser.setLastLoginTime(LocalDateTime.now());
        platformUserService.updateUser(platformUser);
        return token;
    }


    /**
     * 忘记密码
     *
     * @param forgotPassWordFO
     * @return
     */
    @ApiOperation(value = "忘记密码")
    @PostMapping(path = "/anon/forgot")
    public String forgot(@Validated ForgotPassWordFO forgotPassWordFO) {

        if (StrUtil.isNotEmpty(forgotPassWordFO.getBelong()) && StrUtil.isNotEmpty(forgotPassWordFO.getEnterpriseId())) {
            passwordBuilder.getPasswordStrategyService(tokenProperties.getPasswordStrategy()).check(forgotPassWordFO.getPassword());
            MessageCaptchaDTO result = messageCaptchaService.validate(forgotPassWordFO.getCaptchaKey(), forgotPassWordFO.getCaptcha(), forgotPassWordFO.getTelephone(), SmsBusinessScopeType.FORGET_PASS.getCode());
            if (!result.isSuccess()) {
                return RenderJson.fail(result.getMessage());
            }

            BizAssert.isTrue(userService.checkByTel(forgotPassWordFO.getTelephone(), forgotPassWordFO.getBelong(), forgotPassWordFO.getEnterpriseId()), "手机号码不存在");
            userService.resetPassword(forgotPassWordFO.getTelephone(), forgotPassWordFO.getBelong(), forgotPassWordFO.getEnterpriseId(), forgotPassWordFO.getPassword());
            return RenderJson.success();
        } else {
            passwordBuilder.getPasswordStrategyService(tokenProperties.getPasswordStrategy()).check(forgotPassWordFO.getPassword());
            MessageCaptchaDTO result = messageCaptchaService.validate(forgotPassWordFO.getCaptchaKey(), forgotPassWordFO.getCaptcha(), forgotPassWordFO.getTelephone(), SmsBusinessScopeType.FORGET_PASS.getCode());
            if (!result.isSuccess()) {
                return RenderJson.fail(result.getMessage());
            }
            BizAssert.isTrue(platformUserService.checkByTel(forgotPassWordFO.getTelephone()), "手机号码不存在");
            PlatformUser platformUser = platformUserService.getByTel(forgotPassWordFO.getTelephone());
            platformUserService.resetPassword(platformUser.getId(), forgotPassWordFO.getPassword());
            return RenderJson.success();
        }


    }

    @ApiOperation(value = "获取短信验证码")
    @PostMapping(path = "/anon/sendSms")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = MessageCaptchaDTO.class)})
    public String sendSms(@Validated SendCaptchaSmsFO sendCaptchaSmsFO) {
        if (!imageCaptchaService.validate(sendCaptchaSmsFO.getCaptchaKey(), sendCaptchaSmsFO.getCaptcha(), true)) {
            return RenderJson.fail("图形验证码不正确,请刷新后重试");
        }
        MessageCaptchaDTO result = messageCaptchaService.generateMobileCaptcha(sendCaptchaSmsFO.getTelephone());
        if (result.isSuccess()) {
            smsApiRequest.sendCaptcha(sendCaptchaSmsFO.getTelephone(), result.getCaptcha(), sendCaptchaSmsFO.getBusinessScope());
        } else {
            throw new BusinessAssertException(result.getMessage());
        }
        return RenderJson.success(result.getCaptchaKey());
    }

    @ApiOperation(value = "登出", notes = "登出")
    @PostMapping(path = "/logout")
    public String logout(@ApiIgnore @CurrentUser LoginUser loginUser) {
        String token = redisHelper.strGet(RedisCacheKeys.getTokenKey(loginUser.getId()));
        redisHelper.delKey(RedisCacheKeys.getTokenKey(loginUser.getId()));
        redisHelper.delKey(RedisCacheKeys.getTokenPrefixKey(token));
        redisHelper.delKey(RedisCacheKeys.getMenuKey(loginUser.getId()));
        return RenderJson.success("操作成功");
    }

    @ApiOperation(value = "获取当前账户信息")
    @GetMapping(path = "/currentUser")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = LoginUser.class)})
    public BaseResult<CurrentUserVO> currentUser(@ApiIgnore @CurrentUser LoginUser loginUser) {
        CurrentUserVO result = new CurrentUserVO();
        result.setLoginUser(loginUser);
        result.setMenuAndBtn(permissionService.findCurrentUserMenusAndBtns(loginUser.getPermissions(), loginUser.getId(), loginUser.getBelong()));
        return Render.success(result);
    }


    @ApiOperation(value = "获取图形验证码")
    @GetMapping(path = "/anon/captcha")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ImageCaptchaDTO.class)})
    public String captcha() {
        return RenderJson.success(imageCaptchaService.generateBase64Obj());
    }


}
