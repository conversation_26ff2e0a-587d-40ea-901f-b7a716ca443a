package com.endovas.cps.controller.web.platform;

import com.endovas.cps.pojo.fo.version.VersionAddFO;
import com.endovas.cps.pojo.fo.version.VersionEditFO;
import com.endovas.cps.pojo.vo.version.VersionListVO;
import com.endovas.cps.pojo.vo.version.VersionSelectVO;
import com.endovas.cps.pojo.vo.version.VersionUpdateStrategyVO;
import com.endovas.cps.service.version.VersionService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/25
 * Time: 下午1:03
 */
@Slf4j
@RestController
@Api(value = "version", tags = "客户端版本管理")
@RequestMapping(Project.PLATFORM + "/version")
@RequiredArgsConstructor
public class VersionController {
    private final VersionService versionService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "列表")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = VersionListVO.class)})
    public String list(PageFO pageFO) {
        return RenderJson.success(versionService.list(pageFO));
    }

    @GetMapping(value = "/select")
    @ApiOperation(value = "下拉列表")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = VersionSelectVO.class)})
    public String select(String terminalType, String os) {
        BizAssert.notEmpty(terminalType, "应用类型不能为空");
        BizAssert.notEmpty(os, "终端系统不能为空");
        return RenderJson.success(versionService.select(terminalType, os));
    }

    @PostMapping(value = "/available")
    @ApiOperation(value = "应用版本")
    public String remove(String id) {
        versionService.switchAvailable(id);
        return RenderJson.success();
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "添加版本")
    public String add(@Validated VersionAddFO input) {
        versionService.add(input);
        return RenderJson.success();
    }

    @PostMapping(value = "/modify")
    @ApiOperation(value = "编辑版本")
    public String modify(@Validated VersionEditFO input) {
        versionService.edit(input);
        return RenderJson.success();
    }

    @GetMapping(value = "/strategy/list")
    @ApiOperation(value = "根据版本ID获取更新策略列表")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = VersionUpdateStrategyVO.class)})
    public String strategyList(String versionId) {
        BizAssert.notEmpty(versionId, "版本ID不能为空");
        return RenderJson.success(versionService.listStrategy(versionId));
    }


}
