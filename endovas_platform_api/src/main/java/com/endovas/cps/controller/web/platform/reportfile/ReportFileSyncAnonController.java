package com.endovas.cps.controller.web.platform.reportfile;

import com.endovas.cps.pojo.fo.measurement.filesync.QueryReportFileExistFO;
import com.endovas.cps.pojo.vo.measurement.filesync.QueryReportFileExistVO;
import com.endovas.cps.service.measurement.ReportFileSyncService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/26
 * @Time: 15:19
 */
@RestController
@Api(tags = "报告文件同步接口")
@RequestMapping(Project.ANON + "/reportFile")
@Slf4j
@RequiredArgsConstructor
public class ReportFileSyncAnonController {

    private final ReportFileSyncService reportFileSyncService;


    @GetMapping("/equipUserList")
    public BaseResult<List<String>> equipUserList(String hostname,String protocol) {
        return Render.success(reportFileSyncService.getEquipUserNameList(hostname,protocol));
    }

    @GetMapping("/reportFileExist")
    public BaseResult<QueryReportFileExistVO> reportFileExist(@RequestBody QueryReportFileExistFO input) {
        return Render.success(reportFileSyncService.reportFileExist(input));
    }

    @PostMapping(value = "/uploadReportFile")
    @ApiOperation(value = "上传报告文件")
    public BaseResult<String> uploadEncryptFile(MultipartFile file, @ApiParam("测量任务id") String measurementTaskId,
                                                @ApiParam("文件HASH") String fileHash) throws Exception {
        reportFileSyncService.uploadReportFile(file, measurementTaskId, fileHash);
        return Render.success("上传成功");
    }

}
