package com.endovas.cps.controller.web.platform.dicom;

import com.endovas.cps.pojo.fo.resource.NormalFileAddFO;
import com.endovas.cps.pojo.fo.resource.NormalFileEditFO;
import com.endovas.cps.service.resource.NormalFileService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/7
 * Time: 14:20
 */
@Slf4j
@RestController
@Api(value = "普通影像文件", tags = "普通影像文件接口")
@RequestMapping(Project.PLATFORM + "/normalFile")
@RequiredArgsConstructor
public class NormalFileController {
    private final NormalFileService normalFileService;
    @ApiOperation(value = "上传普通影像", notes = "")
    @PostMapping(path = "/add")
    public String normalFileAdd(@Validated NormalFileAddFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        normalFileService.add(input, loginUser);
        return RenderJson.success();
    }


    @ApiOperation(value = "编辑影像文件")
    @PostMapping("/edit")
    public BaseResult<String> edit(@ApiIgnore @CurrentUser LoginUser loginUser, @Validated NormalFileEditFO param) {
        normalFileService.edit(param);
        return Render.success();
    }


    @ApiOperation(value = "删除影像文件")
    @PostMapping("/del")
    public BaseResult<String> del(@ApiIgnore @CurrentUser LoginUser loginUser, String id) {
        BizAssert.notEmpty(id, "影像ID不能为空");
        normalFileService.del(id);
        return Render.success();
    }

}
