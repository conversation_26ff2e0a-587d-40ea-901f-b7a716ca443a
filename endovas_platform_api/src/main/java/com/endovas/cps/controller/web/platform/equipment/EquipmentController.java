package com.endovas.cps.controller.web.platform.equipment;

import com.endovas.cps.pojo.fo.equipment.EquipmentAddFO;
import com.endovas.cps.pojo.fo.equipment.EquipmentEditFO;
import com.endovas.cps.pojo.fo.equipment.EquipmentSearchFO;
import com.endovas.cps.pojo.vo.OrganizationTreeVO;
import com.endovas.cps.pojo.vo.platform.equip.EquipmentListVO;
import com.endovas.cps.pojo.vo.platform.PlatformUserVO;
import com.endovas.cps.service.equipment.EquipmentService;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.platform.PlatformUserService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 10:28
 */
@Slf4j
@RestController
@Api(value = "设备资产管理", tags = "设备资产管理接口")
@RequestMapping(Project.PLATFORM + "/equipment")
@RequiredArgsConstructor
public class EquipmentController {
    private final EquipmentService equipmentService;
    private final PlatformUserService platformUserService;
    private final OrganizationService organizationService;

    @ApiOperation(value = "设备资产列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<EquipmentListVO>> list(@ApiIgnore @CurrentUser LoginUser loginUser, EquipmentSearchFO searchFO, PageFO pageFO) {
        return Render.success(equipmentService.list(searchFO, pageFO));
    }

    @ApiOperation(value = "添加设备资产", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated EquipmentAddFO input) {
        equipmentService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑设备资产", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated EquipmentEditFO input) {
        equipmentService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "组织下拉列表", notes = "")
    @GetMapping(path = "/org/select")
    public String tree(@ApiIgnore @CurrentUser LoginUser loginUser) {
        PlatformUserVO userVO = platformUserService.getUser(loginUser.getId());
        List<OrganizationTreeVO> results = organizationService.treeByOrganizationId(false, userVO.getOrganizationId(), null, loginUser.getBelong());
        return RenderJson.success(results);
    }
}
