package com.endovas.cps.controller.web.platform.measurement;

/**
 * @author: wk
 * @Date: 2024/11/25
 * @Time: 14:33
 */

import com.endovas.cps.pojo.fo.measurement.MeasurementResultAddFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskResultFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskResultVO;
import com.endovas.cps.service.measurement.MeasurementResultService;
import io.daige.starter.common.Project;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Slf4j
@RestController
@Api(value = "测量结果", tags = "测量操作结果")
@RequestMapping(Project.PLATFORM + "/measureResult")
@RequiredArgsConstructor
public class MeasurementResultController {


    private final MeasurementResultService measurementResultService;

    @ApiOperation(value = "上传测量结果", notes = "")
    @PostMapping(path = "/upload")
    public String normalFileAdd(@Validated MeasurementResultAddFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        measurementResultService.upload(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "保存测量结果", notes = "")
    @PostMapping(path = "/saveResult")
    public String saveResult(@Validated MeasurementTaskResultFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        // 验证框架有问题，大字符串时，发生空值错误
        if(StringUtils.isBlank(input.getMeasurementId())) {
            throw new BusinessAssertException("测量id不能为空");
        }
        if(StringUtils.isBlank(input.getResult())) {
            throw new BusinessAssertException("测量结果内容不能为空");
        }
        measurementResultService.saveResult(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "删除测量结果", notes = "")
    @PostMapping(path = "/delete")
    public String delete(@Validated MeasurementTaskResultFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        if(StringUtils.isBlank(input.getId())) {
            throw new BusinessAssertException("测量结果id不能为空");
        }
        measurementResultService.delete(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "获取测量结果", notes = "")
    @GetMapping(path = "/getResult")
    public BaseResult<MeasurementTaskResultVO> getResult(MeasurementTaskResultFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementResultService.getResult(input, loginUser));
    }

    @ApiOperation(value = "获取测量结果列表", notes = "")
    @GetMapping(path = "/getResultList")
    public BaseResult<List<MeasurementTaskResultListVO>> getResultList(MeasurementTaskResultFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementResultService.getResultList(input, loginUser));
    }

}
