package com.endovas.cps.controller.web.platform.label;

import com.endovas.cps.pojo.fo.label.*;
import com.endovas.cps.pojo.vo.label.LabelTaskInfoVO;
import com.endovas.cps.pojo.vo.label.LabelTaskListVO;
import com.endovas.cps.service.label.LabelTaskService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:30
 */
@Slf4j
@RestController
@Api(value = "标注任务", tags = "标注任务接口")
@RequestMapping(Project.PLATFORM + "/label")
@RequiredArgsConstructor
public class LabelTaskController {

    private final LabelTaskService labelTaskService;

    @ApiOperation(value = "我的测量", notes = "")
    @GetMapping(path = "/listSelf")
    public BaseResult<PageVO<LabelTaskListVO>> listSelf(LabelTaskSearchSelfFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(labelTaskService.listSelfMissionPool(searchFO, pageFO, loginUser));
    }

    @ApiOperation(value = "任务列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<LabelTaskListVO>> list(LabelTaskSearchFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(labelTaskService.listMissionPool(searchFO, pageFO, loginUser));
    }

    @ApiOperation(value = "详情", notes = "")
    @GetMapping(path = "/detail")
    public BaseResult<LabelTaskInfoVO> detail(LabelTaskDetailFO detailFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(labelTaskService.detail(detailFO, loginUser));
    }

    @ApiOperation(value = "添加测量任务", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated LabelTaskAddFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        labelTaskService.add(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑测量任务", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated LabelTaskEditFO input) {
        labelTaskService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "领取", notes = "")
    @PostMapping(path = "/collect")
    public String collect(@Validated LabelTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        labelTaskService.collect(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "放弃", notes = "")
    @PostMapping(path = "/giveUp")
    public String giveUp(@Validated LabelTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        labelTaskService.giveUp(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "完成", notes = "")
    @PostMapping(path = "/finish")
    public String finish(@Validated LabelTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        labelTaskService.finish(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "关闭", notes = "")
    @PostMapping(path = "/close")
    public String close(@Validated LabelTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        labelTaskService.close(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "重启", notes = "")
    @PostMapping(path = "/reOpen")
    public String reOpen(@Validated LabelTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        labelTaskService.reOpen(input, loginUser);
        return RenderJson.success();
    }

}
