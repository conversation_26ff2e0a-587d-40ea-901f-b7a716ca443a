package com.endovas.cps.controller.web.platform.expert;

import com.endovas.cps.pojo.fo.expert.ExpertAddFO;
import com.endovas.cps.pojo.fo.expert.ExpertEditFO;
import com.endovas.cps.pojo.fo.expert.ExpertSearchFO;
import com.endovas.cps.pojo.vo.ExpertUserListVO;
import com.endovas.cps.service.expert.ExpertService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/1
 * Time: 11:36
 */
@Slf4j
@RestController
@Api(value = "专家账号管理", tags = "专家账号管理接口")
@RequestMapping(Project.PLATFORM + "/expert")
@RequiredArgsConstructor
public class ExpertController {
    private final ExpertService expertUserService;

    @ApiOperation(value = "专家账户列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<ExpertUserListVO>> list(@ApiIgnore @CurrentUser LoginUser loginUser, ExpertSearchFO searchFO, PageFO pageFO) {
        return Render.success(expertUserService.list(searchFO, pageFO));
    }

    @ApiOperation(value = "添加专家账户", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated ExpertAddFO input) {
        expertUserService.add(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑专家账户", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated ExpertEditFO input) {
        expertUserService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "初始化密码", notes = "")
    @PostMapping(path = "/initPass")
    public String initPass(String id) {
        BizAssert.notEmpty(id, "ID不能为空");
        expertUserService.initPass(id);
        return RenderJson.success();
    }

    @ApiOperation(value = "开启关闭账号", notes = "")
    @PostMapping(path = "/switchStatus")
    public String switchStatus(String id) {
        BizAssert.notEmpty(id, "ID不能为空");
        expertUserService.closeOpenAccount(id);
        return RenderJson.success();
    }

}
