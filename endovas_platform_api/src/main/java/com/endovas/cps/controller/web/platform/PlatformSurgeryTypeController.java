package com.endovas.cps.controller.web.platform;

import com.endovas.cps.pojo.fo.surgery.SurgeryTypeAddFO;
import com.endovas.cps.pojo.fo.surgery.SurgeryTypeEditFO;
import com.endovas.cps.pojo.vo.surgery.SurgeryTypeListVO;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2024/7/15
 * Time: 15:00
 */

@Slf4j
@RestController
@Api(value = "surgeryType", tags = "疾病分类")
@RequestMapping(Project.PLATFORM + "/surgeryType")
@RequiredArgsConstructor
public class PlatformSurgeryTypeController {
    private final SurgeryTypeService surgeryTypeService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "列表")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = SurgeryTypeListVO.class)})
    public String list() {
        return RenderJson.success(surgeryTypeService.list());
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "添加")
    public String add(@Validated SurgeryTypeAddFO input) {
        surgeryTypeService.add(input);
        return RenderJson.success();
    }

    @PostMapping(value = "/modify")
    @ApiOperation(value = "编辑")
    public String modify(@Validated SurgeryTypeEditFO input) {
        surgeryTypeService.edit(input);
        return RenderJson.success();
    }


    @ApiOperation(value = "删除", notes = "")
    @PostMapping(path = "/del")
    public String del(String id) {
        BizAssert.notEmpty(id, "疾病类别ID不能为空");
        surgeryTypeService.del(id);
        return RenderJson.success();
    }

    @PostMapping(value = "/available")
    @ApiOperation(value = "隐藏/显示")
    public String available(String id) {
        surgeryTypeService.switchAvailable(id);
        return RenderJson.success();
    }


}
