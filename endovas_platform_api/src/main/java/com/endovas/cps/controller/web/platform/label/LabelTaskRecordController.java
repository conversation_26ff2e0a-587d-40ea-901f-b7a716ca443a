package com.endovas.cps.controller.web.platform.label;

import com.endovas.cps.pojo.fo.label.LabelTaskRecordSearchFO;
import com.endovas.cps.pojo.vo.label.LabelTaskRecordListVO;
import com.endovas.cps.service.label.LabelTaskRecordService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:47
 */
@Slf4j
@RestController
@Api(value = "标注任务日志", tags = "标注任务日志接口")
@RequestMapping(Project.PLATFORM + "/labelRecord")
@RequiredArgsConstructor
public class LabelTaskRecordController {

    private final LabelTaskRecordService labelTaskRecordService;

    @ApiOperation(value = "任务列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<List<LabelTaskRecordListVO>> list(LabelTaskRecordSearchFO searchFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(labelTaskRecordService.list(searchFO, loginUser));
    }
}
