package com.endovas.cps.controller.web.platform.measurement;

import com.endovas.cps.pojo.fo.measurement.*;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskInfoVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskListVO;
import com.endovas.cps.service.measurement.MeasurementTaskService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:30
 */
@Slf4j
@RestController
@Api(value = "测量任务", tags = "测量任务接口")
@RequestMapping(Project.PLATFORM + "/measure")
@RequiredArgsConstructor
public class MeasurementTaskController {

    private final MeasurementTaskService measurementTaskService;

    @ApiOperation(value = "我的测量", notes = "")
    @GetMapping(path = "/listSelf")
    public BaseResult<PageVO<MeasurementTaskListVO>> listSelf(MeasurementTaskSearchSelfFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementTaskService.listSelfMissionPool(searchFO, pageFO, loginUser));
    }

    @ApiOperation(value = "任务列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<MeasurementTaskListVO>> list(MeasurementTaskSearchFO searchFO, PageFO pageFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementTaskService.listMissionPool(searchFO, pageFO, loginUser));
    }

    @ApiOperation(value = "详情", notes = "")
    @GetMapping(path = "/detail")
    public BaseResult<MeasurementTaskInfoVO> detail(MeasurementTaskDetailFO detailFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementTaskService.detail(detailFO, loginUser));
    }

    @ApiOperation(value = "添加测量任务", notes = "")
    @PostMapping(path = "/add")
    public BaseResult<MeasurementTaskInfoVO> add(@Validated MeasurementTaskAddFO input,@ApiIgnore @CurrentUser LoginUser loginUser) {
        MeasurementTaskInfoVO vo = measurementTaskService.add(input, loginUser);
        return Render.success(vo);
    }

    @ApiOperation(value = "编辑测量任务", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated MeasurementTaskEditFO input) {
        measurementTaskService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "领取", notes = "")
    @PostMapping(path = "/collect")
    public String collect(@Validated MeasurementTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        measurementTaskService.collect(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "放弃", notes = "")
    @PostMapping(path = "/giveUp")
    public String giveUp(@Validated MeasurementTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        measurementTaskService.giveUp(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "完成", notes = "")
    @PostMapping(path = "/finish")
    public String finish(@Validated MeasurementTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        measurementTaskService.finish(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "关闭", notes = "")
    @PostMapping(path = "/close")
    public String close(@Validated MeasurementTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        measurementTaskService.close(input, loginUser);
        return RenderJson.success();
    }

    @ApiOperation(value = "重启", notes = "")
    @PostMapping(path = "/reOpen")
    public String reOpen(@Validated MeasurementTaskModifyFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        measurementTaskService.reOpen(input, loginUser);
        return RenderJson.success();
    }

}
