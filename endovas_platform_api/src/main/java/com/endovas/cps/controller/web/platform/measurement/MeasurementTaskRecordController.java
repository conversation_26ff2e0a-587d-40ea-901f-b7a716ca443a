package com.endovas.cps.controller.web.platform.measurement;

import com.endovas.cps.pojo.fo.measurement.MeasurementTaskRecordSearchFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskRecordListVO;
import com.endovas.cps.service.measurement.MeasurementTaskRecordService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 17:47
 */
@Slf4j
@RestController
@Api(value = "测量任务日志", tags = "测量任务日志接口")
@RequestMapping(Project.PLATFORM + "/measureRecord")
@RequiredArgsConstructor
public class MeasurementTaskRecordController {

    private final MeasurementTaskRecordService measurementTaskRecordService;

    @ApiOperation(value = "任务列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<List<MeasurementTaskRecordListVO>> list(MeasurementTaskRecordSearchFO searchFO, @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(measurementTaskRecordService.list(searchFO, loginUser));
    }
}
