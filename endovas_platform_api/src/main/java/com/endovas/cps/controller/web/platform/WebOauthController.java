package com.endovas.cps.controller.web.platform;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.config.properties.TokenProperties;
import com.endovas.cps.constant.DictConst;
import com.endovas.cps.entity.Permission;
import com.endovas.cps.entity.Role;
import com.endovas.cps.enums.UserStatusEnum;
import com.endovas.cps.pojo.dto.WebLoginUserDTO;
import com.endovas.cps.pojo.fo.EnterpriseSearchFO;
import com.endovas.cps.pojo.fo.UserPassLoginFO;
import com.endovas.cps.pojo.vo.EnterpriseSelectVO;
import com.endovas.cps.service.DictService;
import com.endovas.cps.service.UserService;
import com.endovas.cps.service.agent.MedAgentService;
import com.endovas.cps.service.hospital.HospitalService;
import com.endovas.cps.service.role.PermissionService;
import com.google.common.collect.Lists;
import io.daige.starter.common.Project;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.enums.LoginMode;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.component.captcha.service.ImageCaptchaService;
import io.daige.starter.component.redis.service.RedisHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/2/22
 * Time: 下午4:21
 */
@Slf4j
@RestController
@RequestMapping(path = Project.BASE)
@Api(value = "前台Web端用户授权认证", tags = "登录接口")
@AllArgsConstructor
public class WebOauthController {
    private final TokenProperties tokenProperties;
    private final ImageCaptchaService imageCaptchaService;
    private final RedisHelper redisHelper;
    private final PermissionService permissionService;
    private final UserService userService;
    private final MedAgentService medAgentService;
    private final HospitalService hospitalService;
    private final DictService dictService;

    @ApiOperation(value = "模糊搜索企业名称")
    @GetMapping(path = "/anon/enterprise")
    public BaseResult<List<EnterpriseSelectVO>> enterprise(@Validated EnterpriseSearchFO input) {
        List<EnterpriseSelectVO> result = Lists.newArrayList();
        if (StrUtil.isEmpty(input.getName())) {
            return Render.success(result);
        }

        switch (BelongEnum.valueOf(input.getBelong())) {
            case MED_AGENT_USER: {
                result = medAgentService.select(input.getName());
                break;
            }
            case HOSPITAL_USER:{
                result = hospitalService.select(input.getName());
                break;
            }

        }
        return Render.success(result);
    }


    /**
     * 登录
     *
     * @param userPassLoginFO
     * @return
     */
    @ApiOperation(value = "代理商、医院用户登录")
    @PostMapping(path = "/anon/passLogin")
    public String passLogin(HttpServletRequest request, @Validated UserPassLoginFO userPassLoginFO) {
        if (!accountIsSafe(userPassLoginFO.getAccount())) {
            if (StrUtil.isEmpty(userPassLoginFO.getCaptchaKey()) || StrUtil.isEmpty(userPassLoginFO.getCaptcha())) {
                return RenderJson.fail("图形验证码不能为空");
            }
            if (!imageCaptchaService.validate(userPassLoginFO.getCaptchaKey(), userPassLoginFO.getCaptcha(), true)) {
                return RenderJson.fail("图形验证码不正确,请刷新后重试");
            }
        }

        BizAssert.isTrue(userService.check(userPassLoginFO), "账号或密码错误");
        WebLoginUserDTO webLoginUser = userService.getByAccount(userPassLoginFO.getAccount(), userPassLoginFO.getBelong(), userPassLoginFO.getEnterpriseId());
        if (UserStatusEnum.CLOSE.getCode().equals(webLoginUser.getStatus())) {
            throw new BusinessAssertException("用户被禁止登录");
        }


        return loginByAccount(userPassLoginFO, webLoginUser);
    }


    /**
     * 检测账户是否需要图形验证码
     *
     * @param account
     * @return
     */
    private Boolean accountIsSafe(String account) {
        //手机模式必须有图形验证码
        if (LoginMode.Tel.getCode().equals(tokenProperties.getMethod())) {
            return false;
        }
        String failedLogin = redisHelper.strGet(RedisCacheKeys.loginFailedCountCacheKey(account));
        return !Objects.nonNull(failedLogin) || Integer.valueOf(failedLogin) < tokenProperties.getShowCaptchaTimes();
    }

    private String loginByAccount(UserPassLoginFO userPassLoginFO, WebLoginUserDTO webLoginUser) {
        if (StrUtil.isEmpty(userPassLoginFO.getPassword())) {
            return RenderJson.fail("密码不能为空");
        }
        String inputPassWord = SecureUtil.sha256(userPassLoginFO.getPassword() + webLoginUser.getSalt());
        if (!inputPassWord.equals(webLoginUser.getPassword())) {
            redisHelper.strIncrement(RedisCacheKeys.loginFailedCountCacheKey(userPassLoginFO.getAccount()), 1L);
            return RenderJson.fail("账号或密码错误");
        }
        redisHelper.delKey(RedisCacheKeys.loginFailedCountCacheKey(userPassLoginFO.getAccount()));
        return RenderJson.success(createTokenAndCheckAutoLogin(userPassLoginFO.getAutoLogin(), webLoginUser));
    }


    private String createTokenAndCheckAutoLogin(Boolean autoLogin, WebLoginUserDTO webLoginUser) {
        if (tokenProperties.getLoginKick()) {
            String existToken = redisHelper.strGet(RedisCacheKeys.getTokenKey(webLoginUser.getId()));
            if (StrUtil.isNotEmpty(existToken)) {
                redisHelper.delKey(RedisCacheKeys.getTokenPrefixKey(existToken));
            }
            redisHelper.delKey(RedisCacheKeys.getTokenKey(webLoginUser.getId()));
        }


        // 创建token
        String token = UUID.randomUUID().toString().replace("-", "");
        List<String> permissions = new ArrayList<>();
        for (Permission p : webLoginUser.getPermissions()) {
            if (StrUtil.isNotBlank(p.getCode())
                    && StrUtil.isNotBlank(p.getPath())) {
                permissions.add(p.getCode());
            }
        }
        for (Role r : webLoginUser.getRoles()) {
            permissions.add(r.getCode());
        }

        LoginUser loginUser = new LoginUser();
        loginUser.setId(webLoginUser.getId());
        loginUser.setMedAgentId(webLoginUser.getMedAgentId());
        loginUser.setHospitalId(webLoginUser.getHospitalId());

        loginUser.setAccount(webLoginUser.getAccount());
        loginUser.setTelephone(webLoginUser.getTelephone());

        loginUser.setPermissions(permissions);
        loginUser.setNickName(webLoginUser.getNickName());
        loginUser.setDataType(webLoginUser.getDataType());
        loginUser.setBelong(webLoginUser.getBelong());

        //规则,多个角色如果有一个不脱敏(false)就不脱敏
        Boolean desensitize = webLoginUser.getRoles().stream().noneMatch(x -> BooleanUtil.isFalse(x.getDesensitize()) || Objects.isNull(x.getDesensitize()));
        loginUser.setDesensitize(desensitize);
        loginUser.setSpecialFields(permissionService.findCurrentUserFields(loginUser.getPermissions(), loginUser.getId(), webLoginUser.getBelong()));
        loginUser.setOrgId(webLoginUser.getOrganizationId());
        String roleName = webLoginUser.getRoles().stream().map(Role::getName).collect(Collectors.joining(","));
        loginUser.setRoleName(roleName);

        loginUser.setSaveLogin(false);
        redisHelper.strSet(RedisCacheKeys.getTokenKey(webLoginUser.getId()), token, dictService.getSysOrDefault(DictConst.WEB_TOKEN_EXPIRE_TIME_MIN, tokenProperties.getTokenExpireTime()), TimeUnit.MINUTES);
        redisHelper.strSet(RedisCacheKeys.getTokenPrefixKey(token), JSONUtil.toJsonStr(loginUser), dictService.getSysOrDefault(DictConst.WEB_TOKEN_EXPIRE_TIME_MIN, tokenProperties.getTokenExpireTime()), TimeUnit.MINUTES);

        userService.updateLoginTime(webLoginUser);

        return token;
    }




}
