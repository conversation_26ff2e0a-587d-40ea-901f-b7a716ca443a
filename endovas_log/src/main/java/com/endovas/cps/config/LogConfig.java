package com.endovas.cps.config;

import com.endovas.cps.service.OptLogService;
import io.daige.starter.component.logger.event.SysLogListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/3/16
 * Time: 下午7:42
 */
@Configuration
public class LogConfig {
    @Bean
    public SysLogListener sysLogListener(OptLogService optLogService) {
        return new SysLogListener(optLogService::save);
    }
}
