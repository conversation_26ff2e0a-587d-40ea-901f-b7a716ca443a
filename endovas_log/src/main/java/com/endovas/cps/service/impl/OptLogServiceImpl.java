
package com.endovas.cps.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.endovas.cps.dao.OptLogDAO;
import com.endovas.cps.dao.OptLogExtDAO;
import com.endovas.cps.entity.OptLog;
import com.endovas.cps.entity.OptLogExt;
import com.endovas.cps.service.OptLogService;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.component.logger.pojo.dto.OptLogDTO;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <p>
 * 业务实现类
 * 系统日志
 * </p>
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class OptLogServiceImpl implements OptLogService {
    private final OptLogExtDAO optLogExtDAO;
    private final OptLogDAO optLogDAO;
    private final RedisHelper redisHelper;

    @Override
    public void save(OptLogDTO entity) {
        String currentUser = redisHelper.strGet(RedisCacheKeys.getTokenPrefixKey(entity.getToken()));
        OptLog optLog = BeanUtil.toBean(entity, OptLog.class);
        OptLogExt optLogExt = BeanUtil.toBean(entity, OptLogExt.class);
        if (Objects.nonNull(currentUser)) {
            LoginUser loginUser = JSONUtil.toBean(currentUser, LoginUser.class);
            optLog.setCreateById(loginUser.getId());
            optLogExt.setCreateById(loginUser.getId());
        }
        optLogDAO.save(optLog);
        optLogExt.setId(optLog.getId());
        optLogExtDAO.insert(optLogExt);
    }

}

