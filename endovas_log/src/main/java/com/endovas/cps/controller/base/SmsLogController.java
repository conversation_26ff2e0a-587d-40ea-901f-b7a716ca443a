package com.endovas.cps.controller.base;


import com.endovas.cps.service.LoginLogService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static io.daige.starter.common.Project.PLATFORM;

/**
 * <p>
 * 前端控制器
 * 短信日志
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping(PLATFORM + "/smsLog")
@Api(value = "smsLog", tags = "短信日志")
@RequiredArgsConstructor
public class SmsLogController {
    private final LoginLogService loginLogService;


}
