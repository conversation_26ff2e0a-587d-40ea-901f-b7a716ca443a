
package com.endovas.cps.controller.base;


import com.endovas.cps.service.OptLogService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static io.daige.starter.common.Project.PLATFORM;


/**
 * <p>
 * 前端控制器
 * 系统管理操作日志
 * </p>
 */

@Slf4j
@RestController
@RequestMapping(PLATFORM + "/optLog")
@Api(value = "OptLog", tags = "系统管理操作日志")
@RequiredArgsConstructor
public class OptLogController {
    private final OptLogService optLogService;



}

