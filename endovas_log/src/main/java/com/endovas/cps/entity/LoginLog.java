package com.endovas.cps.entity;

import io.daige.starter.common.database.mongo.MongoBase;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <p>
 * 实体类
 * 登录日志
 * </p>
 */


@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "m_login_log")
public class LoginLog extends MongoBase {


    /**
     * 登录IP
     */
    private String requestIp;


    /**
     * 登录人ID
     */
    private String userId;
    /**
     * 登录人账号
     */
    private String account;

    /**
     * 登录描述
     */
    private String description;


    /**
     * 浏览器请求头
     */
    private String ua;

    /**
     * 浏览器名称
     */
    private String browser;

    /**
     * 浏览器版本
     */
    private String browserVersion;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 登录地点
     */
    private String location;

}
