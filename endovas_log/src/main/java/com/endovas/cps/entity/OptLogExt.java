package com.endovas.cps.entity;

import io.daige.starter.common.database.mongo.MongoBase;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <p>
 * 实体类
 * 系统日志扩展
 * </p>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "m_opt_log_ext")
public class OptLogExt extends MongoBase {

    /**
     * 请求参数
     */
    private String params;
    /**
     * 返回值
     */
    private String result;
    /**
     * 异常描述
     */
    private String exDetail;

    /**
     * 创建人ID
     */
    private String createById;

}
