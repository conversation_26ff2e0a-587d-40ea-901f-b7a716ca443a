package com.endovas.cps.dao;

import com.endovas.cps.entity.Dict;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DictDAO extends MysqlBaseRepo<Dict> {
    Dict getByDictGroupIdAndCode(String groupId, String code);

    Dict getByCode(String code);

    List<Dict> findByDictGroupIdAndActiveIsTrueOrderBySerialNumberAsc(String dictGroupId);

    List<Dict> findByDictGroupIdOrderBySerialNumberAsc(String dictGroupId);

    List<Dict> findByDictGroupIdAndCode(String groupId, String code);

    Dict findFirstByDictGroupIdOrderBySerialNumberDesc(String dictGroupId);

    Page<Dict> findByDictGroupIdOrderBySerialNumberAsc(String dictGroupId, Pageable pageable);

    Page<Dict> findByDictGroupIdAndNameContainsOrderBySerialNumberAsc(String dictGroupId, String name, Pageable pageable);

    Dict findByDictGroupIdAndName(String gid, String name);

    List<Dict> findByIdIn(List<String> ids);

    void deleteAllByDictGroupId(String groupId);
}
