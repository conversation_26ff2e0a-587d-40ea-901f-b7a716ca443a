package com.endovas.cps.dao;

import com.endovas.cps.entity.DictGroup;
import io.daige.starter.common.database.mysql.MysqlBaseRepo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DictGroupDAO extends MysqlBaseRepo<DictGroup> {
    DictGroup getByCode(String code);

    List<DictGroup> findByActiveIsTrue();

    List<DictGroup> findByActiveIsTrueAndType(String type);

    List<DictGroup> findByType(String type);

}
