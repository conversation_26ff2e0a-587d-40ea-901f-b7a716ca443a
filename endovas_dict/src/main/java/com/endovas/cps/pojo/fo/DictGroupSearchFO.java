package com.endovas.cps.pojo.fo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 **/
@ApiModel
@Setter
@Getter
@Accessors(chain = true)
public class DictGroupSearchFO {
    @ApiModelProperty(value = "编码，名称")
    private String code;
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "启用状态")
    private Boolean active;
    @ApiModelProperty(value = "备注")
    private String remark;

}
