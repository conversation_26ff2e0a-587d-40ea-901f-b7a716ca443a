package com.endovas.cps.pojo.vo;

import com.endovas.cps.entity.Dict;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *
 */
@Setter
@Getter
@Accessors(chain = true)
public class DictVO extends BaseVO implements BeanConvert<DictVO, Dict> {
    private String id;

    private String code; // 编码
    private String name; // 中文名字
    private Integer serialNumber;//排序
    private Boolean active;
    private String remark; // 备注
    private String remark1; // 英文
    private String remark2; // 国际化扩展字段
}
