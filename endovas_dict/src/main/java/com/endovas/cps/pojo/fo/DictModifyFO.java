package com.endovas.cps.pojo.fo;

import com.endovas.cps.entity.Dict;

import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 **/
@ApiModel
@Setter
@Getter
@Accessors(chain = true)
public class DictModifyFO implements BeanConvert<DictModifyFO, Dict> {

    private String id;

    @ApiModelProperty(value = "排序编号")
    @NotNull(message = "排序编号")
    private Integer serialNumber;

    @ApiModelProperty(value = "编码")
    @NotBlank(message = "编码不能为空")
    private String code;
    @ApiModelProperty(value = "枚举值")
    @NotBlank(message = "枚举值不能为空")
    private String name;
    @ApiModelProperty(value = "显示状态")
    @NotNull(message = "显示状态")
    private Boolean active;
    private String remark;
    private String remark1;//扩展字段1
    private String remark2;//扩展字段2

}
