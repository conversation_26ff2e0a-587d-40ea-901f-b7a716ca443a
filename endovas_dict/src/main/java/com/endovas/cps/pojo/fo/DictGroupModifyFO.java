package com.endovas.cps.pojo.fo;

import com.endovas.cps.entity.DictGroup;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 **/
@ApiModel
@Setter
@Getter
@Accessors(chain = true)
public class DictGroupModifyFO implements BeanConvert<DictGroupModifyFO, DictGroup> {

    @NotBlank(message = "ID不能为空")
    private String id;

    @ApiModelProperty(value = "值")
    private String name;

    @ApiModelProperty(value = "开启标识")
    @NotNull(message = "开启标识不能为空")
    private Boolean active;
    @ApiModelProperty(value = "备注信息")
    @NotBlank(message = "备注信息不能为空")
    private String remark;

    @Valid
    private List<DictModifyFO> dictList;
}
