package com.endovas.cps.pojo.fo;

import com.endovas.cps.entity.DictGroup;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 **/
@ApiModel
@Setter
@Getter
@Accessors(chain = true)
public class DictGroupAddFO implements BeanConvert<DictGroupAddFO, DictGroup> {
    @ApiModelProperty(value = "类型")
    @NotBlank(message = "类型不能为空")
    private String type;
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String code;
    @ApiModelProperty(value = "开启标识")
    @NotNull(message = "开启标识不能为空")
    private Boolean active;
    @ApiModelProperty(value = "备注信息")
    @NotBlank(message = "备注信息不能为空")
    private String remark;

    @ApiModelProperty(value = "值")
    private String name;
}
