package com.endovas.cps.pojo.vo;

import com.endovas.cps.entity.DictGroup;

import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@Setter
@Getter
@Accessors(chain = true)
public class DictGroupListVO extends BaseVO implements BeanConvert<DictGroupListVO, DictGroup> {
    private String id;
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    private String code; // 编码
    private String name; // 中文名字

    private String type;
    private Boolean active;
    private String remark;

    private List<DictVO> dictList;
}
