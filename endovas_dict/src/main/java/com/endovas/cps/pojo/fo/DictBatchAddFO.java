package com.endovas.cps.pojo.fo;

import com.endovas.cps.entity.DictGroup;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *
 * <AUTHOR>
 **/
@ApiModel
@Setter
@Getter
@Accessors(chain = true)
public class DictBatchAddFO implements BeanConvert<DictBatchAddFO, DictGroup> {

    @NotBlank(message = "字典组Id不能为空")
    private String id;
    @Valid
    private List<DictAddFO> dictList;

}
