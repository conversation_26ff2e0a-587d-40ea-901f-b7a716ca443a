package com.endovas.cps.pojo.fo;

import com.endovas.cps.entity.Dict;
import io.daige.starter.common.javabean.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 **/
@ApiModel
@Setter
@Getter
@Accessors(chain = true)
public class DictAddFO implements BeanConvert<DictAddFO, Dict> {
    @ApiModelProperty(value = "编码")
    private String code;
    @ApiModelProperty(value = "排序编号")
    private Integer serialNumber;
    @ApiModelProperty(value = "枚举值")
    private String name; // 中文名字
    @ApiModelProperty(value = "是否隐藏启用")
    private Boolean active;
    @ApiModelProperty(value = "备注")
    private String remark;

    private String remark1;
    private String remark2;
    private String remark3;

}
