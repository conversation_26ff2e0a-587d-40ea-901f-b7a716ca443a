package com.endovas.cps.enums;

import java.util.stream.Stream;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 字典类型
 * <AUTHOR>
 * @return
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum DictGroupTypeEnum implements BaseEnum {
    DICT("枚举"),
    PARAMETER("参数"),
    ;

    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static DictGroupTypeEnum match(String val, DictGroupTypeEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static DictGroupTypeEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(DictGroupTypeEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
