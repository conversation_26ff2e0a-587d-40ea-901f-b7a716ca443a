package com.endovas.cps.controller.base;

import com.endovas.cps.service.DictService;
import io.daige.starter.common.Project;
import io.daige.starter.common.render.RenderJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午2:21
 */
@Slf4j
@RequestMapping(path = Project.BASE + "/dict")
@Api(value = "字典信息", tags = "网页端-字典接口")
@RequiredArgsConstructor
@RestController
public class WebDictController {
    private final DictService dictService;
    @ApiOperation(value = "获取所有字典详情")
    @GetMapping("/listAll")
    public String listAllDict() {
        return RenderJson.success(dictService.listAllActiveDict());
    }

}
