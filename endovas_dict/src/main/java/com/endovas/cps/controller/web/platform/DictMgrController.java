package com.endovas.cps.controller.web.platform;

import com.endovas.cps.pojo.fo.DictBatchAddFO;
import com.endovas.cps.pojo.fo.DictGroupAddFO;
import com.endovas.cps.pojo.fo.DictGroupModifyFO;
import com.endovas.cps.pojo.fo.DictGroupSearchFO;
import com.endovas.cps.pojo.vo.DictGroupListVO;
import com.endovas.cps.pojo.vo.DictGroupVO;
import com.endovas.cps.service.DictService;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午2:21
 */
@Slf4j
@RequestMapping(path = Project.PLATFORM + "/dict")
@Api(value = "字典信息", tags = "字典接口")
@RequiredArgsConstructor
@RestController
public class DictMgrController {
    private final DictService dictService;

    @ApiOperation(value = "字典列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<DictGroupListVO>> list(DictGroupSearchFO param, PageFO pageFO) {
        PageVO<DictGroupListVO> voiPage = dictService.pageBySearchFO(param, pageFO);
        return Render.success(voiPage);
    }


    @ApiOperation(value = "字典详情")
    @GetMapping(path = "/detail")
    public BaseResult<DictGroupVO> detail(String id, String name, PageFO pageFO) {
        return Render.success(dictService.detail(id, name, pageFO));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "字典添加")
    public BaseResult addDict(@Validated DictGroupAddFO fo) {
        dictService.addDict(fo);
        return Render.success();
    }

    @PostMapping(value = "/batchAdd")
    @ApiOperation(value = "批量添加枚举")
    public BaseResult batchAdd(@Validated DictBatchAddFO fo, @ApiIgnore @CurrentUser LoginUser loginUser) {
        dictService.batchAddDict(fo);
        return Render.success();
    }

    @PostMapping(value = "/modify")
    @ApiOperation(value = "字典修改")
    public BaseResult modifyDict(@Validated DictGroupModifyFO fo) {
        dictService.modifyDict(fo);
        return Render.success();
    }

    @PostMapping(value = "/enable")
    @ApiOperation(value = "字典开启关闭")
    public BaseResult enable(String id) {
        dictService.switchAvailable(id);
        return Render.success();
    }

    @PostMapping(value = "/enableDictItem")
    @ApiOperation(value = "字典项目开启关闭")
    public BaseResult enableDictItem(String id, @ApiIgnore @CurrentUser LoginUser loginUser) {
        dictService.switchDictItemAvailable(id);
        return Render.success();
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "字典删除")
    public BaseResult delete(String id, @ApiIgnore @CurrentUser LoginUser loginUser) {
        dictService.deleteDict(id);
        return Render.success();
    }

    @PostMapping(value = "/deleteDictItem")
    @ApiOperation(value = "字典项目删除")
    public BaseResult deleteDictItem(String id, @ApiIgnore @CurrentUser LoginUser loginUser) {
        dictService.deleteDictItem(id);
        return Render.success();
    }


}
