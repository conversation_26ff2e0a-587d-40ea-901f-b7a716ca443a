package com.endovas.cps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.endovas.cps.dao.DictDAO;
import com.endovas.cps.dao.DictGroupDAO;
import com.endovas.cps.entity.Dict;
import com.endovas.cps.entity.DictGroup;
import com.endovas.cps.enums.DictGroupTypeEnum;
import com.endovas.cps.pojo.fo.*;
import com.endovas.cps.pojo.vo.DictGroupListVO;
import com.endovas.cps.pojo.vo.DictGroupVO;
import com.endovas.cps.pojo.vo.DictVO;
import com.endovas.cps.service.DictService;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.database.mysql.MysqlBase;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.utils.BizAssert;
import io.daige.starter.common.utils.PageUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午12:49
 */
@Service
@RequiredArgsConstructor
public class DictServiceImpl implements DictService {
    private final DictGroupDAO dictGroupDAO;
    private final DictDAO dictDAO;
    private final RedisHelper redisHelper;

    @Override
    public List<DictVO> listDict(String groupCode) {
        DictGroup dictGroup = dictGroupDAO.getByCode(groupCode);
        BizAssert.notNull(dictGroup, "字典组代码错误");
        List<DictVO> result = redisHelper.listGet(RedisCacheKeys.getSystemDictKey(groupCode), DictVO.class);
        if (CollectionUtils.isEmpty(result)) {
            result = dictDAO.findByDictGroupIdAndActiveIsTrueOrderBySerialNumberAsc(dictGroup.getId()).stream().map(x -> new DictVO().convertFrom(x)).collect(Collectors.toList());
            redisHelper.objectSet(RedisCacheKeys.getSystemDictKey(groupCode), result, 365, TimeUnit.DAYS);
        }

        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(d -> {
                d.setName(d.getName());
            });
        }

        return result;
    }

    @Override
    public List<DictVO> listDict(DictGroup dictGroup, Boolean needActive) {
        BizAssert.notNull(dictGroup, "字典组代码错误");
        List<DictVO> result;
        if (BooleanUtils.isFalse(needActive)) {
            result = dictDAO.findByDictGroupIdOrderBySerialNumberAsc(dictGroup.getId())
                    .stream().map(x -> new DictVO().convertFrom(x)).collect(Collectors.toList());
        } else {
            result = dictDAO.findByDictGroupIdAndActiveIsTrueOrderBySerialNumberAsc(dictGroup.getId())
                    .stream().map(x -> new DictVO().convertFrom(x)).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public String getOneParamValue(String key) {
        String v = redisHelper.strGet(RedisCacheKeys.getSystemConfigKey(key));
        if (StrUtil.isNotBlank(v)) {
            return v;
        }
        DictGroup dictGroup = dictGroupDAO.getByCode(key);
        return Objects.nonNull(dictGroup) && BooleanUtil.isTrue(dictGroup.getActive()) ? dictGroup.getName() : "";
    }

    @Override
    public Map<String, List<DictVO>> listAllActiveDict() {
        Map<String, List<DictVO>> result = new HashMap<>();
        List<DictGroup> dictGroups = dictGroupDAO.findByActiveIsTrueAndType(DictGroupTypeEnum.DICT.getCode());
        for (DictGroup dictGroup : dictGroups) {
            result.put(dictGroup.getCode(), listDict(dictGroup.getCode()));
        }
        return result;
    }

    @Override
    public Map<String, List<DictVO>> listAllDict(Boolean needActive) {
        Map<String, List<DictVO>> result = new HashMap<>();
        List<DictGroup> dictGroups = BooleanUtils.isFalse(needActive) ? dictGroupDAO.findByType(DictGroupTypeEnum.DICT.getCode())
                : dictGroupDAO.findByActiveIsTrueAndType(DictGroupTypeEnum.DICT.getCode());
        for (DictGroup dictGroup : dictGroups) {
            result.put(dictGroup.getCode(), listDict(dictGroup, needActive));
        }
        return result;
    }

    @Override
    public List<DictGroupVO> listDictGroup(String category) {
        return dictGroupDAO.findByActiveIsTrueAndType(category).stream()
                .map(x -> new DictGroupVO().convertFrom(x)).collect(Collectors.toList());
    }

    @Override
    public List<DictGroupVO> listActiveDictGroup(String category) {
        return dictGroupDAO.findByActiveIsTrueAndType(category).stream().filter(DictGroup::getActive)
                .map(x -> new DictGroupVO().convertFrom(x)).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> listActiveConfig() {

        Set<String> keys = redisHelper.keys(RedisCacheKeys.getSystemConfigKey("*"));
        Map<String, String> map = new HashMap<>();

        if (CollUtil.isNotEmpty(keys)) {
            for (String key : keys) {
                map.put(StrUtil.removePrefix(key, RedisCacheKeys.getSystemConfigKey("")), redisHelper.strGet(key));
            }
            return map;
        }

        List<DictGroupVO> list = listActiveDictGroup(DictGroupTypeEnum.PARAMETER.getCode());

        list.forEach(group -> {
            map.put(StrUtil.removePrefix(group.getCode(), RedisCacheKeys.getSystemConfigKey("")), group.getName());
            redisHelper.strSet(RedisCacheKeys.getSystemConfigKey(group.getCode()), group.getName());
        });

        return map;
    }

    @Override
    public PageVO<DictGroupListVO> pageBySearchFO(DictGroupSearchFO searchFO, PageFO pageFO) {

        Pageable pageable = PageRequest.of(pageFO.getPageNumber() - 1, pageFO.getPageSize(), Sort.by(Sort.Direction.DESC, DictGroup.COL_ACTIVE, MysqlBase.CREATE_TIME));
        // 查询条件
        DictGroup queryObject = new DictGroup();
        ExampleMatcher matcher = ExampleMatcher.matching();
        if (StrUtil.isNotBlank(searchFO.getCode())) {
            queryObject.setCode(StrUtil.nullToEmpty(searchFO.getCode()));
            matcher = matcher.withMatcher(DictGroup.COL_CODE, ExampleMatcher.GenericPropertyMatchers.contains());
        }

        if (StrUtil.isNotBlank(searchFO.getType())) {
            queryObject.setType(searchFO.getType());
            matcher = matcher.withMatcher(DictGroup.COL_TYPE, ExampleMatcher.GenericPropertyMatchers.exact());
        }

        // 状态
        if (ObjectUtil.isNotNull(searchFO.getActive())) {
            queryObject.setActive(searchFO.getActive());
            matcher = matcher.withMatcher(DictGroup.COL_ACTIVE, ExampleMatcher.GenericPropertyMatchers.exact());
        }

        // 备注
        if (ObjectUtil.isNotNull(searchFO.getRemark())) {
            queryObject.setRemark(searchFO.getRemark());
            matcher = matcher.withMatcher(DictGroup.COL_REMARK, ExampleMatcher.GenericPropertyMatchers.contains());
        }

        matcher = matcher.withIgnorePaths(MysqlBase.FIELD_ID, MysqlBase.COL_CREATE_TIME, MysqlBase.DEL, MysqlBase.VERSION);

        Example<DictGroup> example = Example.of(queryObject, matcher);
        // 查询分页
        Page<DictGroup> pages = dictGroupDAO.findAll(example, pageable);
        if (Objects.isNull(pages)) {
            return PageUtil.empty(pageFO);
        }

        // 转换对象
        pages.map(c -> BeanUtil.toBean(c, DictGroupListVO.class));
        return PageUtil.convert(pages);
    }


    @Override
    public DictGroupVO detail(String id, String name, PageFO pageFO) {
        Optional<DictGroup> dictGroupOptional = dictGroupDAO.findById(id);
        if (dictGroupOptional.isPresent()) {
            DictGroup dictGroup = dictGroupOptional.get();
            DictGroupVO dictGroupVO = BeanUtil.toBean(dictGroup, DictGroupVO.class);
            if (StrUtil.equals(dictGroupVO.getType(), DictGroupTypeEnum.DICT.name())) {
                Pageable pageable = PageRequest.of(pageFO.getPageNumber() - 1, pageFO.getPageSize(), Sort.by(Sort.Direction.DESC, DictGroup.COL_ACTIVE, MysqlBase.CREATE_TIME));
                Page<Dict> dictList = StrUtil.isNotBlank(name)
                        ? dictDAO.findByDictGroupIdAndNameContainsOrderBySerialNumberAsc(dictGroup.getId(), name, pageable)
                        : dictDAO.findByDictGroupIdOrderBySerialNumberAsc(dictGroup.getId(), pageable);

                Dict first = dictDAO.findFirstByDictGroupIdOrderBySerialNumberDesc(dictGroup.getId());
                dictGroupVO.setSerialNumberMax(Objects.nonNull(first) ? first.getSerialNumber() : 0);

                PageVO<DictVO> dictListPage;
                if (Objects.isNull(dictList)) {
                    dictListPage = PageUtil.empty(pageFO);
                } else {
                    dictList.map(c -> BeanUtil.toBean(c, DictVO.class));
                    dictListPage = PageUtil.convert(dictList);
                }
                dictGroupVO.setDictList(dictListPage);
            }
            return dictGroupVO;
        }
        return null;
    }


    @Override
    @Transactional
    public void addDict(DictGroupAddFO addFO) {
        DictGroup byCode = dictGroupDAO.getByCode(addFO.getCode());
        if (ObjectUtil.isNotNull(byCode)) {
            throw new BusinessAssertException("字典名称已存在");
        }
        if (DictGroupTypeEnum.PARAMETER.name().equals(addFO.getType())) {
            BizAssert.isTrue(StrUtil.isNotBlank(addFO.getName()), "值不能为空");
            DictGroup group = new DictGroup();
            addFO.convertTo(group);
            group.setName(StrUtil.trimToEmpty(group.getName()));
            dictGroupDAO.save(group);
            if (group.getActive()) {
                redisHelper.strSet(RedisCacheKeys.getSystemConfigKey(group.getCode()), group.getName());
            }
        }
        if (DictGroupTypeEnum.DICT.name().equals(addFO.getType())) {
            DictGroup group = new DictGroup();
            addFO.convertTo(group);
            dictGroupDAO.save(group);
        }
    }

    @Override
    @Transactional
    public void batchAddDict(DictBatchAddFO addFO) {
        DictGroup group = dictGroupDAO.findById(addFO.getId()).orElseThrow(() -> new BusinessAssertException("枚举不存在"));

        if (CollUtil.isEmpty(addFO.getDictList())) {
            throw new BusinessAssertException("枚举值不能空");
        }

        List<String> codeList = addFO.getDictList().stream().map(DictAddFO::getCode).collect(Collectors.toList());
        if (new HashSet(codeList).size() != codeList.size()) {
            throw new BusinessAssertException("枚举值不能重复");
        }

        List<Dict> dictList = new ArrayList<>();
        for (DictAddFO dictAddFO : addFO.getDictList()) {
            List<Dict> dictListByCode = dictDAO.findByDictGroupIdAndCode(group.getId(), dictAddFO.getCode());
            if (CollectionUtil.isNotEmpty(dictListByCode)) {
                throw new BusinessAssertException(dictAddFO.getCode() + ",枚举值编码已存在");
            }

            Dict dict = new Dict();
            dictAddFO.convertTo(dict);
            dict.setDictGroupId(group.getId());
            dictList.add(dict);
        }
        dictDAO.saveAll(dictList);
    }

    @Override
    @Transactional
    public void modifyDict(DictGroupModifyFO modifyFO) {
        DictGroup group = dictGroupDAO.findById(modifyFO.getId()).orElseThrow(() -> new BusinessAssertException("未查询到字典信息"));

        if (DictGroupTypeEnum.PARAMETER.name().equals(group.getType())) {
            modifyFO.convertTo(group);
            group.setUpdateTime(LocalDateTime.now());
            dictGroupDAO.save(group);
            if (group.getActive()) {
                redisHelper.delKey(RedisCacheKeys.getSystemConfigKey(group.getCode()));
                redisHelper.strSet(RedisCacheKeys.getSystemConfigKey(group.getCode()), group.getName());
            } else {
                redisHelper.delKey(RedisCacheKeys.getSystemConfigKey(group.getCode()));
            }
        }
        if (DictGroupTypeEnum.DICT.name().equals(group.getType())) {
            if (CollUtil.isEmpty(modifyFO.getDictList())) {
                throw new BusinessAssertException("枚举值不能空");
            }

            modifyFO.convertTo(group);
            group.setUpdateTime(LocalDateTime.now());
            dictGroupDAO.save(group);
            List<Dict> dictList = new ArrayList<>();

            for (DictModifyFO dictModifyFO : modifyFO.getDictList().stream().filter(e -> StrUtil.isNotBlank(e.getId())).collect(Collectors.toList())) {
                Dict dict = dictDAO.findById(dictModifyFO.getId()).orElseThrow(() -> new BusinessAssertException("枚举不存在"));
                if (!StrUtil.equals(dictModifyFO.getCode(), dict.getCode())) {
                    throw new BusinessAssertException(dictModifyFO.getCode() + ",枚举编码不能修改");
                }
                dictModifyFO.convertTo(dict);
                dictList.add(dict);
            }
            dictDAO.saveAll(dictList);
            redisHelper.delKey(RedisCacheKeys.getSystemDictKey(group.getCode()));
        }
    }

    @Transactional
    @Override
    public void switchAvailable(String id) {
        dictGroupDAO.findById(id).ifPresent(group -> {
            group.setActive(!group.getActive());
            dictGroupDAO.save(group);
            if (DictGroupTypeEnum.PARAMETER.name().equals(group.getType())) {
                Set<String> keys = redisHelper.keys(RedisCacheKeys.getSystemConfigKey("*"));
                redisHelper.delKeys(keys);
            }
            if (DictGroupTypeEnum.DICT.name().equals(group.getType())) {
                if (group.getActive()) {
                    redisHelper.delKey(RedisCacheKeys.getSystemDictKey(group.getCode()));
                    List<Dict> dictListActive = dictDAO.findByDictGroupIdAndActiveIsTrueOrderBySerialNumberAsc(group.getId());
                    if (CollUtil.isNotEmpty(dictListActive)) {
                        redisHelper.objectSet(RedisCacheKeys.getSystemDictKey(group.getCode()), dictListActive, 365, TimeUnit.DAYS);
                    }
                } else {
                    redisHelper.delKey(RedisCacheKeys.getSystemDictKey(group.getCode()));
                }
            }
        });

    }

    @Transactional
    @Override
    public void switchDictItemAvailable(String id) {
        // 查询出字典
        Dict dict = dictDAO.findById(id).orElseThrow(() -> new BusinessAssertException("未查询到字典信息"));
        DictGroup group = dictGroupDAO.findById(dict.getDictGroupId()).orElseThrow(() -> new BusinessAssertException("未查询到字典信息"));

        // 更新字典
        dict.setActive(!dict.getActive());
        dictDAO.save(dict);

        // 删除旧的缓存
        redisHelper.delKey(RedisCacheKeys.getSystemDictKey(group.getCode()));
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void deleteDict(String id) {
        DictGroup dg = dictGroupDAO.findById(id).orElseThrow(() -> new BusinessAssertException("未查询到字典信息"));
        if (dg.getType().equals(DictGroupTypeEnum.DICT.name())) {
            dictDAO.deleteAllByDictGroupId(dg.getId());
        }
        dictGroupDAO.delete(dg);

        // 删除旧的缓存
        redisHelper.delKey(RedisCacheKeys.getSystemDictKey(dg.getCode()));
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void deleteDictItem(String id) {
        Dict dict = dictDAO.findById(id).orElseThrow(() -> new BusinessAssertException("未查询到字典信息"));
        DictGroup dg = dictGroupDAO.findById(dict.getDictGroupId()).orElseThrow(() -> new BusinessAssertException("未查询到字典信息"));
        dictDAO.deleteById(dict.getId());
        // 删除旧的缓存
        redisHelper.delKey(RedisCacheKeys.getSystemDictKey(dg.getCode()));
    }

    @Override
    public Integer getSysOrDefault(String sysValKey, Integer defaultVal) {
        String sysVal = getOneParamValue(sysValKey);
        if (NumberUtil.isInteger(sysVal)) {
            return Integer.parseInt(sysVal);
        }
        return defaultVal;
    }
}
