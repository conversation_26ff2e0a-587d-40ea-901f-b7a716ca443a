package com.endovas.cps.service;

import com.endovas.cps.entity.DictGroup;
import com.endovas.cps.pojo.fo.DictBatchAddFO;
import com.endovas.cps.pojo.fo.DictGroupAddFO;
import com.endovas.cps.pojo.fo.DictGroupModifyFO;
import com.endovas.cps.pojo.fo.DictGroupSearchFO;
import com.endovas.cps.pojo.vo.DictGroupListVO;
import com.endovas.cps.pojo.vo.DictGroupVO;
import com.endovas.cps.pojo.vo.DictVO;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 下午12:48
 */
public interface DictService {
    List<DictVO> listDict(String groupCode);

    List<DictVO> listDict(DictGroup dictGroup, Boolean needActive);

    String getOneParamValue(String key);

    Map<String, List<DictVO>> listAllActiveDict();

    Map<String, List<DictVO>> listAllDict(Boolean needActive);

    List<DictGroupVO> listDictGroup(String category);

    List<DictGroupVO> listActiveDictGroup(String category);

    Map<String, String> listActiveConfig();

    // 字典列表
    PageVO<DictGroupListVO> pageBySearchFO(DictGroupSearchFO searchFO, PageFO pageFO);

    DictGroupVO detail(String id, String name, PageFO pageFO);

    //  字典添加
    void addDict(DictGroupAddFO addFO);

    void batchAddDict(DictBatchAddFO addFO);

    //  字典更新
    void modifyDict(DictGroupModifyFO modifyFO);

    void switchAvailable(String id);

    // 删除字典
    void deleteDict(String id);

    void switchDictItemAvailable(String id);

    void deleteDictItem(String id);

    Integer getSysOrDefault(String sysValKey, Integer defaultVal);


}
