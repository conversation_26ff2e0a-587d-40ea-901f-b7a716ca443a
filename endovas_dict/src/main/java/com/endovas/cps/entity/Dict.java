package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 上午11:59
 */
@Entity
@Setter
@Getter
@Accessors(chain = true)
@Table(name = "t_dict")
@TableName("t_dict")
public class Dict extends MysqlBase {
    private String code;
    private String name;
    private Integer serialNumber;//序号
    private String dictGroupId;//组ID
    private Boolean active;//是否可用
    private String remark;//备注
    private String remark1;//扩展字段1
    private String remark2;//扩展字段2
    private String remark3;//扩展字段3
    private String parentId;//父级ID


    public static final String COL_SERIALNUMBER = "serial_number";
}
