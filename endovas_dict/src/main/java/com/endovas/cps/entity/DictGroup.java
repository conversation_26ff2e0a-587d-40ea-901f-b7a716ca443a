package com.endovas.cps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;



/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/16
 * Time: 上午11:59
 */
@Entity
@Setter
@Getter
@Accessors(chain = true)
@Table(name = "t_dict_group")
@TableName("t_dict_group")
public class DictGroup extends MysqlBase {
    private String code;
    private String name;
    private String type;
    private Boolean active;
    private String remark;

    public static final String COL_CODE = "code";
    public static final String COL_NAME = "name";
    public static final String COL_TYPE = "type";
    public static final String COL_ACTIVE = "active";
    public static final String COL_REMARK = "remark";
}
